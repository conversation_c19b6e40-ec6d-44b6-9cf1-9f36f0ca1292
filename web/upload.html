<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上傳服務</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #333;
            text-align: center;
        }

        .upload-container {
            border: 2px dashed #ccc;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .file-type {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background-color: #f5f5f5;
        }

        .file-list {
            margin: 20px 0;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .file-item:hover {
            background-color: #f9f9f9;
        }

        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background-color: #45a049;
        }

        .delete-btn {
            background-color: #f44336;
        }

        .delete-btn:hover {
            background-color: #d32f2f;
        }

        select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>
</head>

<body>
    <h1>文件上傳服務</h1>

    <div class="file-type">
        <label for="fileType">選擇文件類型：</label>
        <select id="fileType">
            <option value="music">音樂</option>
            <option value="video">視頻</option>
            <option value="pdf">PDF</option>
        </select>
    </div>

    <div class="upload-container" id="dropArea">
        <p>拖放文件到此處或點擊選擇文件</p>
        <input type="file" id="fileInput" multiple style="display: none;">
        <button id="selectButton">選擇文件</button>
    </div>

    <div id="uploadProgress"></div>

    <div class="file-list">
        <h2>文件列表</h2>
        <div id="fileList"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const dropArea = document.getElementById('dropArea');
            const fileInput = document.getElementById('fileInput');
            const selectButton = document.getElementById('selectButton');
            const fileTypeSelect = document.getElementById('fileType');
            const fileListElement = document.getElementById('fileList');
            const uploadProgress = document.getElementById('uploadProgress');

            // 選擇文件按鈕
            selectButton.addEventListener('click', function () {
                fileInput.click();
            });

            // 文件選擇事件
            fileInput.addEventListener('change', function () {
                handleFiles(this.files);
            });

            // 拖放事件
            dropArea.addEventListener('dragover', function (e) {
                e.preventDefault();
                dropArea.style.backgroundColor = '#f0f0f0';
            });

            dropArea.addEventListener('dragleave', function () {
                dropArea.style.backgroundColor = '';
            });

            dropArea.addEventListener('drop', function (e) {
                e.preventDefault();
                dropArea.style.backgroundColor = '';
                handleFiles(e.dataTransfer.files);
            });

            // 處理文件上傳
            function handleFiles(files) {
                if (files.length === 0) return;

                const fileType = fileTypeSelect.value;
                const formData = new FormData();

                for (let i = 0; i < files.length; i++) {
                    formData.append('file', files[i]);
                }

                uploadProgress.innerHTML = '<p>上傳中...</p>';

                fetch(`/api/upload/${fileType}`, {
                    method: 'POST',
                    body: formData
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            uploadProgress.innerHTML = `<p>上傳成功！已上傳 ${data.files.length} 個文件</p>`;
                            loadFileList(fileType);
                        } else {
                            uploadProgress.innerHTML = `<p>上傳失敗：${data.error}</p>`;
                        }
                    })
                    .catch(error => {
                        uploadProgress.innerHTML = `<p>上傳錯誤：${error.message}</p>`;
                    });
            }

            // 加載文件列表
            function loadFileList(fileType) {
                fetch(`/api/files/${fileType}`)
                    .then(response => response.json())
                    .then(data => {
                        fileListElement.innerHTML = '';

                        if (data.files && data.files.length > 0) {
                            data.files.forEach(file => {
                                const fileItem = document.createElement('div');
                                fileItem.className = 'file-item';

                                const fileSize = formatFileSize(file.size);
                                const fileDate = new Date(file.modified).toLocaleString();

                                fileItem.innerHTML = `
                                <div>
                                    <strong>${file.name}</strong>
                                    <div>${fileSize} - ${fileDate}</div>
                                </div>
                                <button class="delete-btn" data-filename="${file.name}">刪除</button>
                            `;

                                fileListElement.appendChild(fileItem);
                            });

                            // 添加刪除事件
                            document.querySelectorAll('.delete-btn').forEach(button => {
                                button.addEventListener('click', function () {
                                    const filename = this.getAttribute('data-filename');
                                    deleteFile(fileType, filename);
                                });
                            });
                        } else {
                            fileListElement.innerHTML = '<p>沒有找到文件</p>';
                        }
                    })
                    .catch(error => {
                        fileListElement.innerHTML = `<p>加載文件列表錯誤：${error.message}</p>`;
                    });
            }

            // 刪除文件
            function deleteFile(fileType, filename) {
                if (confirm(`確定要刪除文件 "${filename}" 嗎？`)) {
                    fetch(`/api/files/${fileType}/${filename}`, {
                        method: 'DELETE'
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                loadFileList(fileType);
                            } else {
                                alert(`刪除失敗：${data.error}`);
                            }
                        })
                        .catch(error => {
                            alert(`刪除錯誤：${error.message}`);
                        });
                }
            }

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';

                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));

                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 初始加載文件列表
            loadFileList(fileTypeSelect.value);

            // 文件類型變更時重新加載列表
            fileTypeSelect.addEventListener('change', function () {
                loadFileList(this.value);
            });
        });
    </script>
</body>

</html>