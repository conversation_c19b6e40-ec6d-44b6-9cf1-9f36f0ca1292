<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ManLeBox - 文件上傳系統</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK TC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
            position: relative;
            z-index: 1;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.2em;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .upload-section {
            margin-bottom: 35px;
        }

        .section-title {
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .file-type-select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 1.1em;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .file-type-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .upload-area {
            border: 3px dashed #cbd5e0;
            border-radius: 16px;
            padding: 60px 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 25px;
            background: linear-gradient(145deg, #f8fafc, #f1f5f9);
            position: relative;
            overflow: hidden;
            will-change: transform, box-shadow;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: linear-gradient(145deg, #f0f4ff, #e6f0ff);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
        }

        .upload-area:hover::before {
            opacity: 1;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background: linear-gradient(145deg, #e6f0ff, #dbeafe);
            transform: scale(1.02);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }

        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .upload-text {
            font-size: 1.2em;
            color: #4a5568;
            margin-bottom: 20px;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }

        .upload-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            z-index: 1;
            will-change: transform, box-shadow;
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .upload-button:active {
            transform: translateY(0);
        }

        .progress-area {
            margin-top: 25px;
            display: none;
        }

        .progress-area.show {
            display: block;
            animation: fadeIn 0.3s ease;
            will-change: opacity, transform;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .progress-info {
            background: linear-gradient(145deg, #f8fafc, #f1f5f9);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .progress-title {
            font-weight: 600;
            color: #2d3748;
            font-size: 1.1em;
        }

        .progress-percentage {
            font-weight: 700;
            color: #667eea;
            font-size: 1.2em;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background-color: #e2e8f0;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 15px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 6px;
            transition: width 0.3s ease;
            width: 0%;
            position: relative;
            overflow: hidden;
            will-change: width;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background-image: linear-gradient(-45deg,
                    rgba(255, 255, 255, .2) 25%,
                    transparent 25%,
                    transparent 50%,
                    rgba(255, 255, 255, .2) 50%,
                    rgba(255, 255, 255, .2) 75%,
                    transparent 75%,
                    transparent);
            background-size: 30px 30px;
            animation: move 2s linear infinite;
            will-change: background-position;
        }

        @keyframes move {
            0% {
                background-position: 0 0;
            }

            100% {
                background-position: 30px 30px;
            }
        }

        .progress-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.95em;
            color: #4a5568;
        }

        .file-size-info {
            font-weight: 500;
        }

        .upload-speed {
            color: #667eea;
            font-weight: 500;
        }

        .status-message {
            padding: 15px 20px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-success {
            background: linear-gradient(145deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: linear-gradient(145deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .file-list {
            margin-top: 35px;
        }

        .file-list-title {
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-list-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px 20px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            margin-bottom: 12px;
            background: linear-gradient(145deg, #ffffff, #f8fafc);
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            will-change: transform, box-shadow;
        }

        .file-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border-color: #cbd5e0;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 6px;
            font-size: 1.05em;
            word-break: break-all;
        }

        .file-meta {
            font-size: 0.9em;
            color: #718096;
            display: flex;
            gap: 15px;
        }

        .delete-button {
            background: linear-gradient(135deg, #e53e3e, #c53030);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
            will-change: transform, box-shadow;
        }

        .delete-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);
            background: linear-gradient(135deg, #c53030, #9c2626);
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #718096;
            font-size: 1.1em;
        }

        .empty-state-icon {
            font-size: 3em;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* PDF轉換功能樣式 */
        .pdf-converter-area {
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 25px;
            background: linear-gradient(145deg, #f8fafc, #f1f5f9);
            margin-top: 15px;
        }

        .converter-description {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.05em;
            text-align: center;
            padding: 15px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .image-preview-area {
            margin-top: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
        }

        .preview-title {
            font-weight: 600;
            color: #2d3748;
            font-size: 1.1em;
        }

        .clear-images-button {
            background: linear-gradient(135deg, #e53e3e, #c53030);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
        }

        .clear-images-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);
        }

        .image-preview-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .image-preview-item {
            position: relative;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            background: white;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .image-preview-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .image-preview-item img {
            width: 100%;
            height: 100px;
            object-fit: cover;
            display: block;
        }

        .image-preview-item .image-name {
            padding: 8px;
            font-size: 0.8em;
            color: #4a5568;
            background: #f8fafc;
            text-align: center;
            word-break: break-all;
            line-height: 1.2;
        }

        .image-preview-item .remove-image {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(229, 62, 62, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .image-preview-item .remove-image:hover {
            background: rgba(229, 62, 62, 1);
            transform: scale(1.1);
        }

        .converter-controls {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .convert-button {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 12px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
        }

        .convert-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }

        .convert-button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .pdf-progress-area {
            margin-top: 25px;
            animation: fadeIn 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 16px;
            }

            .content {
                padding: 25px;
            }

            .upload-area {
                padding: 40px 20px;
            }

            .file-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .delete-button {
                align-self: flex-end;
            }

            .progress-details {
                flex-direction: column;
                gap: 5px;
            }

            .image-preview-list {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 10px;
            }

            .pdf-converter-area {
                padding: 20px;
            }

            .preview-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>📱 ManLeBox</h1>
            <p>專業文件上傳系統 - 支持大文件與中文文件名</p>
        </div>

        <div class="content">
            <div class="upload-section">
                <div class="section-title">選擇文件類型</div>
                <select id="fileType" class="file-type-select" aria-label="選擇文件類型">
                    <option value="music">🎵 音樂文件</option>
                    <option value="video">🎬 視頻文件</option>
                    <option value="pdf">📄 PDF文檔</option>
                </select>
            </div>

            <!-- 圖片轉PDF功能區塊 -->
            <div class="upload-section">
                <div class="section-title">圖片轉PDF</div>
                <div class="pdf-converter-area">
                    <div class="converter-description">
                        選擇多張圖片文件，將它們合併轉換為一個PDF文檔
                    </div>
                    <div class="upload-area" id="imageUploadArea">
                        <div class="upload-icon">🖼️</div>
                        <div class="upload-text">拖放圖片到此處或點擊選擇圖片</div>
                        <button type="button" class="upload-button" id="selectImageButton">選擇圖片</button>
                        <input type="file" id="imageInput" multiple accept="image/*" style="display: none;"
                            aria-label="選擇要轉換的圖片">
                    </div>

                    <!-- 圖片預覽區域 -->
                    <div class="image-preview-area" id="imagePreviewArea" style="display: none;">
                        <div class="preview-header">
                            <span class="preview-title">已選擇的圖片</span>
                            <button type="button" class="clear-images-button" id="clearImagesButton">清空</button>
                        </div>
                        <div class="image-preview-list" id="imagePreviewList"></div>
                        <div class="converter-controls">
                            <button type="button" class="convert-button" id="convertToPdfButton">轉換為PDF</button>
                        </div>
                    </div>

                    <!-- PDF轉換進度 -->
                    <div class="pdf-progress-area" id="pdfProgressArea" style="display: none;">
                        <div class="progress-info">
                            <div class="progress-header">
                                <div class="progress-title">PDF轉換進度</div>
                                <div class="progress-percentage" id="pdfProgressPercentage">0%</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="pdfProgressFill"></div>
                            </div>
                            <div class="progress-details">
                                <div class="file-size-info" id="pdfProgressInfo">準備轉換...</div>
                                <div class="upload-speed" id="pdfCurrentFile">等待中...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="upload-section">
                <div class="section-title">上傳文件</div>
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">拖放文件到此處或點擊選擇文件</div>
                    <button type="button" class="upload-button" id="selectButton">選擇文件</button>
                    <input type="file" id="fileInput" multiple style="display: none;" aria-label="選擇要上傳的文件">
                </div>
            </div>

            <div class="progress-area" id="progressArea">
                <div class="progress-info" id="progressInfo" style="display: none;">
                    <div class="progress-header">
                        <div class="progress-title">上傳進度</div>
                        <div class="progress-percentage" id="progressPercentage">0%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-details">
                        <div class="file-size-info" id="fileSizeInfo">準備上傳...</div>
                        <div class="upload-speed" id="uploadSpeed">計算中...</div>
                    </div>
                </div>
                <div id="statusMessages"></div>
            </div>

            <div class="upload-section">
                <div class="section-title">文件列表</div>
                <div id="fileList">
                    <div class="empty-state">
                        <div class="empty-state-icon">📂</div>
                        <div>暫無文件</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入jsPDF庫用於PDF生成 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script>
        // 全局變量
        let uploadArea, fileInput, selectButton, fileTypeSelect;
        let progressInfo, progressFill, progressPercentage, fileSizeInfo, uploadSpeed;
        let statusMessages, fileList;
        let currentUpload = null;
        let startTime = 0;

        // PDF轉換相關變量
        let imageUploadArea, imageInput, selectImageButton;
        let imagePreviewArea, imagePreviewList, clearImagesButton, convertToPdfButton;
        let pdfProgressArea, pdfProgressFill, pdfProgressPercentage, pdfProgressInfo, pdfCurrentFile;
        let selectedImages = [];
        let isConverting = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('Initializing ManLeBox upload system...');

            // 獲取DOM元素
            uploadArea = document.getElementById('uploadArea');
            fileInput = document.getElementById('fileInput');
            selectButton = document.getElementById('selectButton');
            fileTypeSelect = document.getElementById('fileType');
            progressInfo = document.getElementById('progressInfo');
            progressFill = document.getElementById('progressFill');
            progressPercentage = document.getElementById('progressPercentage');
            fileSizeInfo = document.getElementById('fileSizeInfo');
            uploadSpeed = document.getElementById('uploadSpeed');
            statusMessages = document.getElementById('statusMessages');
            fileList = document.getElementById('fileList');

            // 獲取PDF轉換相關元素
            imageUploadArea = document.getElementById('imageUploadArea');
            imageInput = document.getElementById('imageInput');
            selectImageButton = document.getElementById('selectImageButton');
            imagePreviewArea = document.getElementById('imagePreviewArea');
            imagePreviewList = document.getElementById('imagePreviewList');
            clearImagesButton = document.getElementById('clearImagesButton');
            convertToPdfButton = document.getElementById('convertToPdfButton');
            pdfProgressArea = document.getElementById('pdfProgressArea');
            pdfProgressFill = document.getElementById('pdfProgressFill');
            pdfProgressPercentage = document.getElementById('pdfProgressPercentage');
            pdfProgressInfo = document.getElementById('pdfProgressInfo');
            pdfCurrentFile = document.getElementById('pdfCurrentFile');

            // 檢查必要元素
            if (!uploadArea || !fileInput || !selectButton) {
                console.error('Required elements not found!');
                return;
            }

            setupEvents();
            setupImageConverterEvents();
            loadFileList();

            console.log('Upload system ready!');
        });

        // 設置事件監聽器
        function setupEvents() {
            // 選擇文件按鈕
            selectButton.addEventListener('click', function (e) {
                e.preventDefault();
                console.log('Select button clicked');
                fileInput.click();
            });

            // 上傳區域點擊
            uploadArea.addEventListener('click', function (e) {
                if (e.target !== selectButton) {
                    console.log('Upload area clicked');
                    fileInput.click();
                }
            });

            // 文件選擇
            fileInput.addEventListener('change', function (e) {
                if (e.target.files.length > 0) {
                    console.log('Files selected:', e.target.files.length);
                    uploadFiles(e.target.files);
                }
            });

            // 拖放事件
            uploadArea.addEventListener('dragover', function (e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function (e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function (e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                if (e.dataTransfer.files.length > 0) {
                    console.log('Files dropped:', e.dataTransfer.files.length);
                    uploadFiles(e.dataTransfer.files);
                }
            });

            // 文件類型變化
            if (fileTypeSelect) {
                fileTypeSelect.addEventListener('change', function () {
                    loadFileList();
                });
            }
        }

        // 上傳文件函數
        function uploadFiles(files) {
            if (!files || files.length === 0) return;

            console.log('Starting upload of', files.length, 'files');

            const fileType = fileTypeSelect ? fileTypeSelect.value : 'music';
            const formData = new FormData();

            // 計算總大小並顯示文件信息
            let totalSize = 0;
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                formData.append('file', file);
                totalSize += file.size;

                const sizeMB = (file.size / (1024 * 1024)).toFixed(1);
                console.log('File ' + (i + 1) + ':', file.name, '(' + sizeMB + ' MB)');
            }

            // 顯示進度
            showProgress();
            updateProgress(0, formatFileSize(0), formatFileSize(totalSize), '0 B/s');

            // 創建上傳請求
            const xhr = new XMLHttpRequest();
            currentUpload = xhr;
            startTime = Date.now();

            // 上傳進度
            xhr.upload.addEventListener('progress', function (e) {
                if (e.lengthComputable) {
                    const percent = (e.loaded / e.total) * 100;
                    const elapsed = (Date.now() - startTime) / 1000;
                    const speed = elapsed > 0 ? e.loaded / elapsed : 0;

                    updateProgress(
                        percent,
                        formatFileSize(e.loaded),
                        formatFileSize(e.total),
                        formatSpeed(speed)
                    );
                }
            });

            // 上傳完成
            xhr.addEventListener('load', function () {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const data = JSON.parse(xhr.responseText);
                        if (data.success) {
                            showStatus('上傳成功！', 'success');
                            loadFileList();
                        } else {
                            showStatus('上傳失敗: ' + (data.error || '未知錯誤'), 'error');
                        }
                    } catch (e) {
                        showStatus('響應解析錯誤', 'error');
                    }
                } else {
                    showStatus('上傳失敗 (狀態碼: ' + xhr.status + ')', 'error');
                }
                hideProgress();
                currentUpload = null;
            });

            // 上傳錯誤
            xhr.addEventListener('error', function () {
                showStatus('網絡錯誤', 'error');
                hideProgress();
                currentUpload = null;
            });

            // 發送請求
            xhr.open('POST', '/api/upload/' + fileType);
            xhr.timeout = 60 * 60 * 1000; // 60分鐘超時
            xhr.send(formData);
        }

        // 顯示進度
        function showProgress() {
            if (progressInfo) {
                progressInfo.style.display = 'block';
                document.getElementById('progressArea').classList.add('show');
            }
        }

        // 更新進度
        function updateProgress(percent, loaded, total, speed) {
            if (progressFill) {
                progressFill.style.width = percent + '%';
            }
            if (progressPercentage) {
                progressPercentage.textContent = percent.toFixed(1) + '%';
            }
            if (fileSizeInfo) {
                fileSizeInfo.textContent = loaded + ' / ' + total;
            }
            if (uploadSpeed) {
                uploadSpeed.textContent = speed;
            }
        }

        // 隱藏進度
        function hideProgress() {
            if (progressInfo) {
                progressInfo.style.display = 'none';
                document.getElementById('progressArea').classList.remove('show');
            }
        }

        // 顯示狀態消息
        function showStatus(message, type) {
            if (statusMessages) {
                const statusDiv = document.createElement('div');
                statusDiv.className = 'status-message status-' + type;
                statusDiv.innerHTML = '<span>' + (type === 'success' ? '✅' : '❌') + '</span>' + message;
                statusMessages.appendChild(statusDiv);

                setTimeout(function () {
                    if (statusDiv.parentNode) {
                        statusDiv.parentNode.removeChild(statusDiv);
                    }
                }, 5000);
            }
        }

        // 加載文件列表
        function loadFileList() {
            const fileType = fileTypeSelect ? fileTypeSelect.value : 'music';

            fetch('/api/files/' + fileType)
                .then(response => response.json())
                .then(data => {
                    displayFileList(data.files || [], fileType);
                })
                .catch(error => {
                    console.error('Failed to load file list:', error);
                    displayFileList([], fileType);
                });
        }

        // 顯示文件列表
        function displayFileList(files, fileType) {
            if (!fileList) return;

            fileList.innerHTML = '';

            if (files.length === 0) {
                fileList.innerHTML =
                    '<div class="empty-state">' +
                    '<div class="empty-state-icon">📂</div>' +
                    '<div>暫無文件</div>' +
                    '</div>';
                return;
            }

            files.forEach(function (file) {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';

                const fileSize = formatFileSize(file.size);
                const fileDate = new Date(file.modified).toLocaleString('zh-TW');

                fileItem.innerHTML =
                    '<div class="file-info">' +
                    '<div class="file-name">' + file.name + '</div>' +
                    '<div class="file-meta">' +
                    '<span>' + fileSize + '</span>' +
                    '<span>' + fileDate + '</span>' +
                    '</div>' +
                    '</div>' +
                    '<button type="button" class="delete-button" onclick="deleteFile(\'' + fileType + '\', \'' + encodeURIComponent(file.name) + '\')">刪除</button>';

                fileList.appendChild(fileItem);
            });
        }

        // 刪除文件
        function deleteFile(fileType, encodedFilename) {
            const filename = decodeURIComponent(encodedFilename);
            if (!confirm('確定要刪除文件 "' + filename + '" 嗎？')) {
                return;
            }

            fetch('/api/files/' + fileType + '/' + encodedFilename, {
                method: 'DELETE'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showStatus('文件已刪除', 'success');
                        loadFileList();
                    } else {
                        showStatus('刪除失敗: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    showStatus('刪除錯誤: ' + error.message, 'error');
                });
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 格式化上傳速度
        function formatSpeed(bytesPerSecond) {
            if (bytesPerSecond === 0) return '0 B/s';
            const k = 1024;
            const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
            const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));
            return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 設置圖片轉PDF事件監聽器
        function setupImageConverterEvents() {
            if (!imageUploadArea || !imageInput || !selectImageButton) {
                console.log('Image converter elements not found, skipping setup');
                return;
            }

            // 選擇圖片按鈕
            selectImageButton.addEventListener('click', function (e) {
                e.preventDefault();
                console.log('Select image button clicked');
                imageInput.click();
            });

            // 圖片上傳區域點擊
            imageUploadArea.addEventListener('click', function (e) {
                if (e.target !== selectImageButton) {
                    console.log('Image upload area clicked');
                    imageInput.click();
                }
            });

            // 圖片選擇
            imageInput.addEventListener('change', function (e) {
                if (e.target.files.length > 0) {
                    console.log('Images selected:', e.target.files.length);
                    addImagesToPreview(e.target.files);
                }
            });

            // 圖片拖放事件
            imageUploadArea.addEventListener('dragover', function (e) {
                e.preventDefault();
                imageUploadArea.classList.add('dragover');
            });

            imageUploadArea.addEventListener('dragleave', function (e) {
                e.preventDefault();
                imageUploadArea.classList.remove('dragover');
            });

            imageUploadArea.addEventListener('drop', function (e) {
                e.preventDefault();
                imageUploadArea.classList.remove('dragover');
                if (e.dataTransfer.files.length > 0) {
                    console.log('Images dropped:', e.dataTransfer.files.length);
                    addImagesToPreview(e.dataTransfer.files);
                }
            });

            // 清空圖片按鈕
            if (clearImagesButton) {
                clearImagesButton.addEventListener('click', function () {
                    clearSelectedImages();
                });
            }

            // 轉換為PDF按鈕
            if (convertToPdfButton) {
                convertToPdfButton.addEventListener('click', function () {
                    if (selectedImages.length > 0 && !isConverting) {
                        convertImagesToPdf();
                    }
                });
            }
        }

        // 添加圖片到預覽區域
        function addImagesToPreview(files) {
            const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));

            if (imageFiles.length === 0) {
                showStatus('請選擇圖片文件', 'error');
                return;
            }

            imageFiles.forEach(file => {
                if (!selectedImages.find(img => img.name === file.name && img.size === file.size)) {
                    selectedImages.push(file);
                }
            });

            updateImagePreview();
            imageInput.value = ''; // 清空input以允許重複選擇同一文件
        }

        // 更新圖片預覽
        function updateImagePreview() {
            if (!imagePreviewList || !imagePreviewArea) return;

            if (selectedImages.length === 0) {
                imagePreviewArea.style.display = 'none';
                return;
            }

            imagePreviewArea.style.display = 'block';
            imagePreviewList.innerHTML = '';

            selectedImages.forEach((file, index) => {
                const previewItem = document.createElement('div');
                previewItem.className = 'image-preview-item';

                const img = document.createElement('img');
                const reader = new FileReader();
                reader.onload = function (e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);

                const fileName = document.createElement('div');
                fileName.className = 'image-name';
                fileName.textContent = file.name;

                const removeButton = document.createElement('button');
                removeButton.className = 'remove-image';
                removeButton.innerHTML = '×';
                removeButton.onclick = function () {
                    removeImageFromPreview(index);
                };

                previewItem.appendChild(img);
                previewItem.appendChild(fileName);
                previewItem.appendChild(removeButton);
                imagePreviewList.appendChild(previewItem);
            });

            // 更新轉換按鈕狀態
            if (convertToPdfButton) {
                convertToPdfButton.disabled = selectedImages.length === 0 || isConverting;
            }
        }

        // 從預覽中移除圖片
        function removeImageFromPreview(index) {
            selectedImages.splice(index, 1);
            updateImagePreview();
        }

        // 清空選擇的圖片
        function clearSelectedImages() {
            selectedImages = [];
            updateImagePreview();
        }

        // 轉換圖片為PDF
        async function convertImagesToPdf() {
            if (selectedImages.length === 0) {
                showStatus('請先選擇圖片', 'error');
                return;
            }

            if (typeof window.jsPDF === 'undefined') {
                showStatus('PDF庫加載失敗，請刷新頁面重試', 'error');
                return;
            }

            isConverting = true;
            showPdfProgress();
            updatePdfProgress(0, '開始轉換...', '準備中...');

            try {
                const { jsPDF } = window.jsPDF;
                const pdf = new jsPDF();
                let isFirstPage = true;

                for (let i = 0; i < selectedImages.length; i++) {
                    const file = selectedImages[i];
                    const progress = ((i + 1) / selectedImages.length) * 100;

                    updatePdfProgress(
                        progress,
                        `正在處理: ${file.name}`,
                        `${i + 1}/${selectedImages.length} (${progress.toFixed(1)}%)`
                    );

                    try {
                        const imageData = await loadImageAsDataURL(file);
                        const img = await loadImage(imageData);

                        // 計算圖片在PDF中的尺寸
                        const pdfWidth = pdf.internal.pageSize.getWidth();
                        const pdfHeight = pdf.internal.pageSize.getHeight();
                        const margin = 10;
                        const maxWidth = pdfWidth - (margin * 2);
                        const maxHeight = pdfHeight - (margin * 2);

                        let imgWidth = img.width;
                        let imgHeight = img.height;

                        // 按比例縮放圖片以適應頁面
                        const widthRatio = maxWidth / imgWidth;
                        const heightRatio = maxHeight / imgHeight;
                        const ratio = Math.min(widthRatio, heightRatio);

                        imgWidth *= ratio;
                        imgHeight *= ratio;

                        // 居中放置
                        const x = (pdfWidth - imgWidth) / 2;
                        const y = (pdfHeight - imgHeight) / 2;

                        if (!isFirstPage) {
                            pdf.addPage();
                        }
                        isFirstPage = false;

                        pdf.addImage(imageData, 'JPEG', x, y, imgWidth, imgHeight);

                    } catch (error) {
                        console.error('處理圖片時出錯:', file.name, error);
                        showStatus(`處理圖片 ${file.name} 時出錯: ${error.message}`, 'error');
                    }
                }

                updatePdfProgress(100, '生成PDF文件...', '完成');

                // 生成PDF並下載
                const pdfBlob = pdf.output('blob');
                const fileName = `images_to_pdf_${new Date().getTime()}.pdf`;
                downloadBlob(pdfBlob, fileName);

                showStatus(`PDF轉換完成！已下載 ${fileName}`, 'success');

            } catch (error) {
                console.error('PDF轉換失敗:', error);
                showStatus('PDF轉換失敗: ' + error.message, 'error');
            } finally {
                isConverting = false;
                hidePdfProgress();
                updateImagePreview(); // 更新按鈕狀態
            }
        }

        // 加載圖片為DataURL
        function loadImageAsDataURL(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function (e) {
                    resolve(e.target.result);
                };
                reader.onerror = function (e) {
                    reject(new Error('讀取圖片失敗'));
                };
                reader.readAsDataURL(file);
            });
        }

        // 加載圖片對象
        function loadImage(src) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function () {
                    resolve(img);
                };
                img.onerror = function () {
                    reject(new Error('圖片加載失敗'));
                };
                img.src = src;
            });
        }

        // 下載Blob文件
        function downloadBlob(blob, fileName) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>

</html>