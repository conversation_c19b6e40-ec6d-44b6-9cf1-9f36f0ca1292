<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>漫畫上傳</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        /* 保持原有的样式不变 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        h1,
        h2 {
            color: #e67e22;
            margin-bottom: 20px;
            text-align: center;
        }

        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }

        .upload-form {
            margin: 20px 0;
            text-align: center;
        }

        .upload-btn {
            background-color: #e67e22;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }

        .upload-btn:hover {
            background-color: #d35400;
        }

        #fileList,
        #imageList {
            margin-top: 20px;
        }

        .file-item {
            background-color: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-name {
            flex-grow: 1;
            margin-right: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background-color: #eee;
            margin-top: 5px;
            border-radius: 2px;
            display: none;
        }

        .progress {
            width: 0%;
            height: 100%;
            background-color: #e67e22;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .error {
            color: #e74c3c;
            margin-top: 10px;
        }

        .success {
            color: #2ecc71;
            margin-top: 10px;
        }

        .file-size {
            color: #666;
            font-size: 0.9em;
            margin-left: 10px;
        }

        .tab-buttons {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .tab-btn {
            background-color: #f8f9fa;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            cursor: pointer;
            border-radius: 4px;
        }

        .tab-btn.active {
            background-color: #e67e22;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        #pdfFileName {
            padding: 8px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>漫畫文件處理</h1>

        <div class="tab-buttons">
            <button class="tab-btn active" onclick="showTab('upload')">上傳漫畫</button>
            <button class="tab-btn" onclick="showTab('convert')">圖片轉PDF</button>
        </div>

        <div id="uploadTab" class="tab-content active">
            <div class="upload-form">
                <input type="file" id="comicInput" multiple accept=".pdf" style="display: none;">
                <button onclick="document.getElementById('comicInput').click()" class="upload-btn">選擇漫畫文件</button>
                <button onclick="uploadFiles()" class="upload-btn">開始上傳</button>
                <div id="fileList"></div>
            </div>
        </div>

        <div id="convertTab" class="tab-content">
            <div class="upload-form">
                <input type="file" id="imageInput" multiple accept="image/*" style="display: none;">
                <button onclick="document.getElementById('imageInput').click()" class="upload-btn">選擇圖片文件</button>
                <input type="text" id="pdfFileName" placeholder="輸入PDF文件名">
                <button onclick="convertToPDF()" class="upload-btn">轉換為PDF</button>
                <div id="imageList"></div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let selectedImages = [];

        function showTab(tabName) {
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[onclick="showTab('${tabName}')"]`).classList.add('active');

            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}Tab`).classList.add('active');
        }

        // 清理文件名，保留中文字符
        function sanitizeFileName(fileName) {
            // 获取文件扩展名
            const lastDotIndex = fileName.lastIndexOf('.');
            const ext = lastDotIndex !== -1 ? fileName.slice(lastDotIndex) : '';
            const nameWithoutExt = lastDotIndex !== -1 ? fileName.slice(0, lastDotIndex) : fileName;

            // 只移除不安全的字符，保留中文和基本字符
            const sanitized = nameWithoutExt
                .replace(/[<>:"/\\|?*\x00-\x1F]/g, '') // 移除文件系统不允许的字符
                .replace(/\s+/g, '')                   // 将空格替换为下划线
                .trim();

            // 如果清理后的文件名为空，使用默认名称
            return (sanitized || 'file') + ext;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 更新漫画文件列表
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const div = document.createElement('div');
                div.className = 'file-item';

                const nameSpan = document.createElement('span');
                nameSpan.className = 'file-name';
                const sanitizedName = sanitizeFileName(file.name);
                nameSpan.textContent = sanitizedName;

                const sizeSpan = document.createElement('span');
                sizeSpan.className = 'file-size';
                sizeSpan.textContent = formatFileSize(file.size);

                const removeBtn = document.createElement('button');
                removeBtn.textContent = '移除';
                removeBtn.className = 'upload-btn';
                removeBtn.style.padding = '5px 10px';
                removeBtn.onclick = () => {
                    selectedFiles.splice(index, 1);
                    updateFileList();
                };

                const progressBar = document.createElement('div');
                progressBar.className = 'progress-bar';
                progressBar.innerHTML = '<div class="progress"></div>';

                const infoDiv = document.createElement('div');
                infoDiv.style.display = 'flex';
                infoDiv.style.alignItems = 'center';
                infoDiv.appendChild(nameSpan);
                infoDiv.appendChild(sizeSpan);

                div.appendChild(infoDiv);
                div.appendChild(removeBtn);
                div.appendChild(progressBar);
                fileList.appendChild(div);
            });
        }

        // 更新图片列表
        function updateImageList() {
            const imageList = document.getElementById('imageList');
            imageList.innerHTML = '';

            selectedImages.forEach((file, index) => {
                const div = document.createElement('div');
                div.className = 'file-item';

                const nameSpan = document.createElement('span');
                nameSpan.className = 'file-name';
                nameSpan.textContent = file.name;

                const sizeSpan = document.createElement('span');
                sizeSpan.className = 'file-size';
                sizeSpan.textContent = formatFileSize(file.size);

                const removeBtn = document.createElement('button');
                removeBtn.textContent = '移除';
                removeBtn.className = 'upload-btn';
                removeBtn.style.padding = '5px 10px';
                removeBtn.onclick = () => {
                    selectedImages.splice(index, 1);
                    updateImageList();
                };

                const infoDiv = document.createElement('div');
                infoDiv.style.display = 'flex';
                infoDiv.style.alignItems = 'center';
                infoDiv.appendChild(nameSpan);
                infoDiv.appendChild(sizeSpan);

                div.appendChild(infoDiv);
                div.appendChild(removeBtn);
                imageList.appendChild(div);
            });
        }

        // 漫画文件选择处理
        document.getElementById('comicInput').addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            const comicFiles = files.filter(file => {
                const ext = file.name.toLowerCase();
                return ext.endsWith('.pdf');
                /* ext.endsWith('.zip') || ext.endsWith('.cbz') ||
                     ext.endsWith('.cbr') || ext.endsWith('.pdf');*/
            });

            selectedFiles = [...selectedFiles, ...comicFiles];
            updateFileList();
            e.target.value = '';
        });

        // 图片文件选择处理
        document.getElementById('imageInput').addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            const imageFiles = files.filter(file => file.type.startsWith('image/'));

            selectedImages = [...selectedImages, ...imageFiles];
            updateImageList();
            e.target.value = '';
        });

        // 上传漫画文件
        async function uploadFiles() {
            if (selectedFiles.length === 0) {
                alert('請選擇要上傳的文件');
                return;
            }

            const fileItems = document.getElementsByClassName('file-item');

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const formData = new FormData();
                const sanitizedName = sanitizeFileName(file.name);

                const cleanFile = new File([file], sanitizedName, { type: file.type });
                formData.append('file', cleanFile);

                const progressBar = fileItems[i].querySelector('.progress-bar');
                const progress = progressBar.querySelector('.progress');
                progressBar.style.display = 'block';

                try {
                    const response = await fetch('/upload', {
                        method: 'POST',
                        body: formData
                    });

                    if (response.ok) {
                        progress.style.width = '100%';
                        progress.style.backgroundColor = '#2ecc71';
                    } else {
                        progress.style.backgroundColor = '#e74c3c';
                        throw new Error('Upload failed');
                    }
                } catch (error) {
                    progress.style.backgroundColor = '#e74c3c';
                    console.error('Error uploading file:', error);
                }
            }

            selectedFiles = [];
            setTimeout(() => {
                updateFileList();
                alert('上傳完成');
            }, 1000);
        }

        // 转换为PDF
        async function convertToPDF() {
            if (selectedImages.length === 0) {
                alert('請選擇要轉換的圖片');
                return;
            }

            let pdfFileName = document.getElementById('pdfFileName').value.trim();
            if (!pdfFileName) {
                pdfFileName = 'converted';
            }
            pdfFileName = sanitizeFileName(pdfFileName);
            if (!pdfFileName.toLowerCase().endsWith('.pdf')) {
                pdfFileName += '.pdf';
            }

            // 创建PDF文档
            const pdf = new jspdf.jsPDF();
            let currentPage = 0;

            for (const file of selectedImages) {
                try {
                    // 读取图片
                    const imageUrl = await new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onload = (e) => resolve(e.target.result);
                        reader.readAsDataURL(file);
                    });

                    // 获取图片尺寸
                    const img = new Image();
                    await new Promise((resolve, reject) => {
                        img.onload = resolve;
                        img.onerror = reject;
                        img.src = imageUrl;
                    });

                    // 如果不是第一页，添加新页
                    if (currentPage > 0) {
                        pdf.addPage();
                    }

                    // 调整图片大小以适应PDF页面
                    const pageWidth = pdf.internal.pageSize.getWidth();
                    const pageHeight = pdf.internal.pageSize.getHeight();
                    const imgRatio = img.width / img.height;
                    const pageRatio = pageWidth / pageHeight;

                    let finalWidth = pageWidth;
                    let finalHeight = pageWidth / imgRatio;

                    if (finalHeight > pageHeight) {
                        finalHeight = pageHeight;
                        finalWidth = pageHeight * imgRatio;
                    }

                    const x = (pageWidth - finalWidth) / 2;
                    const y = (pageHeight - finalHeight) / 2;

                    pdf.addImage(imageUrl, 'JPEG', x, y, finalWidth, finalHeight);
                    currentPage++;
                } catch (error) {
                    console.error('Error processing image:', error);
                }
            }

            // 保存PDF
            pdf.save(pdfFileName);

            // 清空选择的图片
            selectedImages = [];
            updateImageList();
            document.getElementById('pdfFileName').value = '';
            alert('PDF轉換完成');
        }
    </script>
</body>

</html>