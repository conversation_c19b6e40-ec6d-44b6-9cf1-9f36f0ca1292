<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ManLeBox - 圖片轉PDF</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK TC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.2em;
        }

        .back-button {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .content {
            padding: 40px;
        }

        .section-title {
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .upload-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .upload-area {
            border: 3px dashed #cbd5e0;
            border-radius: 16px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: linear-gradient(145deg, #f8fafc, #f1f5f9);
        }

        .upload-area:hover {
            border-color: #667eea;
            background: linear-gradient(145deg, #f0f4ff, #e6f0ff);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
        }

        .upload-area.dragover {
            border-color: #667eea;
            background: linear-gradient(145deg, #e6f0ff, #dbeafe);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.1em;
            color: #4a5568;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .upload-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .file-info {
            background: linear-gradient(145deg, #f8fafc, #f1f5f9);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
            display: none;
        }

        .file-count {
            font-size: 1.2em;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .file-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .clear-button {
            background: linear-gradient(135deg, #e53e3e, #c53030);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .clear-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);
        }

        .convert-button {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 12px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
        }

        .convert-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }

        .convert-button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-area {
            margin-top: 25px;
            display: none;
        }

        .progress-info {
            background: linear-gradient(145deg, #f8fafc, #f1f5f9);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .progress-title {
            font-weight: 600;
            color: #2d3748;
            font-size: 1.1em;
        }

        .progress-percentage {
            font-weight: 700;
            color: #667eea;
            font-size: 1.2em;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background-color: #e2e8f0;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 6px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.95em;
            color: #4a5568;
        }

        .status-message {
            padding: 15px 20px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-success {
            background: linear-gradient(145deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: linear-gradient(145deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .upload-options {
                grid-template-columns: 1fr;
            }

            .content {
                padding: 25px;
            }

            .file-actions {
                flex-direction: column;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <button class="back-button" onclick="location.href='index.html'">← 返回</button>
            <h1>🖼️ 圖片轉PDF</h1>
            <p>將圖片文件轉換為PDF文檔</p>
        </div>

        <div class="content">
            <div class="section-title">選擇圖片</div>
            <div class="upload-options">
                <div class="upload-area" id="fileUploadArea">
                    <div class="upload-icon">📄</div>
                    <div class="upload-text">選擇圖片文件</div>
                    <button type="button" class="upload-button" id="selectFilesButton">選擇文件</button>
                    <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                </div>

                <div class="upload-area" id="folderUploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">選擇圖片文件夾</div>
                    <button type="button" class="upload-button" id="selectFolderButton">選擇文件夾</button>
                    <input type="file" id="folderInput" webkitdirectory multiple accept="image/*"
                        style="display: none;">
                </div>
            </div>

            <div class="file-info" id="fileInfo">
                <div class="file-count" id="fileCount">已選擇 0 張圖片</div>
                <div class="file-actions">
                    <button type="button" class="clear-button" id="clearButton">清空</button>
                    <button type="button" class="convert-button" id="convertButton">轉換為PDF</button>
                </div>
            </div>

            <div class="progress-area" id="progressArea">
                <div class="progress-info">
                    <div class="progress-header">
                        <div class="progress-title">轉換進度</div>
                        <div class="progress-percentage" id="progressPercentage">0%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-details">
                        <div id="progressInfo">準備轉換...</div>
                        <div id="currentFile">等待中...</div>
                    </div>
                </div>
            </div>

            <div id="statusMessages"></div>
        </div>
    </div>

    <!-- 使用更穩定的jsPDF CDN -->
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <script>
        // 全局變量
        let selectedImages = [];
        let isConverting = false;

        // DOM元素
        const fileUploadArea = document.getElementById('fileUploadArea');
        const folderUploadArea = document.getElementById('folderUploadArea');
        const fileInput = document.getElementById('fileInput');
        const folderInput = document.getElementById('folderInput');
        const selectFilesButton = document.getElementById('selectFilesButton');
        const selectFolderButton = document.getElementById('selectFolderButton');
        const fileInfo = document.getElementById('fileInfo');
        const fileCount = document.getElementById('fileCount');
        const clearButton = document.getElementById('clearButton');
        const convertButton = document.getElementById('convertButton');
        const progressArea = document.getElementById('progressArea');
        const progressFill = document.getElementById('progressFill');
        const progressPercentage = document.getElementById('progressPercentage');
        const progressInfo = document.getElementById('progressInfo');
        const currentFile = document.getElementById('currentFile');
        const statusMessages = document.getElementById('statusMessages');

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            setupEvents();
            checkPdfLibrary();
        });

        // 檢查PDF庫是否加載
        function checkPdfLibrary() {
            setTimeout(() => {
                if (typeof window.jsPDF === 'undefined') {
                    showStatus('PDF庫加載失敗，請檢查網絡連接並刷新頁面', 'error');
                } else {
                    console.log('PDF庫加載成功');
                }
            }, 2000);
        }

        // 設置事件監聽器
        function setupEvents() {
            // 文件選擇
            selectFilesButton.addEventListener('click', () => fileInput.click());
            selectFolderButton.addEventListener('click', () => folderInput.click());

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    addImages(e.target.files);
                }
            });

            folderInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    addImages(e.target.files);
                }
            });

            // 拖放事件
            [fileUploadArea, folderUploadArea].forEach(area => {
                area.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    area.classList.add('dragover');
                });

                area.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    area.classList.remove('dragover');
                });

                area.addEventListener('drop', (e) => {
                    e.preventDefault();
                    area.classList.remove('dragover');
                    if (e.dataTransfer.files.length > 0) {
                        addImages(e.dataTransfer.files);
                    }
                });
            });

            // 按鈕事件
            clearButton.addEventListener('click', clearImages);
            convertButton.addEventListener('click', convertToPdf);
        }

        // 添加圖片
        function addImages(files) {
            const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));

            if (imageFiles.length === 0) {
                showStatus('請選擇圖片文件', 'error');
                return;
            }

            // 添加新圖片，避免重複
            imageFiles.forEach(file => {
                if (!selectedImages.find(img => img.name === file.name && img.size === file.size)) {
                    selectedImages.push(file);
                }
            });

            updateFileInfo();
            fileInput.value = '';
            folderInput.value = '';
        }

        // 更新文件信息
        function updateFileInfo() {
            if (selectedImages.length === 0) {
                fileInfo.style.display = 'none';
                return;
            }

            fileInfo.style.display = 'block';
            fileCount.textContent = `已選擇 ${selectedImages.length} 張圖片`;
            convertButton.disabled = isConverting;
        }

        // 清空圖片
        function clearImages() {
            selectedImages = [];
            updateFileInfo();
        }

        // 轉換為PDF
        async function convertToPdf() {
            if (selectedImages.length === 0) {
                showStatus('請先選擇圖片', 'error');
                return;
            }

            if (typeof window.jsPDF === 'undefined') {
                showStatus('PDF庫未加載，請刷新頁面重試', 'error');
                return;
            }

            isConverting = true;
            showProgress();
            updateProgress(0, '開始轉換...', '準備中...');
            convertButton.disabled = true;

            try {
                const { jsPDF } = window.jsPDF;
                const pdf = new jsPDF();
                let isFirstPage = true;

                for (let i = 0; i < selectedImages.length; i++) {
                    const file = selectedImages[i];
                    const progress = ((i + 1) / selectedImages.length) * 100;

                    updateProgress(
                        progress,
                        `正在處理: ${file.name}`,
                        `${i + 1}/${selectedImages.length} (${progress.toFixed(1)}%)`
                    );

                    try {
                        const imageData = await loadImageAsDataURL(file);
                        const img = await loadImage(imageData);

                        // 計算圖片在PDF中的尺寸
                        const pdfWidth = pdf.internal.pageSize.getWidth();
                        const pdfHeight = pdf.internal.pageSize.getHeight();
                        const margin = 10;
                        const maxWidth = pdfWidth - (margin * 2);
                        const maxHeight = pdfHeight - (margin * 2);

                        let imgWidth = img.width;
                        let imgHeight = img.height;

                        // 按比例縮放圖片以適應頁面
                        const widthRatio = maxWidth / imgWidth;
                        const heightRatio = maxHeight / imgHeight;
                        const ratio = Math.min(widthRatio, heightRatio);

                        imgWidth *= ratio;
                        imgHeight *= ratio;

                        // 居中放置
                        const x = (pdfWidth - imgWidth) / 2;
                        const y = (pdfHeight - imgHeight) / 2;

                        if (!isFirstPage) {
                            pdf.addPage();
                        }
                        isFirstPage = false;

                        pdf.addImage(imageData, 'JPEG', x, y, imgWidth, imgHeight);

                    } catch (error) {
                        console.error('處理圖片時出錯:', file.name, error);
                        showStatus(`處理圖片 ${file.name} 時出錯`, 'error');
                    }
                }

                updateProgress(100, '生成PDF文件...', '完成');

                // 生成PDF並下載
                const fileName = `images_to_pdf_${new Date().getTime()}.pdf`;
                pdf.save(fileName);

                showStatus(`PDF轉換完成！已下載 ${fileName}`, 'success');

            } catch (error) {
                console.error('PDF轉換失敗:', error);
                showStatus('PDF轉換失敗: ' + error.message, 'error');
            } finally {
                isConverting = false;
                hideProgress();
                convertButton.disabled = false;
            }
        }

        // 加載圖片為DataURL
        function loadImageAsDataURL(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = () => reject(new Error('讀取圖片失敗'));
                reader.readAsDataURL(file);
            });
        }

        // 加載圖片對象
        function loadImage(src) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => resolve(img);
                img.onerror = () => reject(new Error('圖片加載失敗'));
                img.src = src;
            });
        }

        // 顯示進度
        function showProgress() {
            progressArea.style.display = 'block';
        }

        // 更新進度
        function updateProgress(percent, info, current) {
            progressFill.style.width = percent + '%';
            progressPercentage.textContent = percent.toFixed(1) + '%';
            progressInfo.textContent = info;
            currentFile.textContent = current;
        }

        // 隱藏進度
        function hideProgress() {
            progressArea.style.display = 'none';
        }

        // 顯示狀態消息
        function showStatus(message, type) {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.innerHTML = `<span>${type === 'success' ? '✅' : '❌'}</span>${message}`;
            statusMessages.appendChild(statusDiv);

            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 5000);
        }
    </script>
</body>

</html>