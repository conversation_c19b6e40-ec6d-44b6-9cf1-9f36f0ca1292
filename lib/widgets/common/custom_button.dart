import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';

/// 自定義按鈕組件
/// 提供統一的按鈕樣式和行為
class CustomButton extends StatelessWidget {
  /// 按鈕文字
  final String text;
  
  /// 點擊回調
  final VoidCallback? onPressed;
  
  /// 按鈕類型
  final ButtonType type;
  
  /// 按鈕大小
  final ButtonSize size;
  
  /// 是否載入中
  final bool isLoading;
  
  /// 按鈕圖標
  final IconData? icon;
  
  /// 是否全寬
  final bool fullWidth;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.icon,
    this.fullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    // 根據類型獲取顏色
    Color getBackgroundColor() {
      switch (type) {
        case ButtonType.primary:
          return colorScheme.primary;
        case ButtonType.secondary:
          return colorScheme.secondary;
        case ButtonType.success:
          return AppTheme.successColor;
        case ButtonType.warning:
          return AppTheme.warningColor;
        case ButtonType.error:
          return AppTheme.errorColor;
        case ButtonType.outline:
          return Colors.transparent;
      }
    }
    
    Color getForegroundColor() {
      switch (type) {
        case ButtonType.outline:
          return colorScheme.primary;
        default:
          return colorScheme.onPrimary;
      }
    }
    
    // 根據大小獲取內邊距
    EdgeInsets getPadding() {
      switch (size) {
        case ButtonSize.small:
          return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
        case ButtonSize.medium:
          return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
        case ButtonSize.large:
          return const EdgeInsets.symmetric(horizontal: 32, vertical: 16);
      }
    }
    
    // 根據大小獲取文字樣式
    TextStyle getTextStyle() {
      final baseStyle = Theme.of(context).textTheme.labelLarge ?? const TextStyle();
      switch (size) {
        case ButtonSize.small:
          return baseStyle.copyWith(fontSize: 12);
        case ButtonSize.medium:
          return baseStyle.copyWith(fontSize: 14);
        case ButtonSize.large:
          return baseStyle.copyWith(fontSize: 16);
      }
    }

    Widget buttonChild = Row(
      mainAxisSize: fullWidth ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isLoading) ...[
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(getForegroundColor()),
            ),
          ),
          const SizedBox(width: 8),
        ] else if (icon != null) ...[
          Icon(icon, size: 18),
          const SizedBox(width: 8),
        ],
        Text(
          text,
          style: getTextStyle().copyWith(color: getForegroundColor()),
        ),
      ],
    );

    if (type == ButtonType.outline) {
      return SizedBox(
        width: fullWidth ? double.infinity : null,
        child: OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            padding: getPadding(),
            side: BorderSide(color: colorScheme.primary),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppRadius.md),
            ),
          ),
          child: buttonChild,
        ),
      );
    }

    return SizedBox(
      width: fullWidth ? double.infinity : null,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: getBackgroundColor(),
          foregroundColor: getForegroundColor(),
          padding: getPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.md),
          ),
        ),
        child: buttonChild,
      ),
    );
  }
}

/// 按鈕類型枚舉
enum ButtonType {
  primary,    // 主要按鈕
  secondary,  // 次要按鈕
  success,    // 成功按鈕
  warning,    // 警告按鈕
  error,      // 錯誤按鈕
  outline,    // 輪廓按鈕
}

/// 按鈕大小枚舉
enum ButtonSize {
  small,   // 小按鈕
  medium,  // 中等按鈕
  large,   // 大按鈕
}

/// 圖標按鈕組件
class CustomIconButton extends StatelessWidget {
  /// 圖標
  final IconData icon;
  
  /// 點擊回調
  final VoidCallback? onPressed;
  
  /// 按鈕大小
  final double size;
  
  /// 背景顏色
  final Color? backgroundColor;
  
  /// 圖標顏色
  final Color? iconColor;
  
  /// 工具提示
  final String? tooltip;

  const CustomIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = 48.0,
    this.backgroundColor,
    this.iconColor,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    final button = Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(size / 4),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: iconColor ?? Theme.of(context).colorScheme.onPrimaryContainer,
          size: size * 0.5,
        ),
      ),
    );

    if (tooltip != null) {
      return Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return button;
  }
}
