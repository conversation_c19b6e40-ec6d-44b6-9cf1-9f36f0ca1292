import 'package:flutter/material.dart';

/// 通用文件列表項組件
/// 減少音樂、視頻、PDF列表項的重複代碼
class FileListItem extends StatelessWidget {
  final String fileName;
  final String? subtitle;
  final String? duration;
  final bool isSelected;
  final bool isPlaying;
  final bool isCurrent;
  final Color primaryColor;
  final IconData icon;
  final IconData? playingIcon;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onLeadingTap;
  final Widget? trailing;

  const FileListItem({
    super.key,
    required this.fileName,
    this.subtitle,
    this.duration,
    this.isSelected = false,
    this.isPlaying = false,
    this.isCurrent = false,
    required this.primaryColor,
    required this.icon,
    this.playingIcon,
    this.onTap,
    this.onLongPress,
    this.onLeadingTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected
            ? primaryColor.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: isSelected
            ? Border.all(color: primaryColor.withValues(alpha: 0.3))
            : null,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: _buildLeading(),
        title: _buildTitle(),
        subtitle: _buildSubtitle(),
        trailing: trailing ?? _buildTrailing(),
        onTap: onTap,
        onLongPress: onLongPress,
        selected: isSelected,
        selectedTileColor: primaryColor.withValues(alpha: 0.05),
      ),
    );
  }

  /// 構建前導圖標
  Widget _buildLeading() {
    final leadingIcon = isPlaying && playingIcon != null ? playingIcon! : icon;
    final iconColor = isCurrent ? primaryColor : Colors.grey[600];

    final iconWidget = Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(leadingIcon, color: iconColor, size: 32),
    );

    return onLeadingTap != null
        ? GestureDetector(onTap: onLeadingTap, child: iconWidget)
        : iconWidget;
  }

  /// 構建標題
  Widget _buildTitle() {
    return Text(
      fileName,
      style: TextStyle(
        fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
        color: isCurrent ? primaryColor : null,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 構建副標題
  Widget? _buildSubtitle() {
    if (subtitle == null && duration == null) return null;

    return Text(
      subtitle ?? '',
      style: TextStyle(color: Colors.grey[600], fontSize: 12),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 構建尾部
  Widget? _buildTrailing() {
    if (duration == null) return null;

    return Text(
      duration!,
      style: TextStyle(color: Colors.grey[600], fontSize: 12),
    );
  }
}

/// 文件類型枚舉
enum FileType { audio, video, pdf }

/// 文件列表項工廠類
class FileListItemFactory {
  /// 創建音樂文件列表項
  static FileListItem createAudioItem({
    required String fileName,
    String? artist,
    String? duration,
    bool isSelected = false,
    bool isPlaying = false,
    bool isCurrent = false,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    VoidCallback? onLeadingTap,
    Widget? trailing,
  }) {
    return FileListItem(
      fileName: fileName,
      subtitle: artist,
      duration: duration,
      isSelected: isSelected,
      isPlaying: isPlaying,
      isCurrent: isCurrent,
      primaryColor: const Color(0xFF1DB954), // Spotify綠色
      icon: Icons.music_note_outlined,
      playingIcon: Icons.music_note,
      onTap: onTap,
      onLongPress: onLongPress,
      onLeadingTap: onLeadingTap,
      trailing: trailing,
    );
  }

  /// 創建視頻文件列表項
  static FileListItem createVideoItem({
    required String fileName,
    String? duration,
    String? size,
    bool isSelected = false,
    bool isPlaying = false,
    bool isCurrent = false,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    VoidCallback? onLeadingTap,
    Widget? trailing,
  }) {
    return FileListItem(
      fileName: fileName,
      subtitle: size != null ? 'Size: $size' : null,
      duration: duration,
      isSelected: isSelected,
      isPlaying: isPlaying,
      isCurrent: isCurrent,
      primaryColor: const Color(0xFFFF6B6B), // YouTube紅色
      icon: Icons.video_file_outlined,
      playingIcon: Icons.play_circle_filled,
      onTap: onTap,
      onLongPress: onLongPress,
      onLeadingTap: onLeadingTap,
      trailing: trailing,
    );
  }

  /// 創建PDF文件列表項
  static FileListItem createPdfItem({
    required String fileName,
    String? size,
    int? pageCount,
    bool isSelected = false,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    Widget? trailing,
  }) {
    final subtitle = [
      if (size != null) 'Size: $size',
      if (pageCount != null) 'Pages: $pageCount',
    ].join(' • ');

    return FileListItem(
      fileName: fileName,
      subtitle: subtitle.isNotEmpty ? subtitle : null,
      isSelected: isSelected,
      primaryColor: const Color(0xFFFF5722), // PDF橙色
      icon: Icons.picture_as_pdf_outlined,
      onTap: onTap,
      onLongPress: onLongPress,
      trailing: trailing,
    );
  }
}
