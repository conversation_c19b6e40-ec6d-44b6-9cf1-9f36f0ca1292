import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/pdf_controller.dart';
import '../../models/pdf_file.dart';
import '../../services/pdf_reader_service.dart';
import '../../utils/app_theme.dart';

/// PDF閱讀器控制器組件
/// 提供PDF閱讀的完整控制界面
class PdfReaderControls extends GetView<PdfController> {
  const PdfReaderControls({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 頁面信息和進度
                _buildPageInfo(context),
                const SizedBox(height: AppSpacing.md),

                // 主要控制按鈕
                _buildMainControls(context),
                const SizedBox(height: AppSpacing.sm),

                // 次要控制項
                _buildSecondaryControls(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建頁面信息
  Widget _buildPageInfo(BuildContext context) {
    return Column(
      children: [
        // 進度條
        LinearProgressIndicator(
          value: controller.readProgress,
          backgroundColor: Colors.grey.withValues(alpha: 0.3),
          valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.pdfColor),
        ),
        const SizedBox(height: AppSpacing.sm),

        // 頁面信息
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              controller.readProgressText,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontFamily: 'monospace',
              ),
            ),
            Text(
              '${(controller.readProgress * 100).round()}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.pdfColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 構建主要控制按鈕
  Widget _buildMainControls(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 第一頁
        IconButton(
          icon: const Icon(Icons.first_page),
          onPressed: controller.canGoPrevious ? controller.firstPage : null,
          tooltip: '第一頁',
        ),

        // 上一頁
        IconButton(
          icon: const Icon(Icons.chevron_left, size: 32),
          onPressed: controller.canGoPrevious ? controller.previousPage : null,
          tooltip: '上一頁',
        ),

        // 跳轉頁面
        Container(
          decoration: BoxDecoration(
            color: AppTheme.pdfColor,
            borderRadius: BorderRadius.circular(AppRadius.md),
          ),
          child: IconButton(
            icon: const Icon(Icons.format_list_numbered, color: Colors.white),
            onPressed: controller.showGoToPageDialog,
            tooltip: '跳轉頁面',
          ),
        ),

        // 下一頁
        IconButton(
          icon: const Icon(Icons.chevron_right, size: 32),
          onPressed: controller.canGoNext ? controller.nextPage : null,
          tooltip: '下一頁',
        ),

        // 最後一頁
        IconButton(
          icon: const Icon(Icons.last_page),
          onPressed: controller.canGoNext ? controller.lastPage : null,
          tooltip: '最後一頁',
        ),
      ],
    );
  }

  /// 構建次要控制項
  Widget _buildSecondaryControls(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 縮小
        IconButton(
          icon: const Icon(Icons.zoom_out),
          onPressed: controller.zoomOut,
          tooltip: '縮小',
        ),

        // 重置縮放
        IconButton(
          icon: const Icon(Icons.zoom_out_map),
          onPressed: controller.resetZoom,
          tooltip: '重置縮放',
        ),

        // 放大
        IconButton(
          icon: const Icon(Icons.zoom_in),
          onPressed: controller.zoomIn,
          tooltip: '放大',
        ),

        // 書籤
        IconButton(
          icon: Icon(
            controller.hasCurrentBookmark
                ? Icons.bookmark
                : Icons.bookmark_border,
            color: controller.hasCurrentBookmark ? AppTheme.pdfColor : null,
          ),
          onPressed: controller.toggleBookmark,
          tooltip: controller.hasCurrentBookmark ? '移除書籤' : '添加書籤',
        ),

        // 設置
        PopupMenuButton<String>(
          icon: const Icon(Icons.settings),
          onSelected: (value) => _handleMenuAction(value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'fit_mode',
              child: Row(
                children: [
                  Icon(Icons.fit_screen),
                  SizedBox(width: 8),
                  Text('適配模式'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'bookmarks',
              child: Row(
                children: [
                  Icon(Icons.bookmarks),
                  SizedBox(width: 8),
                  Text('書籤列表'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'brightness',
              child: Row(
                children: [
                  Icon(Icons.brightness_6),
                  SizedBox(width: 8),
                  Text('亮度調節'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 處理菜單操作
  void _handleMenuAction(String action) {
    switch (action) {
      case 'fit_mode':
        _showFitModeDialog();
        break;
      case 'bookmarks':
        _showBookmarksDialog();
        break;
      case 'brightness':
        _showBrightnessDialog();
        break;
    }
  }

  /// 顯示適配模式選擇對話框
  void _showFitModeDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('頁面適配模式'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: PageFitMode.values.map((mode) {
            return ListTile(
              title: Text(mode.displayName),
              leading: Radio<PageFitMode>(
                value: mode,
                groupValue: controller.readingSettings.fitMode,
                onChanged: (value) {
                  if (value != null) {
                    controller.updateFitMode(value);
                    Get.back();
                  }
                },
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
        ],
      ),
    );
  }

  /// 顯示書籤列表對話框
  void _showBookmarksDialog() {
    final bookmarks = controller.bookmarks;

    if (bookmarks.isEmpty) {
      Get.snackbar('沒有書籤', '您還沒有添加任何書籤', snackPosition: SnackPosition.BOTTOM);
      return;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('書籤列表'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: bookmarks.length,
            itemBuilder: (context, index) {
              final page = bookmarks[index];
              return ListTile(
                leading: const Icon(Icons.bookmark, color: AppTheme.pdfColor),
                title: Text('第 $page 頁'),
                onTap: () {
                  controller.goToBookmark(page);
                  Get.back();
                },
                trailing: IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () {
                    Get.find<PdfReaderService>().removeBookmark(page);
                    Get.back();
                    _showBookmarksDialog(); // 重新顯示更新後的列表
                  },
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('關閉')),
        ],
      ),
    );
  }

  /// 顯示亮度調節對話框
  void _showBrightnessDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('亮度調節'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.brightness_6, size: 48),
            const SizedBox(height: AppSpacing.md),
            Obx(
              () => Slider(
                value: controller.readingSettings.brightness,
                onChanged: controller.updateBrightness,
                min: 0.1,
                max: 1.0,
                divisions: 9,
                label:
                    '${(controller.readingSettings.brightness * 100).round()}%',
              ),
            ),
            Text(
              '${(controller.readingSettings.brightness * 100).round()}%',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('完成')),
        ],
      ),
    );
  }
}

/// PDF閱讀器頂部工具欄
class PdfReaderAppBar extends GetView<PdfController>
    implements PreferredSizeWidget {
  const PdfReaderAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Obx(
        () => Text(
          controller.currentPdfFile?.title ?? 'PDF閱讀器',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
      centerTitle: true,
      actions: [
        // 書籤快捷按鈕
        Obx(
          () => IconButton(
            icon: Icon(
              controller.hasCurrentBookmark
                  ? Icons.bookmark
                  : Icons.bookmark_border,
              color: controller.hasCurrentBookmark ? AppTheme.pdfColor : null,
            ),
            onPressed: controller.toggleBookmark,
            tooltip: controller.hasCurrentBookmark ? '移除書籤' : '添加書籤',
          ),
        ),

        // 更多選項
        PopupMenuButton<String>(
          onSelected: (value) => _handleAction(value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'info',
              child: Row(
                children: [Icon(Icons.info), SizedBox(width: 8), Text('文件信息')],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [Icon(Icons.share), SizedBox(width: 8), Text('分享')],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _handleAction(String action) {
    switch (action) {
      case 'info':
        _showFileInfo();
        break;
      case 'share':
        // TODO: 實現分享功能
        Get.snackbar('功能開發中', '分享功能即將推出');
        break;
    }
  }

  void _showFileInfo() {
    final pdfFile = controller.currentPdfFile;
    if (pdfFile == null) return;

    Get.dialog(
      AlertDialog(
        title: const Text('文件信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('文件名', pdfFile.fileName),
            _buildInfoRow('標題', pdfFile.title),
            if (pdfFile.author != null) _buildInfoRow('作者', pdfFile.author!),
            _buildInfoRow('總頁數', '${pdfFile.totalPages ?? "未知"}'),
            _buildInfoRow('文件大小', pdfFile.formattedFileSize),
            _buildInfoRow('閱讀進度', pdfFile.readProgressPercentage),
            if (pdfFile.hasReadHistory)
              _buildInfoRow('最後閱讀', _formatDateTime(pdfFile.lastReadAt!)),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('關閉')),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
