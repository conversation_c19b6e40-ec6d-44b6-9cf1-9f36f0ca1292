import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdfx/pdfx.dart' as pdfx;
import '../../controllers/pdf_controller.dart';
import '../../models/pdf_file.dart';
import '../../utils/logger.dart';
import '../../services/pdf_reader_service.dart';
import '../../utils/app_theme.dart';
import '../../widgets/common/loading_widget.dart';
import 'pdf_reader_controls.dart';

/// PDF閱讀器組件
/// 顯示PDF內容和處理用戶交互
class PdfReaderWidget extends GetView<PdfController> {
  const PdfReaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isReaderLoading) {
        return const LoadingWidget(message: '載入PDF中...');
      }

      if (controller.errorMessage != null) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 80, color: Colors.red),
              const SizedBox(height: 16),
              Text('載入失敗', style: Theme.of(context).textTheme.headlineSmall),
              const SizedBox(height: 8),
              Text(
                controller.errorMessage!,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Get.back(),
                child: const Text('返回'),
              ),
            ],
          ),
        );
      }

      final pdfController = Get.find<PdfReaderService>().pdfController;
      if (pdfController == null) {
        return const Center(child: Text('PDF控制器未初始化'));
      }

      return _buildPdfViewer(context, pdfController);
    });
  }

  /// 構建PDF查看器
  Widget _buildPdfViewer(
    BuildContext context,
    pdfx.PdfController pdfController,
  ) {
    return Container(
      color: _getBackgroundColor(),
      child: Opacity(
        opacity: controller.readingSettings.brightness,
        child: pdfx.PdfView(
          controller: pdfController,
          onPageChanged: (page) {
            // 頁面變化會由PdfReaderService自動處理
          },
          onDocumentLoaded: (document) {
            Logger.info('PDF document loaded: ${document.pagesCount} pages');
          },
          onDocumentError: (error) {
            Logger.error('PDF document error: $error');
          },
          scrollDirection: _getScrollDirection(),
          physics: const BouncingScrollPhysics(),
          pageSnapping: true,
          builders: pdfx.PdfViewBuilders<pdfx.DefaultBuilderOptions>(
            options: const pdfx.DefaultBuilderOptions(),
            documentLoaderBuilder: (_) =>
                const LoadingWidget(message: '載入PDF中...'),
            pageLoaderBuilder: (_) => const Center(
              child: CircularProgressIndicator(color: AppTheme.pdfColor),
            ),
            errorBuilder: (_, error) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('載入錯誤: $error'),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 獲取滾動方向
  Axis _getScrollDirection() {
    switch (controller.readingSettings.direction) {
      case ReadingDirection.leftToRight:
      case ReadingDirection.rightToLeft:
        return Axis.horizontal;
      case ReadingDirection.vertical:
        return Axis.vertical;
    }
  }

  /// 獲取背景顏色
  Color _getBackgroundColor() {
    return Color(controller.readingSettings.backgroundColor);
  }
}

/// PDF閱讀器頁面
class PdfReaderPage extends GetView<PdfController> {
  const PdfReaderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const PdfReaderAppBar(),
      body: const PdfReaderWidget(),
      bottomNavigationBar: const PdfReaderControls(),
    );
  }
}
