import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/playlist.dart';
import '../../models/audio_file.dart';
import '../../utils/app_theme.dart';
import '../common/custom_button.dart';

/// 播放列表組件
/// 顯示播放列表信息和歌曲列表
class PlaylistWidget extends StatelessWidget {
  /// 播放列表
  final Playlist playlist;

  /// 播放歌曲回調
  final Function(AudioFile audioFile)? onPlayAudio;

  /// 移除歌曲回調
  final Function(AudioFile audioFile)? onRemoveAudio;

  /// 移動歌曲回調
  final Function(int oldIndex, int newIndex)? onMoveAudio;

  /// 是否可編輯
  final bool editable;

  const PlaylistWidget({
    super.key,
    required this.playlist,
    this.onPlayAudio,
    this.onRemoveAudio,
    this.onMoveAudio,
    this.editable = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 播放列表信息
        _buildPlaylistHeader(context),

        // 歌曲列表
        Expanded(child: _buildSongList(context)),
      ],
    );
  }

  /// 構建播放列表頭部
  Widget _buildPlaylistHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.musicColor.withValues(alpha: 0.1),
            AppTheme.musicColor.withValues(alpha: 0.05),
          ],
        ),
      ),
      child: Row(
        children: [
          // 封面圖片
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppTheme.musicColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppRadius.md),
            ),
            child: playlist.coverImagePath != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(AppRadius.md),
                    child: Image.file(
                      File(playlist.coverImagePath!),
                      fit: BoxFit.cover,
                    ),
                  )
                : const Icon(
                    Icons.queue_music,
                    size: 40,
                    color: AppTheme.musicColor,
                  ),
          ),

          const SizedBox(width: AppSpacing.lg),

          // 播放列表信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  playlist.name,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.musicColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                if (playlist.description != null) ...[
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    playlist.description!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                const SizedBox(height: AppSpacing.sm),

                Row(
                  children: [
                    Icon(
                      Icons.music_note,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Text(
                      '${playlist.songCount} 首歌曲',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.md),
                    Icon(
                      Icons.access_time,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Text(
                      playlist.formattedTotalDuration,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建歌曲列表
  Widget _buildSongList(BuildContext context) {
    if (playlist.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.music_off,
              size: 80,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              '播放列表為空',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              '添加一些歌曲來開始播放',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return editable && onMoveAudio != null
        ? ReorderableListView.builder(
            padding: const EdgeInsets.all(AppSpacing.sm),
            itemCount: playlist.audioFiles.length,
            onReorder: (oldIndex, newIndex) {
              if (newIndex > oldIndex) newIndex--;
              onMoveAudio?.call(oldIndex, newIndex);
            },
            itemBuilder: (context, index) {
              final audioFile = playlist.audioFiles[index];
              return _buildSongItem(
                context,
                audioFile,
                index,
                key: ValueKey(audioFile.path),
              );
            },
          )
        : ListView.builder(
            padding: const EdgeInsets.all(AppSpacing.sm),
            itemCount: playlist.audioFiles.length,
            itemBuilder: (context, index) {
              final audioFile = playlist.audioFiles[index];
              return _buildSongItem(context, audioFile, index);
            },
          );
  }

  /// 構建歌曲項目
  Widget _buildSongItem(
    BuildContext context,
    AudioFile audioFile,
    int index, {
    Key? key,
  }) {
    return Card(
      key: key,
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.xs,
        vertical: AppSpacing.xs,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),

        // 序號或拖拽手柄
        leading: editable && onMoveAudio != null
            ? ReorderableDragStartListener(
                index: index,
                child: const Icon(Icons.drag_handle),
              )
            : Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppTheme.musicColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppRadius.sm),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.musicColor,
                    ),
                  ),
                ),
              ),

        // 歌曲信息
        title: Text(
          audioFile.title,
          style: Theme.of(context).textTheme.titleMedium,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${audioFile.artist} • ${audioFile.album}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              audioFile.formattedDuration,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontFamily: 'monospace',
              ),
            ),
          ],
        ),

        // 操作按鈕
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 播放按鈕
            IconButton(
              icon: const Icon(Icons.play_arrow),
              onPressed: () => onPlayAudio?.call(audioFile),
              tooltip: '播放',
            ),

            // 移除按鈕
            if (editable && onRemoveAudio != null)
              IconButton(
                icon: const Icon(Icons.remove_circle_outline),
                onPressed: () => onRemoveAudio?.call(audioFile),
                tooltip: '移除',
              ),
          ],
        ),

        // 點擊播放
        onTap: () => onPlayAudio?.call(audioFile),
      ),
    );
  }
}

/// 播放列表創建對話框
class PlaylistCreateDialog extends StatefulWidget {
  /// 創建回調
  final Function(String name, String? description)? onCreate;

  const PlaylistCreateDialog({super.key, this.onCreate});

  @override
  State<PlaylistCreateDialog> createState() => _PlaylistCreateDialogState();
}

class _PlaylistCreateDialogState extends State<PlaylistCreateDialog> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('創建播放列表'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: '播放列表名稱',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '請輸入播放列表名稱';
                }
                return null;
              },
              autofocus: true,
            ),
            const SizedBox(height: AppSpacing.md),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: '描述（可選）',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(onPressed: () => Get.back(), child: const Text('取消')),
        CustomButton(text: '創建', onPressed: _createPlaylist),
      ],
    );
  }

  void _createPlaylist() {
    if (_formKey.currentState?.validate() == true) {
      final name = _nameController.text.trim();
      final description = _descriptionController.text.trim();
      widget.onCreate?.call(name, description.isEmpty ? null : description);
      Get.back();
    }
  }
}
