import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/music_controller.dart';
import '../../models/lyric.dart';
import '../../utils/app_theme.dart';

/// 歌詞滾動組件
/// 支持歌詞同步滾動，無歌詞時顯示歌曲名
class LyricScrollWidget extends StatefulWidget {
  final double height;
  final EdgeInsets padding;

  const LyricScrollWidget({
    super.key,
    this.height = 300,
    this.padding = const EdgeInsets.all(16),
  });

  @override
  State<LyricScrollWidget> createState() => _LyricScrollWidgetState();
}

class _LyricScrollWidgetState extends State<LyricScrollWidget> {
  final MusicController controller = Get.find<MusicController>();
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      padding: widget.padding,
      child: Obx(() {
        final currentAudio = controller.currentAudio;
        final lyrics = controller.currentLyrics;
        final position = controller.position;

        if (currentAudio == null) {
          return _buildEmptyState('沒有正在播放的音樂');
        }

        if (lyrics == null || lyrics.isEmpty) {
          return _buildSongTitle(currentAudio.title);
        }

        return _buildLyricsList(lyrics, position);
      }),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.music_note_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  /// 構建歌曲標題顯示（無歌詞時）
  Widget _buildSongTitle(String title) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.music_note,
            size: 80,
            color: AppTheme.musicColor.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.musicColor,
            ),
            textAlign: TextAlign.center,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 16),
          Text('暫無歌詞', style: TextStyle(fontSize: 14, color: Colors.grey[600])),
        ],
      ),
    );
  }

  /// 構建歌詞列表
  Widget _buildLyricsList(List<LyricLine> lyrics, Duration position) {
    // 找到當前播放位置對應的歌詞行
    int currentIndex = _findCurrentLyricIndex(lyrics, position);

    // 自動滾動到當前歌詞
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToCurrentLyric(currentIndex);
    });

    return ListView.builder(
      controller: _scrollController,
      itemCount: lyrics.length,
      itemBuilder: (context, index) {
        final lyricLine = lyrics[index];
        final isCurrent = index == currentIndex;
        final isPassed = index < currentIndex;

        return _buildLyricItem(lyricLine, isCurrent, isPassed);
      },
    );
  }

  /// 構建歌詞項目
  Widget _buildLyricItem(LyricLine lyricLine, bool isCurrent, bool isPassed) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: AnimatedDefaultTextStyle(
        duration: const Duration(milliseconds: 300),
        style: TextStyle(
          fontSize: isCurrent ? 18 : 16,
          fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
          color: isCurrent
              ? AppTheme.musicColor
              : isPassed
              ? Colors.grey[600]
              : Colors.grey[800],
          height: 1.5,
        ),
        child: Text(
          lyricLine.text.trim().isEmpty ? '♪' : lyricLine.text,
          textAlign: TextAlign.center,
          style: isCurrent
              ? TextStyle(
                  shadows: [
                    Shadow(
                      color: AppTheme.musicColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                    ),
                  ],
                )
              : null,
        ),
      ),
    );
  }

  /// 找到當前播放位置對應的歌詞行索引
  int _findCurrentLyricIndex(List<LyricLine> lyrics, Duration position) {
    final positionMs = position.inMilliseconds;
    for (int i = lyrics.length - 1; i >= 0; i--) {
      if (positionMs >= lyrics[i].timestamp) {
        return i;
      }
    }
    return 0;
  }

  /// 滾動到當前歌詞
  void _scrollToCurrentLyric(int index) {
    if (!_scrollController.hasClients) return;

    const itemHeight = 50.0; // 估算每行歌詞的高度
    final targetOffset =
        (index * itemHeight) - (widget.height / 2) + (itemHeight / 2);

    final maxOffset = _scrollController.position.maxScrollExtent;
    final clampedOffset = targetOffset.clamp(0.0, maxOffset);

    _scrollController.animateTo(
      clampedOffset,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }
}

/// 簡化版歌詞顯示組件
/// 用於播放器控制器中的小型歌詞顯示
class MiniLyricWidget extends StatelessWidget {
  final double height;

  const MiniLyricWidget({super.key, this.height = 60});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MusicController>();

    return Container(
      height: height,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Obx(() {
        final currentAudio = controller.currentAudio;
        final lyrics = controller.currentLyrics;
        final position = controller.position;

        if (currentAudio == null) {
          return const Center(
            child: Text('沒有正在播放的音樂', style: TextStyle(color: Colors.grey)),
          );
        }

        if (lyrics == null || lyrics.isEmpty) {
          return Center(
            child: Text(
              currentAudio.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppTheme.musicColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          );
        }

        // 找到當前歌詞
        final currentLyric = _findCurrentLyric(lyrics, position);

        return Center(
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Text(
              currentLyric?.text.trim().isEmpty == true
                  ? '♪'
                  : (currentLyric?.text ?? currentAudio.title),
              key: ValueKey(currentLyric?.text ?? currentAudio.title),
              style: const TextStyle(fontSize: 14, color: AppTheme.musicColor),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
      }),
    );
  }

  /// 找到當前播放位置對應的歌詞
  LyricLine? _findCurrentLyric(List<LyricLine> lyrics, Duration position) {
    final positionMs = position.inMilliseconds;
    for (int i = lyrics.length - 1; i >= 0; i--) {
      if (positionMs >= lyrics[i].timestamp) {
        return lyrics[i];
      }
    }
    return lyrics.isNotEmpty ? lyrics.first : null;
  }
}
