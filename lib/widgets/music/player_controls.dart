import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/music_controller.dart';
import '../../models/player_state.dart' as app_state;
import '../../utils/app_theme.dart';
import '../common/custom_button.dart';
import 'lyric_scroll_widget.dart';

/// 音樂播放控制器組件
/// 提供播放、暫停、上一首、下一首等控制功能
class PlayerControls extends GetView<MusicController> {
  const PlayerControls({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(AppRadius.lg),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 當前播放歌曲信息
            if (controller.currentAudio != null) ...[
              _buildCurrentSongInfo(context),
              const SizedBox(height: AppSpacing.md),
            ],

            // 播放進度條
            _buildProgressBar(context),
            const SizedBox(height: AppSpacing.md),

            // 播放控制按鈕
            _buildControlButtons(context),
            const SizedBox(height: AppSpacing.sm),

            // 播放模式和音量控制
            _buildSecondaryControls(context),
          ],
        ),
      ),
    );
  }

  /// 構建當前歌曲信息
  Widget _buildCurrentSongInfo(BuildContext context) {
    final currentAudio = controller.currentAudio!;

    return Row(
      children: [
        // 專輯封面佔位符
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppTheme.musicColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppRadius.md),
          ),
          child: const Icon(
            Icons.music_note,
            color: AppTheme.musicColor,
            size: 30,
          ),
        ),
        const SizedBox(width: AppSpacing.md),

        // 歌曲信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                currentAudio.title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(
                '${currentAudio.artist} • ${currentAudio.album}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppSpacing.xs),
              // 迷你歌詞顯示
              const MiniLyricWidget(height: 40),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建播放進度條
  Widget _buildProgressBar(BuildContext context) {
    return Column(
      children: [
        // 進度條
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
            activeTrackColor: AppTheme.musicColor,
            inactiveTrackColor: AppTheme.musicColor.withValues(alpha: 0.3),
            thumbColor: AppTheme.musicColor,
            overlayColor: AppTheme.musicColor.withValues(alpha: 0.2),
          ),
          child: Slider(
            value: controller.playProgress,
            onChanged: (value) {
              controller.seekTo(value);
            },
            min: 0.0,
            max: 1.0,
          ),
        ),

        // 時間顯示
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                controller.formattedCurrentTime,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontFamily: 'monospace',
                ),
              ),
              Text(
                controller.formattedTotalTime,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建主要控制按鈕
  Widget _buildControlButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 上一首按鈕
        CustomIconButton(
          icon: Icons.skip_previous,
          onPressed: controller.playPrevious,
          size: 48,
          tooltip: '上一首',
        ),

        // 播放/暫停按鈕
        Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            color: AppTheme.musicColor,
            borderRadius: BorderRadius.circular(32),
            boxShadow: [
              BoxShadow(
                color: AppTheme.musicColor.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: IconButton(
            onPressed: controller.togglePlayPause,
            icon: Icon(
              controller.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),

        // 下一首按鈕
        CustomIconButton(
          icon: Icons.skip_next,
          onPressed: controller.playNext,
          size: 48,
          tooltip: '下一首',
        ),
      ],
    );
  }

  /// 構建次要控制項
  Widget _buildSecondaryControls(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 播放模式按鈕
        Expanded(
          child: Row(
            children: [
              IconButton(
                onPressed: controller.togglePlayMode,
                icon: Icon(
                  _getPlayModeIcon(controller.playerState.playMode),
                  color: AppTheme.musicColor,
                ),
                tooltip: controller.playerState.playMode.displayName,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                controller.playerState.playMode.displayName,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.musicColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        // 音量控制
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Icon(
                Icons.volume_up,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                size: 20,
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    trackHeight: 2,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 6,
                    ),
                    overlayShape: const RoundSliderOverlayShape(
                      overlayRadius: 12,
                    ),
                    activeTrackColor: AppTheme.musicColor,
                    inactiveTrackColor: AppTheme.musicColor.withValues(
                      alpha: 0.3,
                    ),
                    thumbColor: AppTheme.musicColor,
                  ),
                  child: Slider(
                    value: controller.playerState.volume,
                    onChanged: controller.setVolume,
                    min: 0.0,
                    max: 1.0,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 獲取播放模式圖標
  IconData _getPlayModeIcon(app_state.PlayMode playMode) {
    switch (playMode) {
      case app_state.PlayMode.sequence:
        return Icons.playlist_play;
      case app_state.PlayMode.repeatOne:
        return Icons.repeat_one;
      case app_state.PlayMode.repeatAll:
        return Icons.repeat;
      case app_state.PlayMode.shuffle:
        return Icons.shuffle;
    }
  }
}

/// 迷你播放控制器
/// 在其他頁面顯示的簡化版播放控制器
class MiniPlayerControls extends GetView<MusicController> {
  const MiniPlayerControls({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.currentAudio == null) {
        return const SizedBox.shrink();
      }

      return Container(
        height: 80,
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          border: Border(
            top: BorderSide(
              color: Theme.of(
                context,
              ).colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
        ),
        child: Row(
          children: [
            // 專輯封面
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppTheme.musicColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppRadius.sm),
              ),
              child: const Icon(
                Icons.music_note,
                color: AppTheme.musicColor,
                size: 24,
              ),
            ),
            const SizedBox(width: AppSpacing.md),

            // 歌曲信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    controller.currentAudio!.title,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    controller.currentAudio!.artist,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // 播放控制按鈕
            IconButton(
              onPressed: controller.playPrevious,
              icon: const Icon(Icons.skip_previous),
            ),
            IconButton(
              onPressed: controller.togglePlayPause,
              icon: Icon(controller.isPlaying ? Icons.pause : Icons.play_arrow),
            ),
            IconButton(
              onPressed: controller.playNext,
              icon: const Icon(Icons.skip_next),
            ),
          ],
        ),
      );
    });
  }
}
