import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/lyric.dart';
import '../../utils/app_theme.dart';

/// 歌詞顯示組件
/// 提供滾動歌詞顯示功能，支持高亮當前行和平滑滾動
class LyricWidget extends StatefulWidget {
  /// 歌詞數據
  final Lyric? lyric;

  /// 當前播放位置（毫秒）
  final int currentPosition;

  /// 歌詞樣式
  final LyricStyle style;

  /// 點擊歌詞行的回調
  final Function(int timestamp)? onTapLine;

  const LyricWidget({
    super.key,
    this.lyric,
    required this.currentPosition,
    this.style = const LyricStyle(),
    this.onTapLine,
  });

  @override
  State<LyricWidget> createState() => _LyricWidgetState();
}

class _LyricWidgetState extends State<LyricWidget>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  int _currentLineIndex = -1;
  final double _lineHeight = 60.0;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    if (widget.lyric?.hasLyrics == true) {
      _fadeController.forward();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(LyricWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 歌詞變化時重新播放動畫
    if (widget.lyric != oldWidget.lyric) {
      if (widget.lyric?.hasLyrics == true) {
        _fadeController.forward();
      } else {
        _fadeController.reverse();
      }
    }

    // 更新當前行並滾動
    if (widget.currentPosition != oldWidget.currentPosition) {
      _updateCurrentLine();
    }
  }

  /// 更新當前歌詞行
  void _updateCurrentLine() {
    if (widget.lyric == null || !widget.lyric!.hasLyrics) return;

    final newLineIndex = widget.lyric!.getCurrentLineIndex(
      widget.currentPosition,
    );
    if (newLineIndex != _currentLineIndex) {
      setState(() {
        _currentLineIndex = newLineIndex;
      });
      _scrollToCurrentLine();
    }
  }

  /// 滾動到當前歌詞行
  void _scrollToCurrentLine() {
    if (_currentLineIndex < 0 || !_scrollController.hasClients) return;

    final targetOffset =
        _currentLineIndex * _lineHeight -
        (_scrollController.position.viewportDimension / 2) +
        (_lineHeight / 2);

    _scrollController.animateTo(
      targetOffset.clamp(
        _scrollController.position.minScrollExtent,
        _scrollController.position.maxScrollExtent,
      ),
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.lyric == null || !widget.lyric!.hasLyrics) {
      return _buildNoLyricView();
    }

    return FadeTransition(opacity: _fadeAnimation, child: _buildLyricView());
  }

  /// 構建無歌詞視圖
  Widget _buildNoLyricView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.music_note, size: 80, color: widget.style.inactiveColor),
          const SizedBox(height: 16),
          Text(
            '暫無歌詞',
            style: TextStyle(fontSize: 18, color: widget.style.inactiveColor),
          ),
          const SizedBox(height: 8),
          Text(
            '請享受純音樂時光',
            style: TextStyle(
              fontSize: 14,
              color: widget.style.inactiveColor.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建歌詞視圖
  Widget _buildLyricView() {
    final lines = widget.lyric!.lines;

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(vertical: 100),
      itemCount: lines.length,
      itemBuilder: (context, index) {
        final line = lines[index];
        final isCurrentLine = index == _currentLineIndex;
        final isNextLine = index == _currentLineIndex + 1;

        return _buildLyricLine(
          line: line,
          index: index,
          isCurrentLine: isCurrentLine,
          isNextLine: isNextLine,
        );
      },
    );
  }

  /// 構建單行歌詞
  Widget _buildLyricLine({
    required LyricLine line,
    required int index,
    required bool isCurrentLine,
    required bool isNextLine,
  }) {
    return GestureDetector(
      onTap: () => widget.onTapLine?.call(line.timestamp),
      child: Container(
        height: _lineHeight,
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: AnimatedDefaultTextStyle(
          duration: const Duration(milliseconds: 300),
          style: _getLyricLineStyle(isCurrentLine, isNextLine),
          child: Text(
            line.isEmpty ? '♪' : line.text,
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  /// 獲取歌詞行樣式
  TextStyle _getLyricLineStyle(bool isCurrentLine, bool isNextLine) {
    if (isCurrentLine) {
      return TextStyle(
        fontSize: widget.style.currentFontSize,
        fontWeight: widget.style.currentFontWeight,
        color: widget.style.currentColor,
        height: 1.5,
      );
    } else if (isNextLine) {
      return TextStyle(
        fontSize: widget.style.nextFontSize,
        fontWeight: widget.style.nextFontWeight,
        color: widget.style.nextColor,
        height: 1.5,
      );
    } else {
      return TextStyle(
        fontSize: widget.style.inactiveFontSize,
        fontWeight: widget.style.inactiveFontWeight,
        color: widget.style.inactiveColor,
        height: 1.5,
      );
    }
  }
}

/// 歌詞樣式配置
class LyricStyle {
  /// 當前行顏色
  final Color currentColor;

  /// 下一行顏色
  final Color nextColor;

  /// 非活動行顏色
  final Color inactiveColor;

  /// 當前行字體大小
  final double currentFontSize;

  /// 下一行字體大小
  final double nextFontSize;

  /// 非活動行字體大小
  final double inactiveFontSize;

  /// 當前行字體粗細
  final FontWeight currentFontWeight;

  /// 下一行字體粗細
  final FontWeight nextFontWeight;

  /// 非活動行字體粗細
  final FontWeight inactiveFontWeight;

  const LyricStyle({
    this.currentColor = AppTheme.musicColor,
    this.nextColor = const Color(0xFF666666),
    this.inactiveColor = const Color(0xFF999999),
    this.currentFontSize = 18.0,
    this.nextFontSize = 16.0,
    this.inactiveFontSize = 14.0,
    this.currentFontWeight = FontWeight.w600,
    this.nextFontWeight = FontWeight.w500,
    this.inactiveFontWeight = FontWeight.normal,
  });

  /// 創建深色主題樣式
  factory LyricStyle.dark() {
    return const LyricStyle(
      currentColor: Colors.white,
      nextColor: Color(0xFFCCCCCC),
      inactiveColor: Color(0xFF666666),
    );
  }

  /// 創建淺色主題樣式
  factory LyricStyle.light() {
    return const LyricStyle(
      currentColor: Color(0xFF333333),
      nextColor: Color(0xFF666666),
      inactiveColor: Color(0xFF999999),
    );
  }

  /// 創建彩色主題樣式
  factory LyricStyle.colorful() {
    return const LyricStyle(
      currentColor: AppTheme.musicColor,
      nextColor: Color(0xFF4CAF50),
      inactiveColor: Color(0xFF999999),
      currentFontSize: 20.0,
      nextFontSize: 18.0,
      inactiveFontSize: 16.0,
    );
  }
}

/// 歌詞編輯器組件
class LyricEditor extends StatefulWidget {
  /// 歌詞數據
  final Lyric? lyric;

  /// 保存回調
  final Function(Lyric lyric)? onSave;

  const LyricEditor({super.key, this.lyric, this.onSave});

  @override
  State<LyricEditor> createState() => _LyricEditorState();
}

class _LyricEditorState extends State<LyricEditor> {
  late TextEditingController _textController;
  late Lyric _currentLyric;

  @override
  void initState() {
    super.initState();
    _currentLyric = widget.lyric ?? Lyric.empty();
    _textController = TextEditingController(text: _currentLyric.toLrc());
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('編輯歌詞'),
        actions: [TextButton(onPressed: _saveLyric, child: const Text('保存'))],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 歌詞格式說明
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'LRC歌詞格式說明：\n'
                '[mm:ss.xx]歌詞文本\n'
                '例如：[00:12.50]這是一行歌詞',
                style: TextStyle(fontSize: 12),
              ),
            ),
            const SizedBox(height: 16),

            // 歌詞編輯框
            Expanded(
              child: TextField(
                controller: _textController,
                maxLines: null,
                expands: true,
                decoration: const InputDecoration(
                  hintText: '請輸入LRC格式的歌詞...',
                  border: OutlineInputBorder(),
                ),
                style: const TextStyle(fontFamily: 'monospace', fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 保存歌詞
  void _saveLyric() {
    try {
      final lyricContent = _textController.text;
      final lyric = Lyric.fromLrc(lyricContent);
      widget.onSave?.call(lyric);
      Get.back();
      Get.snackbar('保存成功', '歌詞已保存');
    } catch (e) {
      Get.snackbar('保存失敗', '歌詞格式錯誤: $e');
    }
  }
}
