import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/music_controller.dart';
import '../../models/audio_file.dart';
import '../../models/music_folder.dart';
import '../../utils/app_theme.dart';

/// 音樂拖拽移動組件
/// 支持將音樂文件拖拽到不同文件夾
class MusicDragDropWidget extends StatefulWidget {
  final Widget child;
  final AudioFile? audioFile;
  final List<AudioFile>? audioFiles;
  final VoidCallback? onDragStarted;
  final VoidCallback? onDragCompleted;

  const MusicDragDropWidget({
    super.key,
    required this.child,
    this.audioFile,
    this.audioFiles,
    this.onDragStarted,
    this.onDragCompleted,
  });

  @override
  State<MusicDragDropWidget> createState() => _MusicDragDropWidgetState();
}

class _MusicDragDropWidgetState extends State<MusicDragDropWidget> {
  final MusicController controller = Get.find<MusicController>();

  @override
  Widget build(BuildContext context) {
    final files = widget.audioFiles ?? 
        (widget.audioFile != null ? [widget.audioFile!] : <AudioFile>[]);

    if (files.isEmpty) {
      return widget.child;
    }

    return Draggable<List<AudioFile>>(
      data: files,
      feedback: _buildDragFeedback(files),
      childWhenDragging: _buildChildWhenDragging(),
      onDragStarted: () {
        widget.onDragStarted?.call();
      },
      onDragEnd: (details) {
        widget.onDragCompleted?.call();
      },
      child: widget.child,
    );
  }

  /// 構建拖拽時的反饋組件
  Widget _buildDragFeedback(List<AudioFile> files) {
    return Material(
      elevation: 8,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: AppTheme.musicColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.library_music,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              files.length == 1 
                ? files.first.title
                : '${files.length} 首歌曲',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建拖拽時的原位置組件
  Widget _buildChildWhenDragging() {
    return Opacity(
      opacity: 0.5,
      child: widget.child,
    );
  }
}

/// 音樂文件夾拖拽目標組件
/// 接收拖拽的音樂文件
class MusicFolderDropTarget extends StatefulWidget {
  final MusicFolder folder;
  final Widget child;
  final VoidCallback? onDropAccepted;

  const MusicFolderDropTarget({
    super.key,
    required this.folder,
    required this.child,
    this.onDropAccepted,
  });

  @override
  State<MusicFolderDropTarget> createState() => _MusicFolderDropTargetState();
}

class _MusicFolderDropTargetState extends State<MusicFolderDropTarget> {
  final MusicController controller = Get.find<MusicController>();
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    return DragTarget<List<AudioFile>>(
      onWillAcceptWithDetails: (details) {
        // 檢查是否可以接受拖拽
        if (details.data.isEmpty) return false;
        
        // 不能移動到當前文件夾
        if (widget.folder.id == controller.currentFolder?.id) return false;
        
        return true;
      },
      onAcceptWithDetails: (details) {
        // 執行移動操作
        controller.moveMusicToFolder(details.data, widget.folder.id);
        widget.onDropAccepted?.call();
        
        setState(() => _isHovering = false);
      },
      onMove: (details) {
        if (!_isHovering) {
          setState(() => _isHovering = true);
        }
      },
      onLeave: (data) {
        setState(() => _isHovering = false);
      },
      builder: (context, candidateData, rejectedData) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            border: _isHovering
                ? Border.all(
                    color: AppTheme.musicColor,
                    width: 2,
                  )
                : null,
            borderRadius: BorderRadius.circular(8),
            color: _isHovering
                ? AppTheme.musicColor.withValues(alpha: 0.1)
                : null,
          ),
          child: widget.child,
        );
      },
    );
  }
}

/// 拖拽移動對話框
/// 顯示可用的目標文件夾
class MusicDragMoveDialog extends StatelessWidget {
  final List<AudioFile> audioFiles;

  const MusicDragMoveDialog({
    super.key,
    required this.audioFiles,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MusicController>();

    return AlertDialog(
      title: Text('移動 ${audioFiles.length} 個音樂文件'),
      content: SizedBox(
        width: double.maxFinite,
        height: 300,
        child: Obx(() {
          final folders = controller.folders
              .where((f) => f.id != controller.currentFolder?.id)
              .toList();

          if (folders.isEmpty) {
            return const Center(
              child: Text('沒有可用的目標文件夾'),
            );
          }

          return ListView.builder(
            itemCount: folders.length,
            itemBuilder: (context, index) {
              final folder = folders[index];
              return MusicFolderDropTarget(
                folder: folder,
                onDropAccepted: () => Get.back(),
                child: Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: Icon(
                      folder.isSystem ? Icons.star : Icons.folder,
                      color: folder.color != null ? Color(folder.color!) : AppTheme.musicColor,
                    ),
                    title: Text(folder.name),
                    subtitle: folder.description != null
                        ? Text(folder.description!)
                        : null,
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      controller.moveMusicToFolder(audioFiles, folder.id);
                      Get.back();
                    },
                  ),
                ),
              );
            },
          );
        }),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('取消'),
        ),
      ],
    );
  }
}

/// 拖拽移動助手
/// 提供拖拽移動的便捷方法
class MusicDragMoveHelper {
  static void showMoveDialog(List<AudioFile> audioFiles) {
    Get.dialog(
      MusicDragMoveDialog(audioFiles: audioFiles),
      barrierDismissible: true,
    );
  }

  static void moveMusicToFolder(AudioFile audioFile, String folderId) {
    final controller = Get.find<MusicController>();
    controller.moveMusicToFolder([audioFile], folderId);
  }

  static void moveMusicListToFolder(List<AudioFile> audioFiles, String folderId) {
    final controller = Get.find<MusicController>();
    controller.moveMusicToFolder(audioFiles, folderId);
  }

  static bool canMoveToFolder(MusicFolder folder) {
    final controller = Get.find<MusicController>();
    
    // 不能移動到當前文件夾
    if (folder.id == controller.currentFolder?.id) return false;
    
    // 系統文件夾可能有特殊限制
    if (folder.isSystem) {
      // 這裡可以添加系統文件夾的特殊邏輯
      return true;
    }
    
    return true;
  }

  static List<MusicFolder> getAvailableTargetFolders() {
    final controller = Get.find<MusicController>();
    return controller.folders
        .where((f) => canMoveToFolder(f))
        .toList();
  }
}

/// 拖拽移動指示器
/// 顯示拖拽狀態的視覺指示
class MusicDragMoveIndicator extends StatelessWidget {
  final bool isActive;
  final String message;

  const MusicDragMoveIndicator({
    super.key,
    required this.isActive,
    this.message = '拖拽到文件夾以移動文件',
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: isActive ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.musicColor.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.touch_app,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              message,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
