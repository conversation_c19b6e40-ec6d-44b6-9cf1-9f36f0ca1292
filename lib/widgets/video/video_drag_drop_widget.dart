import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/video_controller.dart';
import '../../models/video_file.dart';
import '../../models/video_folder.dart';
import '../../utils/app_theme.dart';

/// 視頻拖拽移動組件
/// 支持將視頻文件拖拽到不同文件夾
class VideoDragDropWidget extends StatefulWidget {
  final Widget child;
  final VideoFile? videoFile;
  final List<VideoFile>? videoFiles;
  final VoidCallback? onDragStarted;
  final VoidCallback? onDragCompleted;

  const VideoDragDropWidget({
    super.key,
    required this.child,
    this.videoFile,
    this.videoFiles,
    this.onDragStarted,
    this.onDragCompleted,
  });

  @override
  State<VideoDragDropWidget> createState() => _VideoDragDropWidgetState();
}

class _VideoDragDropWidgetState extends State<VideoDragDropWidget> {
  final VideoController controller = Get.find<VideoController>();

  @override
  Widget build(BuildContext context) {
    final files =
        widget.videoFiles ??
        (widget.videoFile != null ? [widget.videoFile!] : <VideoFile>[]);

    if (files.isEmpty) {
      return widget.child;
    }

    return Draggable<List<VideoFile>>(
      data: files,
      feedback: _buildDragFeedback(files),
      childWhenDragging: _buildChildWhenDragging(),
      onDragStarted: () {
        widget.onDragStarted?.call();
      },
      onDragEnd: (details) {
        widget.onDragCompleted?.call();
      },
      child: widget.child,
    );
  }

  /// 構建拖拽時的反饋組件
  Widget _buildDragFeedback(List<VideoFile> files) {
    return Material(
      elevation: 8,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: AppTheme.videoColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.video_library, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              files.length == 1 ? files.first.title : '${files.length} 個視頻',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建拖拽時的原位置組件
  Widget _buildChildWhenDragging() {
    return Opacity(opacity: 0.5, child: widget.child);
  }
}

/// 文件夾拖拽目標組件
/// 接收拖拽的視頻文件
class FolderDropTarget extends StatefulWidget {
  final VideoFolder folder;
  final Widget child;
  final VoidCallback? onDropAccepted;

  const FolderDropTarget({
    super.key,
    required this.folder,
    required this.child,
    this.onDropAccepted,
  });

  @override
  State<FolderDropTarget> createState() => _FolderDropTargetState();
}

class _FolderDropTargetState extends State<FolderDropTarget> {
  final VideoController controller = Get.find<VideoController>();
  bool _isHovering = false;

  @override
  Widget build(BuildContext context) {
    return DragTarget<List<VideoFile>>(
      onWillAcceptWithDetails: (details) {
        // 檢查是否可以接受拖拽
        if (details.data.isEmpty) return false;

        // 不能移動到當前文件夾
        if (widget.folder.id == controller.currentFolder?.id) return false;

        return true;
      },
      onAcceptWithDetails: (details) {
        // 執行移動操作
        controller.moveVideosToFolder(details.data, widget.folder.id);
        widget.onDropAccepted?.call();

        setState(() => _isHovering = false);
      },
      onMove: (details) {
        if (!_isHovering) {
          setState(() => _isHovering = true);
        }
      },
      onLeave: (data) {
        setState(() => _isHovering = false);
      },
      builder: (context, candidateData, rejectedData) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            border: _isHovering
                ? Border.all(color: AppTheme.videoColor, width: 2)
                : null,
            borderRadius: BorderRadius.circular(8),
            color: _isHovering
                ? AppTheme.videoColor.withValues(alpha: 0.1)
                : null,
          ),
          child: widget.child,
        );
      },
    );
  }
}

/// 拖拽移動對話框
/// 顯示可用的目標文件夾
class DragMoveDialog extends StatelessWidget {
  final List<VideoFile> videoFiles;

  const DragMoveDialog({super.key, required this.videoFiles});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<VideoController>();

    return AlertDialog(
      title: Text('移動 ${videoFiles.length} 個視頻文件'),
      content: SizedBox(
        width: double.maxFinite,
        height: 300,
        child: Obx(() {
          final folders = controller.folders
              .where((f) => f.id != controller.currentFolder?.id)
              .toList();

          if (folders.isEmpty) {
            return const Center(child: Text('沒有可用的目標文件夾'));
          }

          return ListView.builder(
            itemCount: folders.length,
            itemBuilder: (context, index) {
              final folder = folders[index];
              return FolderDropTarget(
                folder: folder,
                onDropAccepted: () => Get.back(),
                child: Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: Icon(
                      folder.isSystem ? Icons.star : Icons.folder,
                      color: folder.color != null
                          ? Color(folder.color!)
                          : AppTheme.videoColor,
                    ),
                    title: Text(folder.name),
                    subtitle: folder.description != null
                        ? Text(folder.description!)
                        : null,
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      controller.moveVideosToFolder(videoFiles, folder.id);
                      Get.back();
                    },
                  ),
                ),
              );
            },
          );
        }),
      ),
      actions: [
        TextButton(onPressed: () => Get.back(), child: const Text('取消')),
      ],
    );
  }
}

/// 拖拽移動助手
/// 提供拖拽移動的便捷方法
class DragMoveHelper {
  static void showMoveDialog(List<VideoFile> videoFiles) {
    Get.dialog(
      DragMoveDialog(videoFiles: videoFiles),
      barrierDismissible: true,
    );
  }

  static void moveVideoToFolder(VideoFile videoFile, String folderId) {
    final controller = Get.find<VideoController>();
    controller.moveVideosToFolder([videoFile], folderId);
  }

  static void moveVideosToFolder(List<VideoFile> videoFiles, String folderId) {
    final controller = Get.find<VideoController>();
    controller.moveVideosToFolder(videoFiles, folderId);
  }

  static bool canMoveToFolder(VideoFolder folder) {
    final controller = Get.find<VideoController>();

    // 不能移動到當前文件夾
    if (folder.id == controller.currentFolder?.id) return false;

    // 系統文件夾可能有特殊限制
    if (folder.isSystem) {
      // 這裡可以添加系統文件夾的特殊邏輯
      return true;
    }

    return true;
  }

  static List<VideoFolder> getAvailableTargetFolders() {
    final controller = Get.find<VideoController>();
    return controller.folders.where((f) => canMoveToFolder(f)).toList();
  }
}

/// 拖拽移動指示器
/// 顯示拖拽狀態的視覺指示
class DragMoveIndicator extends StatelessWidget {
  final bool isActive;
  final String message;

  const DragMoveIndicator({
    super.key,
    required this.isActive,
    this.message = '拖拽到文件夾以移動文件',
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: isActive ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.videoColor.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.touch_app, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              message,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
