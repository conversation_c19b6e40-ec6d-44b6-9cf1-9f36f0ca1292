import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/video_controller.dart';
import '../../models/video_folder.dart';
import '../../utils/app_theme.dart';

/// 文件夾管理對話框
/// 提供文件夾的創建、重命名、刪除等操作
class FolderManagementDialog extends StatelessWidget {
  const FolderManagementDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<VideoController>();

    return Dialog(
      child: Container(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 標題欄
            Row(
              children: [
                const Icon(Icons.folder_open, color: AppTheme.videoColor),
                const SizedBox(width: 8),
                const Text(
                  '文件夾管理',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            const Divider(),

            // 操作按鈕
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () => _showCreateFolderDialog(context),
                  icon: const Icon(Icons.create_new_folder, size: 16),
                  label: const Text('新建文件夾'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.videoColor,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  onPressed: () => controller.loadFolders(),
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('刷新'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 文件夾列表
            Expanded(
              child: Obx(() {
                if (controller.folders.isEmpty) {
                  return const Center(child: Text('沒有文件夾'));
                }

                return ListView.builder(
                  itemCount: controller.folders.length,
                  itemBuilder: (context, index) {
                    final folder = controller.folders[index];
                    return _buildFolderItem(context, folder);
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建文件夾項目
  Widget _buildFolderItem(BuildContext context, VideoFolder folder) {
    final controller = Get.find<VideoController>();
    final isCurrentFolder = folder.id == controller.currentFolder?.id;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color:
                (folder.color != null
                        ? Color(folder.color!)
                        : AppTheme.videoColor)
                    .withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            folder.isSystem ? Icons.star : Icons.folder,
            color: folder.color != null
                ? Color(folder.color!)
                : AppTheme.videoColor,
          ),
        ),
        title: Text(
          folder.name,
          style: TextStyle(
            fontWeight: isCurrentFolder ? FontWeight.bold : FontWeight.normal,
            color: isCurrentFolder ? AppTheme.videoColor : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (folder.description != null) ...[
              Text(folder.description!),
              const SizedBox(height: 2),
            ],
            Text(
              folder.path,
              style: TextStyle(fontSize: 10, color: Colors.grey[600]),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        trailing: folder.isSystem
            ? Chip(
                label: const Text('系統', style: TextStyle(fontSize: 10)),
                backgroundColor: Colors.orange.withValues(alpha: 0.2),
                side: BorderSide.none,
              )
            : PopupMenuButton<String>(
                onSelected: (value) => _handleFolderAction(folder, value),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'open',
                    child: Row(
                      children: [
                        Icon(Icons.folder_open, size: 16),
                        SizedBox(width: 8),
                        Text('打開'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'rename',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('重命名'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('刪除', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
        onTap: () {
          controller.switchToFolder(folder.id);
          Get.back();
        },
      ),
    );
  }

  /// 處理文件夾操作
  void _handleFolderAction(VideoFolder folder, String action) {
    final controller = Get.find<VideoController>();

    switch (action) {
      case 'open':
        controller.switchToFolder(folder.id);
        Get.back();
        break;
      case 'rename':
        _showRenameFolderDialog(Get.context!, folder);
        break;
      case 'delete':
        _showDeleteFolderDialog(Get.context!, folder);
        break;
    }
  }

  /// 顯示創建文件夾對話框
  void _showCreateFolderDialog(BuildContext context) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('創建新文件夾'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: '文件夾名稱',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.folder),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: '描述（可選）',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: () {
              final name = nameController.text.trim();
              if (name.isNotEmpty) {
                final controller = Get.find<VideoController>();
                controller.createFolder(
                  name: name,
                  description: descriptionController.text.trim().isEmpty
                      ? null
                      : descriptionController.text.trim(),
                );
                Get.back();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.videoColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('創建'),
          ),
        ],
      ),
    );
  }

  /// 顯示重命名文件夾對話框
  void _showRenameFolderDialog(BuildContext context, VideoFolder folder) {
    final nameController = TextEditingController(text: folder.name);

    Get.dialog(
      AlertDialog(
        title: const Text('重命名文件夾'),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(
            labelText: '新名稱',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.edit),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: () {
              final newName = nameController.text.trim();
              if (newName.isNotEmpty && newName != folder.name) {
                final controller = Get.find<VideoController>();
                controller.renameFolder(folder.id, newName);
                Get.back();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.videoColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('重命名'),
          ),
        ],
      ),
    );
  }

  /// 顯示刪除文件夾對話框
  void _showDeleteFolderDialog(BuildContext context, VideoFolder folder) {
    Get.dialog(
      AlertDialog(
        title: const Text('刪除文件夾'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('確定要刪除文件夾 "${folder.name}" 嗎？'),
            const SizedBox(height: 16),
            const Text(
              '注意：',
              style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
            ),
            const Text(
              '• 文件夾中的視頻文件不會被刪除\n'
              '• 此操作無法撤銷',
              style: TextStyle(fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: () {
              final controller = Get.find<VideoController>();
              controller.deleteFolder(folder.id);
              Get.back();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('刪除'),
          ),
        ],
      ),
    );
  }
}
