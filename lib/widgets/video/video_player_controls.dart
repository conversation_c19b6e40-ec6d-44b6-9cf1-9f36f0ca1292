import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/video_player_controller.dart';
import '../../utils/app_theme.dart';

/// 視頻播放控制器組件
/// 提供視頻播放的完整控制界面
class VideoPlayerControls extends GetView<VideoPlayerController> {
  const VideoPlayerControls({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => GestureDetector(
        onTap: controller.toggleControls,
        child: Container(
          color: Colors.transparent,
          child: Stack(
            children: [
              // 控制器層
              AnimatedOpacity(
                opacity: controller.showControls ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 300),
                child: IgnorePointer(
                  ignoring: !controller.showControls,
                  child: Stack(
                    children: [
                      // 頂部控制欄
                      _buildTopControls(context),

                      // 底部控制欄
                      _buildBottomControls(context),

                      // 中央播放/暫停按鈕（僅在全屏時顯示）
                      if (controller.isFullscreen)
                        _buildCenterPlayButton(context),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建頂部控制欄
  Widget _buildTopControls(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      top: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black.withValues(alpha: 0.8), Colors.transparent],
          ),
        ),
        child: SafeArea(
          top: !controller.isFullscreen, // 橫屏時不使用頂部SafeArea
          child: Padding(
            padding: EdgeInsets.all(controller.isFullscreen ? 8 : 16),
            child: Row(
              children: [
                // 返回按鈕
                IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Get.back(),
                  tooltip: '返回',
                ),

                const SizedBox(width: 8),

                // 視頻標題
                Expanded(
                  child: Text(
                    controller.currentVideo?.title ?? '視頻播放器',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建底部控制欄
  Widget _buildBottomControls(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.transparent, Colors.black.withValues(alpha: 0.8)],
          ),
        ),
        child: SafeArea(
          bottom: !controller.isFullscreen, // 橫屏時不使用底部SafeArea
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 16,
                vertical: controller.isFullscreen ? 8 : 8,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 播放進度條
                  _buildProgressBar(context),
                  SizedBox(height: controller.isFullscreen ? 2 : 8),

                  // 主要控制按鈕
                  _buildMainControls(context),

                  // 次要控制項 - 在全屏模式下也顯示
                  const SizedBox(height: 8),
                  _buildSecondaryControls(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 構建中央播放/暫停按鈕
  Widget _buildCenterPlayButton(BuildContext context) {
    return Center(
      child: AnimatedOpacity(
        opacity: controller.showControls ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.6),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: controller.togglePlayPause,
            icon: Icon(
              controller.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 40,
            ),
          ),
        ),
      ),
    );
  }

  /// 構建播放進度條
  Widget _buildProgressBar(BuildContext context) {
    return Column(
      children: [
        // 進度條
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 3,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
            activeTrackColor: AppTheme.videoColor,
            inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
            thumbColor: AppTheme.videoColor,
            overlayColor: AppTheme.videoColor.withValues(alpha: 0.2),
          ),
          child: Slider(
            value: controller.progress,
            onChanged: (value) {
              final duration = controller.duration;
              if (duration > 0) {
                final position = Duration(
                  milliseconds: (duration * value).round(),
                );
                controller.seekTo(position);
              }
            },
            min: 0.0,
            max: 1.0,
            activeColor: AppTheme.videoColor,
            inactiveColor: Colors.white.withValues(alpha: 0.3),
          ),
        ),

        // 時間顯示
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(Duration(milliseconds: controller.position)),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontFamily: 'monospace',
                ),
              ),
              Text(
                _formatDuration(Duration(milliseconds: controller.duration)),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 構建主要控制按鈕
  Widget _buildMainControls(BuildContext context) {
    final isFullscreen = controller.isFullscreen;
    final iconSize = isFullscreen ? 20.0 : 24.0;
    final playButtonSize = isFullscreen ? 40.0 : 48.0;
    final playIconSize = isFullscreen ? 20.0 : 24.0;

    return SizedBox(
      height: isFullscreen ? 40 : 48,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 快退10秒
          IconButton(
            icon: Icon(Icons.replay_10, color: Colors.white, size: iconSize),
            onPressed: controller.seekBackward,
            tooltip: '快退10秒',
          ),

          // 播放/暫停按鈕
          Container(
            width: playButtonSize,
            height: playButtonSize,
            decoration: BoxDecoration(
              color: AppTheme.videoColor,
              borderRadius: BorderRadius.circular(playButtonSize / 2),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.videoColor.withValues(alpha: 0.3),
                  blurRadius: isFullscreen ? 4 : 8,
                  offset: Offset(0, isFullscreen ? 2 : 4),
                ),
              ],
            ),
            child: IconButton(
              onPressed: controller.togglePlayPause,
              icon: Icon(
                controller.isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size: playIconSize,
              ),
            ),
          ),

          // 快進10秒
          IconButton(
            icon: Icon(Icons.forward_10, color: Colors.white, size: iconSize),
            onPressed: controller.seekForward,
            tooltip: '快進10秒',
          ),
        ],
      ),
    );
  }

  /// 構建次要控制項
  Widget _buildSecondaryControls(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 播放速度
          IconButton(
            onPressed: () => _showSpeedDialog(),
            icon: const Icon(Icons.speed, color: Colors.white, size: 24),
            tooltip: '播放速度',
          ),

          // 全屏切換
          IconButton(
            onPressed: controller.toggleFullscreen,
            icon: Icon(
              controller.isFullscreen
                  ? Icons.fullscreen_exit
                  : Icons.fullscreen,
              color: Colors.white,
              size: 24,
            ),
            tooltip: controller.isFullscreen ? '退出全屏' : '全屏',
          ),
        ],
      ),
    );
  }

  /// 顯示播放速度選擇對話框
  void _showSpeedDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('播放速度'),
        content: SizedBox(
          width: double.minPositive,
          child: ListView(
            shrinkWrap: true,
            children: [0.5, 0.75, 1.0, 1.25, 1.5, 2.0].map((speed) {
              return ListTile(
                title: Text('${speed}x'),
                onTap: () {
                  controller.setPlaybackSpeed(speed);
                  Get.back();
                },
                trailing: controller.playbackSpeed == speed
                    ? const Icon(Icons.check, color: AppTheme.videoColor)
                    : null,
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  /// 格式化時間顯示
  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    }
  }
}
