import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/video_playlist.dart';
import '../../models/video_file.dart';
import '../../utils/app_theme.dart';
import '../common/custom_button.dart';

/// 視頻播放列表組件
/// 顯示播放列表信息和視頻列表
class VideoPlaylistWidget extends StatelessWidget {
  /// 播放列表
  final VideoPlaylist playlist;
  
  /// 播放視頻回調
  final Function(VideoFile videoFile)? onPlayVideo;
  
  /// 移除視頻回調
  final Function(VideoFile videoFile)? onRemoveVideo;
  
  /// 移動視頻回調
  final Function(int oldIndex, int newIndex)? onMoveVideo;
  
  /// 是否可編輯
  final bool editable;

  const VideoPlaylistWidget({
    super.key,
    required this.playlist,
    this.onPlayVideo,
    this.onRemoveVideo,
    this.onMoveVideo,
    this.editable = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 播放列表信息
        _buildPlaylistHeader(context),
        
        // 視頻列表
        Expanded(
          child: _buildVideoList(context),
        ),
      ],
    );
  }

  /// 構建播放列表頭部
  Widget _buildPlaylistHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.videoColor.withValues(alpha: 0.1),
            AppTheme.videoColor.withValues(alpha: 0.05),
          ],
        ),
      ),
      child: Row(
        children: [
          // 封面圖片
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppTheme.videoColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppRadius.md),
            ),
            child: playlist.coverImagePath != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(AppRadius.md),
                    child: Image.file(
                      File(playlist.coverImagePath!),
                      fit: BoxFit.cover,
                    ),
                  )
                : const Icon(
                    Icons.video_library,
                    size: 40,
                    color: AppTheme.videoColor,
                  ),
          ),
          
          const SizedBox(width: AppSpacing.lg),
          
          // 播放列表信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  playlist.name,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.videoColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                
                if (playlist.description != null) ...[
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    playlist.description!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                
                const SizedBox(height: AppSpacing.sm),
                
                Row(
                  children: [
                    Icon(
                      Icons.video_library,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Text(
                      '${playlist.videoCount} 個視頻',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.md),
                    Icon(
                      Icons.access_time,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Text(
                      playlist.formattedTotalDuration,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建視頻列表
  Widget _buildVideoList(BuildContext context) {
    if (playlist.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.video_library_outlined,
              size: 80,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              '播放列表為空',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              '添加一些視頻來開始播放',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return editable && onMoveVideo != null
        ? ReorderableListView.builder(
            padding: const EdgeInsets.all(AppSpacing.sm),
            itemCount: playlist.videoFiles.length,
            onReorder: (oldIndex, newIndex) {
              if (newIndex > oldIndex) newIndex--;
              onMoveVideo?.call(oldIndex, newIndex);
            },
            itemBuilder: (context, index) {
              final videoFile = playlist.videoFiles[index];
              return _buildVideoItem(context, videoFile, index, key: ValueKey(videoFile.path));
            },
          )
        : ListView.builder(
            padding: const EdgeInsets.all(AppSpacing.sm),
            itemCount: playlist.videoFiles.length,
            itemBuilder: (context, index) {
              final videoFile = playlist.videoFiles[index];
              return _buildVideoItem(context, videoFile, index);
            },
          );
  }

  /// 構建視頻項目
  Widget _buildVideoItem(
    BuildContext context,
    VideoFile videoFile,
    int index, {
    Key? key,
  }) {
    return Card(
      key: key,
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.xs,
        vertical: AppSpacing.xs,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        
        // 序號或拖拽手柄
        leading: editable && onMoveVideo != null
            ? ReorderableDragStartListener(
                index: index,
                child: const Icon(Icons.drag_handle),
              )
            : Container(
                width: 60,
                height: 40,
                decoration: BoxDecoration(
                  color: AppTheme.videoColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppRadius.sm),
                ),
                child: videoFile.thumbnailPath != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(AppRadius.sm),
                        child: Image.file(
                          File(videoFile.thumbnailPath!),
                          fit: BoxFit.cover,
                        ),
                      )
                    : Center(
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            color: AppTheme.videoColor,
                          ),
                        ),
                      ),
              ),
        
        // 視頻信息
        title: Text(
          videoFile.title,
          style: Theme.of(context).textTheme.titleMedium,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (videoFile.description != null) ...[
              Text(
                videoFile.description!,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppSpacing.xs),
            ],
            Row(
              children: [
                Text(
                  videoFile.formattedDuration,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontFamily: 'monospace',
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  videoFile.formattedFileSize,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                if (videoFile.resolutionString != '未知') ...[
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    videoFile.resolutionString,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
            
            // 播放進度條（如果有播放記錄）
            if (videoFile.hasPlayHistory) ...[
              const SizedBox(height: AppSpacing.xs),
              Row(
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: videoFile.playProgress,
                      backgroundColor: Colors.grey.withValues(alpha: 0.3),
                      valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.videoColor),
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    '${(videoFile.playProgress * 100).round()}%',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.videoColor,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
        
        // 操作按鈕
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 播放按鈕
            IconButton(
              icon: const Icon(Icons.play_arrow),
              onPressed: () => onPlayVideo?.call(videoFile),
              tooltip: '播放',
            ),
            
            // 移除按鈕
            if (editable && onRemoveVideo != null)
              IconButton(
                icon: const Icon(Icons.remove_circle_outline),
                onPressed: () => onRemoveVideo?.call(videoFile),
                tooltip: '移除',
              ),
          ],
        ),
        
        // 點擊播放
        onTap: () => onPlayVideo?.call(videoFile),
      ),
    );
  }
}

/// 視頻播放列表創建對話框
class VideoPlaylistCreateDialog extends StatefulWidget {
  /// 創建回調
  final Function(String name, String? description, VideoPlaylistMode? playMode)? onCreate;

  const VideoPlaylistCreateDialog({
    super.key,
    this.onCreate,
  });

  @override
  State<VideoPlaylistCreateDialog> createState() => _VideoPlaylistCreateDialogState();
}

class _VideoPlaylistCreateDialogState extends State<VideoPlaylistCreateDialog> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  VideoPlaylistMode _selectedPlayMode = VideoPlaylistMode.sequence;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('創建播放列表'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: '播放列表名稱',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '請輸入播放列表名稱';
                }
                return null;
              },
              autofocus: true,
            ),
            const SizedBox(height: AppSpacing.md),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: '描述（可選）',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: AppSpacing.md),
            DropdownButtonFormField<VideoPlaylistMode>(
              value: _selectedPlayMode,
              decoration: const InputDecoration(
                labelText: '播放模式',
                border: OutlineInputBorder(),
              ),
              items: VideoPlaylistMode.values.map((mode) {
                return DropdownMenuItem(
                  value: mode,
                  child: Text(mode.displayName),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedPlayMode = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('取消'),
        ),
        CustomButton(
          text: '創建',
          onPressed: _createPlaylist,
        ),
      ],
    );
  }

  void _createPlaylist() {
    if (_formKey.currentState?.validate() == true) {
      final name = _nameController.text.trim();
      final description = _descriptionController.text.trim();
      widget.onCreate?.call(
        name,
        description.isEmpty ? null : description,
        _selectedPlayMode,
      );
      Get.back();
    }
  }
}
