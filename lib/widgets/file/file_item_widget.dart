import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/file_item.dart';
import '../../utils/app_theme.dart';

/// 文件項目組件
/// 顯示單個文件的信息和操作
class FileItemWidget extends StatelessWidget {
  /// 文件項目
  final FileItem fileItem;

  /// 是否為選中狀態
  final bool isSelected;

  /// 顯示模式
  final FileDisplayMode displayMode;

  /// 點擊回調
  final VoidCallback? onTap;

  /// 長按回調
  final VoidCallback? onLongPress;

  /// 選擇回調
  final Function(bool selected)? onSelectionChanged;

  /// 收藏回調
  final VoidCallback? onFavoriteToggle;

  /// 更多操作回調
  final VoidCallback? onMoreActions;

  const FileItemWidget({
    super.key,
    required this.fileItem,
    this.isSelected = false,
    this.displayMode = FileDisplayMode.list,
    this.onTap,
    this.onLongPress,
    this.onSelectionChanged,
    this.onFavoriteToggle,
    this.onMoreActions,
  });

  @override
  Widget build(BuildContext context) {
    switch (displayMode) {
      case FileDisplayMode.grid:
        return _buildGridItem(context);
      case FileDisplayMode.list:
        return _buildListItem(context);
    }
  }

  /// 構建網格項目
  Widget _buildGridItem(BuildContext context) {
    return Card(
      elevation: isSelected ? 8 : 2,
      color: isSelected ? Theme.of(context).colorScheme.primaryContainer : null,
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(AppRadius.md),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.sm),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 文件圖標和選擇框
              Row(
                children: [
                  Expanded(child: _buildFileIcon(context, size: 48)),
                  if (onSelectionChanged != null)
                    Checkbox(
                      value: isSelected,
                      onChanged: (value) =>
                          onSelectionChanged?.call(value ?? false),
                    ),
                ],
              ),

              const SizedBox(height: AppSpacing.sm),

              // 文件名
              Text(
                fileItem.nameWithoutExtension,
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: AppSpacing.xs),

              // 文件信息
              Text(
                '${fileItem.formattedFileSize} • ${fileItem.type.displayName}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              const Spacer(),

              // 操作按鈕
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (fileItem.isFavorite)
                    Icon(Icons.favorite, size: 16, color: Colors.red),
                  if (onMoreActions != null)
                    IconButton(
                      icon: const Icon(Icons.more_vert, size: 16),
                      onPressed: onMoreActions,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 24,
                        minHeight: 24,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建列表項目
  Widget _buildListItem(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 1,
      color: isSelected ? Theme.of(context).colorScheme.primaryContainer : null,
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.xs,
        ),

        // 文件圖標
        leading: _buildFileIcon(context),

        // 文件信息
        title: Text(
          fileItem.fileName,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${fileItem.formattedFileSize} • ${fileItem.type.displayName}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              '修改於 ${fileItem.formattedModifiedTime}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontSize: 11,
              ),
            ),

            // 標籤
            if (fileItem.hasTags) ...[
              const SizedBox(height: AppSpacing.xs),
              Wrap(
                spacing: AppSpacing.xs,
                children: fileItem.tags.take(3).map((tag) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.xs,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.secondaryContainer,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      tag,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSecondaryContainer,
                        fontSize: 10,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),

        // 操作按鈕
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 收藏按鈕
            if (onFavoriteToggle != null)
              IconButton(
                icon: Icon(
                  fileItem.isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: fileItem.isFavorite ? Colors.red : null,
                ),
                onPressed: onFavoriteToggle,
                tooltip: fileItem.isFavorite ? '取消收藏' : '添加收藏',
              ),

            // 選擇框
            if (onSelectionChanged != null)
              Checkbox(
                value: isSelected,
                onChanged: (value) => onSelectionChanged?.call(value ?? false),
              ),

            // 更多操作
            if (onMoreActions != null)
              IconButton(
                icon: const Icon(Icons.more_vert),
                onPressed: onMoreActions,
                tooltip: '更多操作',
              ),
          ],
        ),

        onTap: onTap,
        onLongPress: onLongPress,
      ),
    );
  }

  /// 構建文件圖標
  Widget _buildFileIcon(BuildContext context, {double size = 40}) {
    Color iconColor;
    IconData iconData;

    switch (fileItem.type) {
      case FileType.audio:
        iconColor = AppTheme.musicColor;
        iconData = Icons.audio_file;
        break;
      case FileType.video:
        iconColor = AppTheme.videoColor;
        iconData = Icons.video_file;
        break;
      case FileType.pdf:
        iconColor = AppTheme.pdfColor;
        iconData = Icons.picture_as_pdf;
        break;
      case FileType.image:
        iconColor = Colors.green;
        iconData = Icons.image;
        break;
      case FileType.document:
        iconColor = Colors.blue;
        iconData = Icons.description;
        break;
      case FileType.archive:
        iconColor = Colors.orange;
        iconData = Icons.archive;
        break;
      case FileType.unknown:
        iconColor = Colors.grey;
        iconData = Icons.insert_drive_file;
        break;
    }

    // 如果有縮略圖，顯示縮略圖
    if (fileItem.thumbnailPath != null &&
        File(fileItem.thumbnailPath!).existsSync()) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.sm),
          image: DecorationImage(
            image: FileImage(File(fileItem.thumbnailPath!)),
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    // 顯示類型圖標
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.sm),
      ),
      child: Icon(iconData, size: size * 0.6, color: iconColor),
    );
  }
}

/// 文件顯示模式
enum FileDisplayMode { list, grid }

/// 文件操作對話框
class FileActionDialog extends StatelessWidget {
  final FileItem fileItem;
  final VoidCallback? onOpen;
  final VoidCallback? onRename;
  final VoidCallback? onMove;
  final VoidCallback? onCopy;
  final VoidCallback? onDelete;
  final VoidCallback? onProperties;

  const FileActionDialog({
    super.key,
    required this.fileItem,
    this.onOpen,
    this.onRename,
    this.onMove,
    this.onCopy,
    this.onDelete,
    this.onProperties,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(fileItem.fileName),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (onOpen != null)
            ListTile(
              leading: const Icon(Icons.open_in_new),
              title: const Text('打開'),
              onTap: () {
                Get.back();
                onOpen?.call();
              },
            ),
          if (onRename != null)
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('重命名'),
              onTap: () {
                Get.back();
                onRename?.call();
              },
            ),
          if (onMove != null)
            ListTile(
              leading: const Icon(Icons.drive_file_move),
              title: const Text('移動'),
              onTap: () {
                Get.back();
                onMove?.call();
              },
            ),
          if (onCopy != null)
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('複製'),
              onTap: () {
                Get.back();
                onCopy?.call();
              },
            ),
          if (onDelete != null)
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('刪除', style: TextStyle(color: Colors.red)),
              onTap: () {
                Get.back();
                onDelete?.call();
              },
            ),
          if (onProperties != null)
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('屬性'),
              onTap: () {
                Get.back();
                onProperties?.call();
              },
            ),
        ],
      ),
      actions: [
        TextButton(onPressed: () => Get.back(), child: const Text('取消')),
      ],
    );
  }
}
