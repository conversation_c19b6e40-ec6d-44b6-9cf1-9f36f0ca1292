import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../routes/app_routes.dart';

class HomeController extends GetxController {
  // 導航項目列表
  final List<NavigationItem> navigationItems = [
    NavigationItem(
      title: '音樂',
      subtitle: '播放音樂文件',
      icon: '🎵',
      route: AppRoutes.music,
      color: const Color(0xFF6366F1),
    ),
    NavigationItem(
      title: '視頻',
      subtitle: '播放視頻文件',
      icon: '🎬',
      route: AppRoutes.video,
      color: const Color(0xFFEF4444),
    ),
    NavigationItem(
      title: '漫畫',
      subtitle: '閱讀PDF漫畫',
      icon: '📚',
      route: AppRoutes.pdf,
      color: const Color(0xFF10B981),
    ),
    NavigationItem(
      title: '文件服務器',
      subtitle: '上傳和管理文件',
      icon: '🌐',
      route: AppRoutes.server,
      color: const Color(0xFFF59E0B),
    ),
    NavigationItem(
      title: '文件管理',
      subtitle: '管理本地文件',
      icon: '📁',
      route: AppRoutes.fileManager,
      color: const Color(0xFF8B5CF6),
    ),
  ];

  void navigateToPage(String route) {
    Get.toNamed(route);
  }
}

class NavigationItem {
  final String title;
  final String subtitle;
  final String icon;
  final String route;
  final Color color;

  NavigationItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.route,
    required this.color,
  });
}
