import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/audio_file.dart';
import '../models/music_folder.dart';
import '../models/player_state.dart' as app_state;
import '../models/lyric.dart';
import '../models/playlist.dart';
import '../services/audio_player_service.dart';
import '../services/storage_service.dart';
import '../services/playlist_service.dart';
import '../services/music_folder_service.dart';
import '../services/audio_metadata_service.dart';
import '../utils/logger.dart';

/// 音樂控制器
/// 管理音樂播放相關的UI狀態和用戶交互
class MusicController extends GetxController {
  final AudioPlayerService _audioPlayerService = Get.find<AudioPlayerService>();
  final StorageService _storageService = Get.find<StorageService>();
  final PlaylistService _playlistService = Get.find<PlaylistService>();
  final MusicFolderService _folderService = Get.find<MusicFolderService>();
  final AudioMetadataService _metadataService =
      Get.find<AudioMetadataService>();

  // 音樂文件列表
  final _audioFiles = <AudioFile>[].obs;
  final _isLoading = false.obs;
  final _selectedFiles = <AudioFile>[].obs;
  final _isSelectionMode = false.obs;

  // 文件夾相關
  final _folders = <MusicFolder>[].obs;
  final _currentFolder = Rxn<MusicFolder>();

  // Getters
  List<AudioFile> get audioFiles => _audioFiles.toList();
  bool get isLoading => _isLoading.value;
  List<AudioFile> get selectedFiles => _selectedFiles.toList();
  bool get isSelectionMode => _isSelectionMode.value;

  // 文件夾相關getters
  List<MusicFolder> get folders => _folders.toList();
  MusicFolder? get currentFolder => _currentFolder.value;

  // 歌詞和播放狀態相關getters
  List<LyricLine>? get currentLyrics => _audioPlayerService.currentLyric?.lines;
  Duration get position => Duration(milliseconds: playerState.currentPosition);

  // 播放器狀態
  app_state.AudioPlayerState get playerState => _audioPlayerService.playerState;
  AudioFile? get currentAudio => _audioPlayerService.currentAudio;
  bool get isPlaying => playerState.playerState.isPlaying;
  bool get isPaused => playerState.playerState.isPaused;
  bool get isShuffleEnabled => _audioPlayerService.isShuffleEnabled;
  app_state.PlayMode get currentPlayMode => playerState.playMode;

  // 歌詞相關
  Lyric? get currentLyric => _audioPlayerService.currentLyric;
  LyricLine? get currentLyricLine => _audioPlayerService.getCurrentLyricLine();

  // 播放列表相關
  List<Playlist> get playlists => _playlistService.playlists;
  Playlist? get favoritesPlaylist => _playlistService.favoritesPlaylist;
  Playlist? get recentPlaylist => _playlistService.recentPlaylist;

  @override
  void onInit() {
    super.onInit();
    _loadAudioFiles();
    loadFolders();
  }

  /// 載入音樂文件
  Future<void> loadAudioFiles() async {
    await _loadAudioFiles();
  }

  /// 內部載入音樂文件方法
  Future<void> _loadAudioFiles() async {
    try {
      _isLoading.value = true;

      final files = await _storageService.getMusicFiles();
      final audioFileList = <AudioFile>[];

      // 使用元數據服務獲取完整信息（包括時長）
      for (final file in files) {
        final audioFile = await _metadataService.parseAudioMetadata(file);
        audioFileList.add(audioFile);
      }

      // 按文件名排序
      audioFileList.sort((a, b) => a.fileName.compareTo(b.fileName));

      _audioFiles.value = audioFileList;

      // 保存元數據緩存
      await _metadataService.saveCache();
    } catch (e) {
      Get.snackbar('載入失敗', '無法載入音樂文件: $e', snackPosition: SnackPosition.BOTTOM);
    } finally {
      _isLoading.value = false;
    }
  }

  /// 播放指定音樂
  Future<void> playAudio(AudioFile audioFile) async {
    final success = await _audioPlayerService.playPlaylist(
      audioFiles,
      startIndex: audioFiles.indexOf(audioFile),
    );

    if (!success) {
      Get.snackbar('播放失敗', '無法播放該音樂文件', snackPosition: SnackPosition.BOTTOM);
    }
  }

  /// 播放/暫停切換
  Future<void> togglePlayPause() async {
    await _audioPlayerService.togglePlayPause();
  }

  /// 播放上一首
  Future<void> playPrevious() async {
    await _audioPlayerService.playPrevious();
  }

  /// 播放下一首
  Future<void> playNext() async {
    await _audioPlayerService.playNext();
  }

  /// 停止播放
  Future<void> stop() async {
    await _audioPlayerService.stop();
  }

  /// 跳轉到指定位置
  Future<void> seekTo(double progress) async {
    final position = Duration(
      milliseconds: (playerState.totalDuration * progress).round(),
    );
    await _audioPlayerService.seekTo(position);
  }

  /// 設置音量
  Future<void> setVolume(double volume) async {
    await _audioPlayerService.setVolume(volume);
  }

  /// 切換播放模式
  void togglePlayMode() {
    _audioPlayerService.togglePlayMode();
  }

  /// 進入選擇模式
  void enterSelectionMode() {
    _isSelectionMode.value = true;
    _selectedFiles.clear();
  }

  /// 退出選擇模式
  void exitSelectionMode() {
    _isSelectionMode.value = false;
    _selectedFiles.clear();
  }

  /// 切換文件選擇狀態
  void toggleFileSelection(AudioFile audioFile) {
    if (_selectedFiles.contains(audioFile)) {
      _selectedFiles.remove(audioFile);
    } else {
      _selectedFiles.add(audioFile);
    }
  }

  /// 全選
  void selectAll() {
    _selectedFiles.value = audioFiles.toList();
  }

  /// 取消全選
  void deselectAll() {
    _selectedFiles.clear();
  }

  /// 刪除選中的文件
  Future<void> deleteSelectedFiles() async {
    if (selectedFiles.isEmpty) return;

    try {
      final filesToDelete = selectedFiles.toList();

      // 確認對話框
      final confirmed =
          await Get.dialog<bool>(
            AlertDialog(
              title: const Text('確認刪除'),
              content: Text('確定要刪除 ${filesToDelete.length} 個音樂文件嗎？'),
              actions: [
                TextButton(
                  onPressed: () => Get.back(result: false),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () => Get.back(result: true),
                  child: const Text('刪除'),
                ),
              ],
            ),
          ) ??
          false;

      if (!confirmed) return;

      // 刪除文件
      int deletedCount = 0;
      for (final audioFile in filesToDelete) {
        final file = File(audioFile.path);
        final success = await _storageService.deleteFile(file);
        if (success) {
          deletedCount++;
          _audioFiles.remove(audioFile);
        }
      }

      exitSelectionMode();

      Get.snackbar(
        '刪除完成',
        '成功刪除 $deletedCount 個文件',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        '刪除失敗',
        '刪除文件時發生錯誤: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// 播放選中的文件
  Future<void> playSelectedFiles() async {
    if (selectedFiles.isEmpty) return;

    final success = await _audioPlayerService.playPlaylist(selectedFiles);

    if (success) {
      exitSelectionMode();
      Get.snackbar(
        '開始播放',
        '正在播放選中的 ${selectedFiles.length} 首歌曲',
        snackPosition: SnackPosition.BOTTOM,
      );
    } else {
      Get.snackbar('播放失敗', '無法播放選中的文件', snackPosition: SnackPosition.BOTTOM);
    }
  }

  /// 獲取格式化的播放時間
  String get formattedCurrentTime => playerState.formattedCurrentPosition;

  /// 獲取格式化的總時間
  String get formattedTotalTime => playerState.formattedTotalDuration;

  /// 獲取播放進度
  double get playProgress => playerState.progress;

  /// 檢查文件是否被選中
  bool isFileSelected(AudioFile audioFile) {
    return _selectedFiles.contains(audioFile);
  }

  /// 檢查是否為當前播放的文件
  bool isCurrentlyPlaying(AudioFile audioFile) {
    return currentAudio?.path == audioFile.path && isPlaying;
  }

  /// 檢查是否為當前音頻文件
  bool isCurrentAudio(AudioFile audioFile) {
    return currentAudio?.path == audioFile.path;
  }

  /// 跳轉到歌詞時間點
  Future<void> seekToLyricTime(int timestamp) async {
    await _audioPlayerService.seekToLyricTime(timestamp);
  }

  /// 創建播放列表
  Future<void> createPlaylist(String name, {String? description}) async {
    try {
      await _playlistService.createPlaylist(
        name: name,
        description: description,
      );
      Get.snackbar('創建成功', '播放列表 "$name" 已創建');
    } catch (e) {
      Get.snackbar('創建失敗', '無法創建播放列表: $e');
    }
  }

  /// 添加到我的最愛
  Future<void> addToFavorites(AudioFile audioFile) async {
    try {
      final success = await _playlistService.addToFavorites(audioFile);
      if (success) {
        Get.snackbar('添加成功', '已添加到我的最愛');
      } else {
        Get.snackbar('添加失敗', '無法添加到我的最愛');
      }
    } catch (e) {
      Get.snackbar('添加失敗', '添加到我的最愛時發生錯誤: $e');
    }
  }

  /// 從我的最愛移除
  Future<void> removeFromFavorites(AudioFile audioFile) async {
    try {
      final success = await _playlistService.removeFromFavorites(audioFile);
      if (success) {
        Get.snackbar('移除成功', '已從我的最愛移除');
      } else {
        Get.snackbar('移除失敗', '無法從我的最愛移除');
      }
    } catch (e) {
      Get.snackbar('移除失敗', '從我的最愛移除時發生錯誤: $e');
    }
  }

  /// 檢查是否在我的最愛中
  bool isInFavorites(AudioFile audioFile) {
    return _playlistService.isInFavorites(audioFile);
  }

  /// 添加到播放列表
  Future<void> addToPlaylist(
    String playlistId,
    List<AudioFile> audioFiles,
  ) async {
    try {
      final success = await _playlistService.addAudiosToPlaylist(
        playlistId,
        audioFiles,
      );
      if (success) {
        Get.snackbar('添加成功', '已添加 ${audioFiles.length} 首歌曲到播放列表');
      } else {
        Get.snackbar('添加失敗', '無法添加到播放列表');
      }
    } catch (e) {
      Get.snackbar('添加失敗', '添加到播放列表時發生錯誤: $e');
    }
  }

  /// 載入播放列表
  Future<void> loadPlaylists() async {
    await _playlistService.loadPlaylists();
  }

  // ==================== 文件夾管理 ====================

  /// 載入文件夾列表
  Future<void> loadFolders() async {
    try {
      _isLoading.value = true;
      final folders = await _folderService.loadFolders();
      _folders.value = folders;

      // 設置默認當前文件夾為根文件夾
      if (_currentFolder.value == null && folders.isNotEmpty) {
        _currentFolder.value = folders.firstWhere(
          (f) => f.id == 'root',
          orElse: () => folders.first,
        );
      }
    } catch (e) {
      Logger.error('Failed to load music folders: $e');
      Get.snackbar('載入失敗', '無法載入文件夾: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// 創建新文件夾
  Future<void> createFolder({
    required String name,
    String? parentId,
    String? description,
    int? color,
    String? icon,
  }) async {
    try {
      final folder = await _folderService.createFolder(
        name: name,
        parentId: parentId,
        description: description,
        color: color,
        icon: icon,
      );

      _folders.add(folder);
      Get.snackbar('創建成功', '文件夾 "$name" 已創建');
    } catch (e) {
      Logger.error('Failed to create music folder: $e');
      Get.snackbar('創建失敗', '無法創建文件夾: $e');
    }
  }

  /// 重命名文件夾
  Future<void> renameFolder(String folderId, String newName) async {
    try {
      final updatedFolder = await _folderService.renameFolder(
        folderId,
        newName,
      );

      final index = _folders.indexWhere((f) => f.id == folderId);
      if (index != -1) {
        _folders[index] = updatedFolder;
      }

      // 更新當前文件夾
      if (_currentFolder.value?.id == folderId) {
        _currentFolder.value = updatedFolder;
      }

      Get.snackbar('重命名成功', '文件夾已重命名為 "$newName"');
    } catch (e) {
      Logger.error('Failed to rename music folder: $e');
      Get.snackbar('重命名失敗', '無法重命名文件夾: $e');
    }
  }

  /// 刪除文件夾
  Future<void> deleteFolder(String folderId, {bool deleteFiles = false}) async {
    try {
      final success = await _folderService.deleteFolder(
        folderId,
        deleteFiles: deleteFiles,
      );

      if (success) {
        _folders.removeWhere((f) => f.id == folderId);

        // 如果刪除的是當前文件夾，切換到根文件夾
        if (_currentFolder.value?.id == folderId) {
          if (_folders.isNotEmpty) {
            _currentFolder.value = _folders.firstWhere(
              (f) => f.id == 'root',
              orElse: () => _folders.first,
            );
          } else {
            _currentFolder.value = null;
          }
        }

        Get.snackbar('刪除成功', '文件夾已刪除');
      }
    } catch (e) {
      Logger.error('Failed to delete music folder: $e');
      Get.snackbar('刪除失敗', '無法刪除文件夾: $e');
    }
  }

  /// 切換當前文件夾
  Future<void> switchToFolder(String folderId) async {
    try {
      final folder = _folders.firstWhere(
        (f) => f.id == folderId,
        orElse: () => throw Exception('文件夾不存在'),
      );

      _currentFolder.value = folder;

      // 載入該文件夾的音樂文件
      await loadFolderMusic(folderId);
    } catch (e) {
      Logger.error('Failed to switch music folder: $e');
      Get.snackbar('切換失敗', '無法切換到該文件夾: $e');
    }
  }

  /// 載入文件夾中的音樂文件
  Future<void> loadFolderMusic(String folderId) async {
    try {
      _isLoading.value = true;

      // 如果是"所有音樂"系統文件夾，載入所有音樂文件
      if (folderId == 'root') {
        await _loadAudioFiles();
      } else {
        // 其他文件夾載入特定文件夾的音樂
        final music = await _folderService.getFolderMusic(folderId);
        _audioFiles.value = music;
      }
    } catch (e) {
      Logger.error('Failed to load folder music: $e');
      Get.snackbar('載入失敗', '無法載入文件夾音樂: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// 移動音樂文件到文件夾
  Future<void> moveMusicToFolder(
    List<AudioFile> music,
    String targetFolderId,
  ) async {
    try {
      int successCount = 0;

      for (final audioFile in music) {
        final success = await _folderService.moveMusicToFolder(
          audioFile,
          targetFolderId,
        );
        if (success) {
          successCount++;
          _audioFiles.remove(audioFile);
        }
      }

      if (successCount > 0) {
        Get.snackbar('移動成功', '成功移動 $successCount 個音樂文件');

        // 如果在選擇模式，退出選擇模式
        if (_isSelectionMode.value) {
          exitSelectionMode();
        }
      } else {
        Get.snackbar('移動失敗', '沒有文件被移動');
      }
    } catch (e) {
      Logger.error('Failed to move music to folder: $e');
      Get.snackbar('移動失敗', '無法移動音樂文件: $e');
    }
  }

  /// 格式化時長顯示
  String formatDuration(int? milliseconds) {
    if (milliseconds == null) return '--:--';

    final duration = Duration(milliseconds: milliseconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    // 超過60分鐘顯示小時:分鐘:秒鐘格式
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }

    // 不超過60分鐘顯示分鐘:秒鐘格式
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
