import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../services/http_server_service.dart';
import '../services/storage_service.dart';
import '../utils/logger.dart';
import '../utils/server_diagnostics.dart';

/// 服務器控制器
/// 管理HTTP文件服務器的啟動、停止和狀態
class ServerController extends GetxController {
  final HttpServerService _httpServerService = Get.find<HttpServerService>();
  final StorageService _storageService = Get.find<StorageService>();

  // 服務器狀態
  RxBool get isServerRunning => _httpServerService.isRunning.obs;

  // 服務器URL（響應式）
  String? get serverUrl => _httpServerService.serverUrl;

  // 服務器地址（響應式）
  Rx<String?> get serverAddress => _httpServerService.serverAddressRx;

  // 服務器端口
  int get serverPort => _httpServerService.serverPort;

  // 文件統計
  final _musicFileCount = 0.obs;
  final _videoFileCount = 0.obs;
  final _pdfFileCount = 0.obs;

  int get musicFileCount => _musicFileCount.value;
  int get videoFileCount => _videoFileCount.value;
  int get pdfFileCount => _pdfFileCount.value;

  @override
  void onInit() {
    super.onInit();
    _loadFileStatistics();
  }

  @override
  void onClose() {
    // 停止服務器
    stopServer();
    super.onClose();
  }

  /// 啟動服務器
  Future<bool> startServer() async {
    try {
      final success = await _httpServerService.startServer();
      if (success) {
        Get.snackbar(
          '服務器啟動成功',
          '服務器地址: $serverUrl',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 3),
        );
      } else {
        Get.snackbar(
          '服務器啟動失敗',
          '請檢查網絡設置或端口是否被占用',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 3),
        );
      }
      return success;
    } catch (e) {
      Get.snackbar(
        '服務器啟動錯誤',
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
      return false;
    }
  }

  /// 停止服務器
  Future<void> stopServer() async {
    try {
      await _httpServerService.stopServer();
      Get.snackbar(
        '服務器已停止',
        '文件上傳服務已關閉',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        '停止服務器錯誤',
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 切換服務器狀態
  Future<void> toggleServer() async {
    if (isServerRunning.value) {
      await stopServer();
    } else {
      await startServer();
    }
  }

  /// 刷新文件統計
  Future<void> refreshFileStatistics() async {
    await _loadFileStatistics();
  }

  /// 加載文件統計信息
  Future<void> _loadFileStatistics() async {
    try {
      final musicFiles = await _storageService.getMusicFiles();
      final videoFiles = await _storageService.getVideoFiles();
      final pdfFiles = await _storageService.getPdfFiles();

      _musicFileCount.value = musicFiles.length;
      _videoFileCount.value = videoFiles.length;
      _pdfFileCount.value = pdfFiles.length;
    } catch (e) {
      Logger.error('Error loading file statistics', error: e);
    }
  }

  /// 獲取服務器狀態文字
  String get serverStatusText {
    return isServerRunning.value ? '運行中' : '已停止';
  }

  /// 獲取服務器狀態顏色
  Color get serverStatusColor {
    return isServerRunning.value ? Colors.green : Colors.grey;
  }

  /// 複製服務器URL到剪貼板
  void copyServerUrl() {
    if (serverUrl != null) {
      Clipboard.setData(ClipboardData(text: serverUrl!));
      Get.snackbar(
        '已複製',
        '服務器地址已複製到剪貼板',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// 設置手動IP地址
  void setManualIpAddress(String ipAddress) {
    if (ipAddress.isEmpty) {
      _httpServerService.setManualIpAddress(null);

      // 只在非測試環境顯示Snackbar
      if (!Get.testMode) {
        Get.snackbar(
          '已重置',
          '已重置IP地址設置，將使用自動檢測\n新地址: ${serverUrl ?? '獲取中...'}',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.blue.withValues(alpha: 0.8),
          colorText: Colors.white,
        );
      }
    } else {
      _httpServerService.setManualIpAddress(ipAddress);

      // 只在非測試環境顯示Snackbar
      if (!Get.testMode) {
        Get.snackbar(
          '已設置',
          '已手動設置IP地址為: $ipAddress\n新地址: ${serverUrl ?? '獲取中...'}',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.green.withValues(alpha: 0.8),
          colorText: Colors.white,
        );
      }
    }
  }

  /// 顯示IP地址設置對話框
  void showIpAddressDialog() {
    final textController = TextEditingController(
      text: _httpServerService.serverAddress,
    );

    Get.dialog(
      AlertDialog(
        title: const Text('設置服務器IP地址'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: textController,
              decoration: const InputDecoration(
                labelText: 'IP地址',
                hintText: '例如: *************',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            const Text('請輸入您設備的Wi-Fi IP地址，通常以192.168開頭'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              setManualIpAddress(''); // 重置為自動檢測
            },
            child: const Text('使用自動檢測'),
          ),
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          TextButton(
            onPressed: () {
              final ip = textController.text.trim();
              if (ip.isNotEmpty) {
                setManualIpAddress(ip);
              }
              Get.back();
            },
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }

  /// 運行服務器診斷
  Future<void> runServerDiagnostics() async {
    try {
      Logger.info('Running server diagnostics...');

      final diagnostics = await ServerDiagnostics.runDiagnostics();
      final report = ServerDiagnostics.generateReport(diagnostics);

      Logger.info('Server diagnostics report:\n$report');

      Get.dialog(
        AlertDialog(
          title: const Text('服務器診斷報告'),
          content: SingleChildScrollView(
            child: Text(
              report,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Clipboard.setData(ClipboardData(text: report));
                Get.back();
                Get.snackbar(
                  '已複製',
                  '診斷報告已複製到剪貼板',
                  snackPosition: SnackPosition.BOTTOM,
                  duration: const Duration(seconds: 2),
                );
              },
              child: const Text('複製'),
            ),
            TextButton(onPressed: () => Get.back(), child: const Text('關閉')),
          ],
        ),
      );
    } catch (e) {
      Logger.error('Error running server diagnostics', error: e);
      Get.snackbar(
        '診斷失敗',
        '無法運行服務器診斷：${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
    }
  }
}
