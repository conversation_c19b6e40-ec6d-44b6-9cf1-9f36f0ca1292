import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/video_file.dart';
import '../models/video_playlist.dart';
import '../models/video_folder.dart';
import '../models/video_player_state.dart' as app_state;
import '../services/video_player_service.dart';
import '../services/storage_service.dart';
import '../services/video_playlist_service.dart';
import '../services/video_folder_service.dart';
import '../utils/logger.dart';

/// 視頻控制器
/// 管理視頻播放相關的UI狀態和用戶交互
class VideoController extends GetxController {
  final VideoPlayerService _videoPlayerService = Get.find<VideoPlayerService>();
  final StorageService _storageService = Get.find<StorageService>();
  final VideoPlaylistService _playlistService =
      Get.find<VideoPlaylistService>();
  final VideoFolderService _folderService = Get.find<VideoFolderService>();

  // 視頻文件列表
  final _videoFiles = <VideoFile>[].obs;
  final _isLoading = false.obs;
  final _selectedFiles = <VideoFile>[].obs;
  final _isSelectionMode = false.obs;

  // 文件夾相關
  final _folders = <VideoFolder>[].obs;
  final _currentFolder = Rxn<VideoFolder>();

  // Getters
  List<VideoFile> get videoFiles => _videoFiles.toList();
  bool get isLoading => _isLoading.value;
  List<VideoFile> get selectedFiles => _selectedFiles.toList();
  bool get isSelectionMode => _isSelectionMode.value;

  // 文件夾相關getters
  List<VideoFolder> get folders => _folders.toList();
  VideoFolder? get currentFolder => _currentFolder.value;

  // 播放列表相關
  List<VideoPlaylist> get playlists => _playlistService.playlists;
  VideoPlaylist? get favoritesPlaylist => _playlistService.favoritesPlaylist;
  VideoPlaylist? get recentPlaylist => _playlistService.recentPlaylist;

  // 播放器狀態
  app_state.VideoPlayerFullState get playerState =>
      _videoPlayerService.playerState;
  VideoFile? get currentVideo => _videoPlayerService.currentVideo;
  bool get isPlaying => playerState.playerState.isPlaying;
  bool get isPaused => playerState.playerState.isPaused;
  bool get isFullscreen => playerState.displayMode.isFullscreen;

  // 視頻控制器
  get videoController => _videoPlayerService.videoController;

  @override
  void onInit() {
    super.onInit();
    _loadVideoFiles();
    loadPlaylists();
    loadFolders();
  }

  /// 載入視頻文件
  Future<void> loadVideoFiles() async {
    await _loadVideoFiles();
  }

  /// 內部載入視頻文件方法
  Future<void> _loadVideoFiles() async {
    try {
      _isLoading.value = true;

      final files = await _storageService.getVideoFiles();
      final videoFileList = files
          .map((file) => VideoFile.fromFile(file))
          .toList();

      // 按文件名排序
      videoFileList.sort((a, b) => a.fileName.compareTo(b.fileName));

      _videoFiles.value = videoFileList;
    } catch (e) {
      Get.snackbar('載入失敗', '無法載入視頻文件: $e', snackPosition: SnackPosition.BOTTOM);
    } finally {
      _isLoading.value = false;
    }
  }

  /// 播放指定視頻
  Future<void> playVideo(VideoFile videoFile) async {
    final success = await _videoPlayerService.playPlaylist(
      videoFiles,
      startIndex: videoFiles.indexOf(videoFile),
    );

    if (!success) {
      Get.snackbar('播放失敗', '無法播放該視頻文件', snackPosition: SnackPosition.BOTTOM);
    }
  }

  /// 播放/暫停切換
  Future<void> togglePlayPause() async {
    await _videoPlayerService.togglePlayPause();
  }

  /// 播放上一個視頻
  Future<void> playPrevious() async {
    await _videoPlayerService.playPrevious();
  }

  /// 播放下一個視頻
  Future<void> playNext() async {
    await _videoPlayerService.playNext();
  }

  /// 停止播放
  Future<void> stop() async {
    await _videoPlayerService.stop();
  }

  /// 跳轉到指定位置
  Future<void> seekTo(double progress) async {
    final position = Duration(
      milliseconds: (playerState.totalDuration * progress).round(),
    );
    await _videoPlayerService.seekTo(position);
  }

  /// 快進5秒
  Future<void> seekForward() async {
    await _videoPlayerService.seekForward();
  }

  /// 快退5秒
  Future<void> seekBackward() async {
    await _videoPlayerService.seekBackward();
  }

  /// 設置音量
  Future<void> setVolume(double volume) async {
    await _videoPlayerService.setVolume(volume);
  }

  /// 設置播放速度
  Future<void> setPlaybackSpeed(double speed) async {
    await _videoPlayerService.setPlaybackSpeed(speed);
  }

  /// 切換播放模式
  void togglePlayMode() {
    _videoPlayerService.togglePlayMode();
  }

  /// 切換全屏模式
  Future<void> toggleFullscreen() async {
    await _videoPlayerService.toggleFullscreen();
  }

  /// 切換畫中畫模式
  Future<void> togglePictureInPicture() async {
    await _videoPlayerService.togglePictureInPicture();
  }

  /// 切換控制器顯示
  void toggleControls() {
    _videoPlayerService.toggleControls();
  }

  /// 進入選擇模式
  void enterSelectionMode() {
    _isSelectionMode.value = true;
    _selectedFiles.clear();
  }

  /// 退出選擇模式
  void exitSelectionMode() {
    _isSelectionMode.value = false;
    _selectedFiles.clear();
  }

  /// 切換文件選擇狀態
  void toggleFileSelection(VideoFile videoFile) {
    if (_selectedFiles.contains(videoFile)) {
      _selectedFiles.remove(videoFile);
    } else {
      _selectedFiles.add(videoFile);
    }
  }

  /// 全選
  void selectAll() {
    _selectedFiles.value = videoFiles.toList();
  }

  /// 取消全選
  void deselectAll() {
    _selectedFiles.clear();
  }

  /// 刪除選中的文件
  Future<void> deleteSelectedFiles() async {
    if (selectedFiles.isEmpty) return;

    try {
      final filesToDelete = selectedFiles.toList();

      // 確認對話框
      final confirmed =
          await Get.dialog<bool>(
            AlertDialog(
              title: const Text('確認刪除'),
              content: Text('確定要刪除 ${filesToDelete.length} 個視頻文件嗎？'),
              actions: [
                TextButton(
                  onPressed: () => Get.back(result: false),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () => Get.back(result: true),
                  child: const Text('刪除'),
                ),
              ],
            ),
          ) ??
          false;

      if (!confirmed) return;

      // 刪除文件
      int deletedCount = 0;
      for (final videoFile in filesToDelete) {
        final file = File(videoFile.path);
        final success = await _storageService.deleteFile(file);
        if (success) {
          deletedCount++;
          _videoFiles.remove(videoFile);
        }
      }

      exitSelectionMode();

      Get.snackbar(
        '刪除完成',
        '成功刪除 $deletedCount 個文件',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        '刪除失敗',
        '刪除文件時發生錯誤: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// 播放選中的文件
  Future<void> playSelectedFiles() async {
    if (selectedFiles.isEmpty) return;

    final success = await _videoPlayerService.playPlaylist(selectedFiles);

    if (success) {
      exitSelectionMode();
      Get.snackbar(
        '開始播放',
        '正在播放選中的 ${selectedFiles.length} 個視頻',
        snackPosition: SnackPosition.BOTTOM,
      );
    } else {
      Get.snackbar('播放失敗', '無法播放選中的文件', snackPosition: SnackPosition.BOTTOM);
    }
  }

  /// 獲取格式化的播放時間
  String get formattedCurrentTime => playerState.formattedCurrentPosition;

  /// 獲取格式化的總時間
  String get formattedTotalTime => playerState.formattedTotalDuration;

  /// 獲取播放進度
  double get playProgress => playerState.progress;

  /// 檢查文件是否被選中
  bool isFileSelected(VideoFile videoFile) {
    return _selectedFiles.contains(videoFile);
  }

  /// 檢查是否為當前播放的文件
  bool isCurrentlyPlaying(VideoFile videoFile) {
    return currentVideo?.path == videoFile.path && isPlaying;
  }

  /// 檢查是否為當前視頻文件
  bool isCurrentVideo(VideoFile videoFile) {
    return currentVideo?.path == videoFile.path;
  }

  /// 創建播放列表
  Future<void> createPlaylist(
    String name, {
    String? description,
    VideoPlaylistMode? playMode,
  }) async {
    try {
      await _playlistService.createPlaylist(
        name: name,
        description: description,
        playMode: playMode,
      );
      Get.snackbar('創建成功', '播放列表 "$name" 已創建');
    } catch (e) {
      Get.snackbar('創建失敗', '無法創建播放列表: $e');
    }
  }

  /// 添加到我的最愛
  Future<void> addToFavorites(VideoFile videoFile) async {
    try {
      final success = await _playlistService.addToFavorites(videoFile);
      if (success) {
        Get.snackbar('添加成功', '已添加到我的最愛');
      } else {
        Get.snackbar('添加失敗', '無法添加到我的最愛');
      }
    } catch (e) {
      Get.snackbar('添加失敗', '添加到我的最愛時發生錯誤: $e');
    }
  }

  /// 從我的最愛移除
  Future<void> removeFromFavorites(VideoFile videoFile) async {
    try {
      final success = await _playlistService.removeFromFavorites(videoFile);
      if (success) {
        Get.snackbar('移除成功', '已從我的最愛移除');
      } else {
        Get.snackbar('移除失敗', '無法從我的最愛移除');
      }
    } catch (e) {
      Get.snackbar('移除失敗', '從我的最愛移除時發生錯誤: $e');
    }
  }

  /// 檢查是否在我的最愛中
  bool isInFavorites(VideoFile videoFile) {
    return _playlistService.isInFavorites(videoFile);
  }

  /// 添加到播放列表
  Future<void> addToPlaylist(
    String playlistId,
    List<VideoFile> videoFiles,
  ) async {
    try {
      final success = await _playlistService.addVideosToPlaylist(
        playlistId,
        videoFiles,
      );
      if (success) {
        Get.snackbar('添加成功', '已添加 ${videoFiles.length} 個視頻到播放列表');
      } else {
        Get.snackbar('添加失敗', '無法添加到播放列表');
      }
    } catch (e) {
      Get.snackbar('添加失敗', '添加到播放列表時發生錯誤: $e');
    }
  }

  /// 從播放列表移除視頻
  Future<void> removeFromPlaylist(
    String playlistId,
    VideoFile videoFile,
  ) async {
    try {
      final success = await _playlistService.removeVideoFromPlaylist(
        playlistId,
        videoFile,
      );
      if (success) {
        Get.snackbar('移除成功', '已從播放列表移除');
      } else {
        Get.snackbar('移除失敗', '無法從播放列表移除');
      }
    } catch (e) {
      Get.snackbar('移除失敗', '從播放列表移除時發生錯誤: $e');
    }
  }

  /// 移動播放列表中的視頻
  Future<void> moveVideoInPlaylist(
    String playlistId,
    int oldIndex,
    int newIndex,
  ) async {
    try {
      final success = await _playlistService.moveVideoInPlaylist(
        playlistId,
        oldIndex,
        newIndex,
      );
      if (!success) {
        Get.snackbar('移動失敗', '無法移動視頻位置');
      }
    } catch (e) {
      Get.snackbar('移動失敗', '移動視頻時發生錯誤: $e');
    }
  }

  /// 播放播放列表
  Future<void> playPlaylist(
    VideoPlaylist playlist, {
    int startIndex = 0,
  }) async {
    try {
      final success = await _videoPlayerService.playPlaylist(
        playlist.videoFiles,
        startIndex: startIndex,
      );

      if (success) {
        // 增加播放次數
        final updatedPlaylist = playlist.incrementPlayCount();
        await _playlistService.updatePlaylist(updatedPlaylist);

        Get.snackbar('開始播放', '正在播放播放列表 "${playlist.name}"');
      } else {
        Get.snackbar('播放失敗', '無法播放播放列表');
      }
    } catch (e) {
      Get.snackbar('播放失敗', '播放播放列表時發生錯誤: $e');
    }
  }

  /// 刪除播放列表
  Future<void> deletePlaylist(String playlistId) async {
    try {
      final success = await _playlistService.deletePlaylist(playlistId);
      if (success) {
        Get.snackbar('刪除成功', '播放列表已刪除');
      } else {
        Get.snackbar('刪除失敗', '無法刪除播放列表（可能是系統播放列表）');
      }
    } catch (e) {
      Get.snackbar('刪除失敗', '刪除播放列表時發生錯誤: $e');
    }
  }

  /// 載入播放列表
  Future<void> loadPlaylists() async {
    await _playlistService.loadPlaylists();
  }

  /// 搜索播放列表
  List<VideoPlaylist> searchPlaylists(String query) {
    return _playlistService.searchPlaylists(query);
  }

  /// 獲取包含指定視頻的播放列表
  List<VideoPlaylist> getPlaylistsContaining(VideoFile videoFile) {
    return _playlistService.getPlaylistsContaining(videoFile);
  }

  // ==================== 文件夾管理 ====================

  /// 載入文件夾列表
  Future<void> loadFolders() async {
    try {
      _isLoading.value = true;
      final folders = await _folderService.loadFolders();
      _folders.value = folders;

      // 設置默認當前文件夾為根文件夾
      if (_currentFolder.value == null && folders.isNotEmpty) {
        _currentFolder.value = folders.firstWhere(
          (f) => f.id == 'root',
          orElse: () => folders.first,
        );
      }
    } catch (e) {
      Logger.error('Failed to load folders: $e');
      Get.snackbar('載入失敗', '無法載入文件夾: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// 創建新文件夾
  Future<void> createFolder({
    required String name,
    String? parentId,
    String? description,
    int? color,
    String? icon,
  }) async {
    try {
      final folder = await _folderService.createFolder(
        name: name,
        parentId: parentId,
        description: description,
        color: color,
        icon: icon,
      );

      _folders.add(folder);
      Get.snackbar('創建成功', '文件夾 "$name" 已創建');
    } catch (e) {
      Logger.error('Failed to create folder: $e');
      Get.snackbar('創建失敗', '無法創建文件夾: $e');
    }
  }

  /// 重命名文件夾
  Future<void> renameFolder(String folderId, String newName) async {
    try {
      final updatedFolder = await _folderService.renameFolder(
        folderId,
        newName,
      );

      final index = _folders.indexWhere((f) => f.id == folderId);
      if (index != -1) {
        _folders[index] = updatedFolder;
      }

      // 更新當前文件夾
      if (_currentFolder.value?.id == folderId) {
        _currentFolder.value = updatedFolder;
      }

      Get.snackbar('重命名成功', '文件夾已重命名為 "$newName"');
    } catch (e) {
      Logger.error('Failed to rename folder: $e');
      Get.snackbar('重命名失敗', '無法重命名文件夾: $e');
    }
  }

  /// 刪除文件夾
  Future<void> deleteFolder(String folderId, {bool deleteFiles = false}) async {
    try {
      final success = await _folderService.deleteFolder(
        folderId,
        deleteFiles: deleteFiles,
      );

      if (success) {
        _folders.removeWhere((f) => f.id == folderId);

        // 如果刪除的是當前文件夾，切換到根文件夾
        if (_currentFolder.value?.id == folderId) {
          if (_folders.isNotEmpty) {
            _currentFolder.value = _folders.firstWhere(
              (f) => f.id == 'root',
              orElse: () => _folders.first,
            );
          } else {
            _currentFolder.value = null;
          }
        }

        Get.snackbar('刪除成功', '文件夾已刪除');
      }
    } catch (e) {
      Logger.error('Failed to delete folder: $e');
      Get.snackbar('刪除失敗', '無法刪除文件夾: $e');
    }
  }

  /// 切換當前文件夾
  Future<void> switchToFolder(String folderId) async {
    try {
      final folder = _folders.firstWhere(
        (f) => f.id == folderId,
        orElse: () => throw Exception('文件夾不存在'),
      );

      _currentFolder.value = folder;

      // 載入該文件夾的視頻文件
      await loadFolderVideos(folderId);
    } catch (e) {
      Logger.error('Failed to switch folder: $e');
      Get.snackbar('切換失敗', '無法切換到該文件夾: $e');
    }
  }

  /// 載入文件夾中的視頻文件
  Future<void> loadFolderVideos(String folderId) async {
    try {
      _isLoading.value = true;

      // 如果是"所有視頻"系統文件夾，載入所有視頻文件
      if (folderId == 'root') {
        await _loadVideoFiles();
      } else {
        // 其他文件夾載入特定文件夾的視頻
        final videos = await _folderService.getFolderVideos(folderId);
        _videoFiles.value = videos;
      }
    } catch (e) {
      Logger.error('Failed to load folder videos: $e');
      Get.snackbar('載入失敗', '無法載入文件夾視頻: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// 移動視頻文件到文件夾
  Future<void> moveVideosToFolder(
    List<VideoFile> videos,
    String targetFolderId,
  ) async {
    try {
      int successCount = 0;

      for (final video in videos) {
        final success = await _folderService.moveVideoToFolder(
          video,
          targetFolderId,
        );
        if (success) {
          successCount++;
          _videoFiles.remove(video);
        }
      }

      if (successCount > 0) {
        Get.snackbar('移動成功', '成功移動 $successCount 個視頻文件');

        // 如果在選擇模式，退出選擇模式
        if (_isSelectionMode.value) {
          exitSelectionMode();
        }
      } else {
        Get.snackbar('移動失敗', '沒有文件被移動');
      }
    } catch (e) {
      Logger.error('Failed to move videos to folder: $e');
      Get.snackbar('移動失敗', '無法移動視頻文件: $e');
    }
  }
}
