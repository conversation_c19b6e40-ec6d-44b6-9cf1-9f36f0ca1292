import 'dart:async';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart' as vp;

import '../models/video_file.dart';
import '../utils/logger.dart';

/// 視頻播放控制器
/// 負責管理視頻播放、控制器顯示/隱藏、全屏切換等功能
class VideoPlayerController extends GetxController {
  // 視頻播放器實例
  vp.VideoPlayerController? _videoController;
  vp.VideoPlayerController? get videoController => _videoController;

  // 當前播放的視頻
  final Rx<VideoFile?> _currentVideo = Rx<VideoFile?>(null);
  VideoFile? get currentVideo => _currentVideo.value;

  // 播放狀態
  final RxBool _isPlaying = false.obs;
  bool get isPlaying => _isPlaying.value;

  // 全屏狀態
  final RxBool _isFullscreen = false.obs;
  bool get isFullscreen => _isFullscreen.value;

  // 控制器顯示狀態
  final RxBool _showControls = true.obs;
  bool get showControls => _showControls.value;
  set showControls(bool value) => _showControls.value = value;

  // 播放速度
  final RxDouble _playbackSpeed = 1.0.obs;
  double get playbackSpeed => _playbackSpeed.value;

  // 播放進度
  final RxDouble _progress = 0.0.obs;
  double get progress => _progress.value;

  // 視頻時長和當前位置
  final RxInt _duration = 0.obs;
  final RxInt _position = 0.obs;
  int get duration => _duration.value;
  int get position => _position.value;

  // 控制器自動隱藏定時器
  Timer? _hideControlsTimer;

  // 播放位置監聽器
  StreamSubscription? _positionSubscription;

  @override
  void onInit() {
    super.onInit();
    Logger.info('VideoPlayerController initialized');
  }

  @override
  void onClose() {
    // 確保退出全屏模式
    if (_isFullscreen.value) {
      exitFullscreen();
    }

    _disposeVideoController();
    _hideControlsTimer?.cancel();
    _positionSubscription?.cancel();
    super.onClose();
  }

  /// 播放指定視頻
  Future<void> playVideo(VideoFile videoFile) async {
    try {
      Logger.info('Playing video: ${videoFile.title}');

      // 釋放之前的視頻控制器
      _disposeVideoController();

      // 設置當前視頻
      _currentVideo.value = videoFile;

      // 創建新的視頻控制器
      _videoController = vp.VideoPlayerController.file(
        videoFile.file,
        videoPlayerOptions: vp.VideoPlayerOptions(
          mixWithOthers: false, // 不與其他音頻混合
          allowBackgroundPlayback: false, // 不允許後台播放
        ),
      );

      // 初始化視頻控制器
      await _videoController!.initialize();

      // 強制更新UI
      update();

      // 監聽播放狀態變化
      _videoController!.addListener(_onVideoStateChanged);

      // 開始播放
      await _videoController!.play();
      _isPlaying.value = true;

      // 開始監聽播放位置
      _startPositionListener();

      // 啟動控制器自動隱藏定時器
      _startHideControlsTimer();

      // 更新視頻時長
      _duration.value = _videoController!.value.duration.inMilliseconds;

      Logger.info('Video initialized and playing');
    } catch (e) {
      Logger.error('Error playing video', error: e);
      Get.snackbar('錯誤', '無法播放視頻: $e');
    }
  }

  /// 播放/暫停切換
  Future<void> togglePlayPause() async {
    if (_videoController == null) return;

    try {
      if (_isPlaying.value) {
        await _videoController!.pause();
        _isPlaying.value = false;
        _hideControlsTimer?.cancel(); // 暫停時取消自動隱藏
      } else {
        await _videoController!.play();
        _isPlaying.value = true;
        _startHideControlsTimer(); // 播放時啟動自動隱藏
      }

      // 顯示控制器
      _showControls.value = true;
    } catch (e) {
      Logger.error('Error toggling play/pause', error: e);
    }
  }

  /// 跳轉到指定位置
  Future<void> seekTo(Duration position) async {
    if (_videoController == null) return;

    try {
      await _videoController!.seekTo(position);
      _position.value = position.inMilliseconds;
    } catch (e) {
      Logger.error('Error seeking to position', error: e);
    }
  }

  /// 快進10秒
  Future<void> seekForward() async {
    if (_videoController == null) return;

    final currentPosition = _videoController!.value.position;
    final newPosition = currentPosition + const Duration(seconds: 10);
    final maxPosition = _videoController!.value.duration;

    await seekTo(newPosition > maxPosition ? maxPosition : newPosition);
  }

  /// 快退10秒
  Future<void> seekBackward() async {
    if (_videoController == null) return;

    final currentPosition = _videoController!.value.position;
    final newPosition = currentPosition - const Duration(seconds: 10);

    await seekTo(newPosition < Duration.zero ? Duration.zero : newPosition);
  }

  /// 設置播放速度
  Future<void> setPlaybackSpeed(double speed) async {
    if (_videoController == null) return;

    try {
      await _videoController!.setPlaybackSpeed(speed);
      _playbackSpeed.value = speed;
      Logger.info('Playback speed set to ${speed}x');
    } catch (e) {
      Logger.error('Error setting playback speed', error: e);
    }
  }

  /// 進入全屏模式
  Future<void> enterFullscreen() async {
    if (_isFullscreen.value) return;

    try {
      // 設置橫屏方向
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      // 隱藏系統UI，但保留手勢導航
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersive,
        overlays: [],
      );

      _isFullscreen.value = true;
      // 進入全屏時顯示控制器
      _showControls.value = true;
      Logger.info('Entered fullscreen mode');
    } catch (e) {
      Logger.error('Error entering fullscreen', error: e);
    }
  }

  /// 退出全屏模式
  Future<void> exitFullscreen() async {
    if (!_isFullscreen.value) return;

    try {
      // 恢復所有支持的方向
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      // 顯示系統UI
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: SystemUiOverlay.values,
      );

      _isFullscreen.value = false;
      Logger.info('Exited fullscreen mode');
    } catch (e) {
      Logger.error('Error exiting fullscreen', error: e);
    }
  }

  /// 切換全屏模式
  Future<void> toggleFullscreen() async {
    if (_isFullscreen.value) {
      await exitFullscreen();
    } else {
      await enterFullscreen();
    }
  }

  /// 切換控制器顯示/隱藏
  void toggleControls() {
    _showControls.value = !_showControls.value;

    if (_showControls.value && _isPlaying.value) {
      _startHideControlsTimer();
    } else {
      _hideControlsTimer?.cancel();
    }
  }

  /// 啟動控制器自動隱藏定時器
  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (_isPlaying.value) {
        _showControls.value = false;
      }
    });
  }

  /// 開始監聽播放位置
  void _startPositionListener() {
    _positionSubscription?.cancel();
    _positionSubscription =
        _videoController?.addListener(() {
              if (_videoController != null) {
                final position =
                    _videoController!.value.position.inMilliseconds;
                final duration =
                    _videoController!.value.duration.inMilliseconds;

                _position.value = position;
                _progress.value = duration > 0 ? position / duration : 0.0;
              }
            })
            as StreamSubscription?;
  }

  /// 視頻狀態變化監聽
  void _onVideoStateChanged() {
    if (_videoController == null) return;

    final value = _videoController!.value;
    _isPlaying.value = value.isPlaying;

    // 如果視頻播放完成
    if (value.position >= value.duration && value.duration.inMilliseconds > 0) {
      _isPlaying.value = false;
      _showControls.value = true;
      _hideControlsTimer?.cancel();
    }
  }

  /// 釋放視頻控制器
  void _disposeVideoController() {
    _positionSubscription?.cancel();
    _videoController?.removeListener(_onVideoStateChanged);
    _videoController?.dispose();
    _videoController = null;
  }
}
