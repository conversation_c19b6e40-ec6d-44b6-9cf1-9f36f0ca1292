import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../utils/logger.dart';

class AppController extends GetxController {
  // 使用RxInt, RxBool直接聲明響應式變量，減少getter/setter代碼
  final currentIndex = 0.obs;
  final isDarkMode = false.obs;
  final isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
  }

  /// 從本地存儲加載設置
  void _loadSettings() {
    // 暫時使用默認值，後續可以集成StorageService
    Logger.info('應用設置已加載');
  }

  /// 切換主題模式
  void toggleTheme() {
    isDarkMode.toggle();
    Get.changeThemeMode(isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
    Logger.info('主題已切換為: ${isDarkMode.value ? '深色' : '淺色'}');
  }

  /// 切換標籤頁
  void changeTabIndex(int index) {
    currentIndex.value = index;
  }

  /// 顯示加載中狀態
  void showLoading() {
    isLoading.value = true;
  }

  /// 隱藏加載中狀態
  void hideLoading() {
    isLoading.value = false;
  }
}
