import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/file_item.dart';
import '../services/file_manager_service.dart';
import '../widgets/file/file_item_widget.dart';

/// 文件管理控制器
/// 負責文件管理的業務邏輯和狀態管理
class FileManagerController extends GetxController {
  final FileManagerService _fileManagerService = Get.find<FileManagerService>();

  // 響應式狀態
  final _selectedFiles = <FileItem>[].obs;
  final _isSelectionMode = false.obs;
  final _currentFilter = Rx<FileType?>(null);
  final _searchQuery = ''.obs;
  final _sortType = FileSortType.name.obs;
  final _sortAscending = true.obs;
  final _displayMode = FileDisplayMode.list.obs;

  // Getters
  List<FileItem> get allFiles => _fileManagerService.allFiles;
  List<FileItem> get favoriteFiles => _fileManagerService.favoriteFiles;
  List<FileItem> get recentFiles => _fileManagerService.recentFiles;
  List<FileItem> get selectedFiles => _selectedFiles.toList();
  bool get isLoading => _fileManagerService.isLoading;
  bool get isScanning => _fileManagerService.isScanning;
  bool get isSelectionMode => _isSelectionMode.value;
  FileType? get currentFilter => _currentFilter.value;
  String get searchQuery => _searchQuery.value;
  FileSortType get sortType => _sortType.value;
  bool get sortAscending => _sortAscending.value;
  FileDisplayMode get displayMode => _displayMode.value;

  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }

  /// 初始化控制器
  void _initializeController() {
    // 初始掃描
    scanFiles();
  }

  /// 掃描文件
  Future<void> scanFiles() async {
    await _fileManagerService.scanFileSystem();
  }

  /// 刷新文件列表
  Future<void> refreshFiles() async {
    await _fileManagerService.loadFileIndex();
    await scanFiles();
  }

  /// 獲取過濾後的文件列表
  List<FileItem> getFilteredFiles() {
    List<FileItem> files;

    // 根據當前過濾器獲取文件
    if (_currentFilter.value == null) {
      // 如果沒有過濾器，顯示所有文件
      files = allFiles;
    } else {
      files = _fileManagerService.getFilesByType(_currentFilter.value!);
    }

    // 搜索過濾
    if (_searchQuery.value.isNotEmpty) {
      files = _fileManagerService.searchFiles(_searchQuery.value);
    }

    // 排序
    files = _fileManagerService.sortFiles(
      files,
      _sortType.value,
      ascending: _sortAscending.value,
    );

    return files;
  }

  /// 更新過濾後的文件列表
  void _updateFilteredFiles() {
    update(); // 通知UI更新
  }

  /// 設置文件類型過濾器
  void setFileTypeFilter(FileType? type) {
    _currentFilter.value = type;
    _updateFilteredFiles();
  }

  /// 設置搜索查詢
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _updateFilteredFiles();
  }

  /// 設置排序類型
  void setSortType(FileSortType type) {
    if (_sortType.value == type) {
      // 如果是同一個排序類型，切換升序/降序
      _sortAscending.value = !_sortAscending.value;
    } else {
      _sortType.value = type;
      _sortAscending.value = true;
    }
    _updateFilteredFiles();
  }

  /// 切換顯示模式
  void toggleDisplayMode() {
    _displayMode.value = _displayMode.value == FileDisplayMode.list
        ? FileDisplayMode.grid
        : FileDisplayMode.list;
  }

  /// 進入選擇模式
  void enterSelectionMode() {
    _isSelectionMode.value = true;
    _selectedFiles.clear();
  }

  /// 退出選擇模式
  void exitSelectionMode() {
    _isSelectionMode.value = false;
    _selectedFiles.clear();
  }

  /// 切換文件選擇狀態
  void toggleFileSelection(FileItem fileItem) {
    if (_selectedFiles.contains(fileItem)) {
      _selectedFiles.remove(fileItem);
    } else {
      _selectedFiles.add(fileItem);
    }

    // 如果沒有選中的文件，退出選擇模式
    if (_selectedFiles.isEmpty) {
      exitSelectionMode();
    }
  }

  /// 全選文件
  void selectAllFiles() {
    final filteredFiles = getFilteredFiles();
    _selectedFiles.value = filteredFiles;
  }

  /// 取消全選
  void deselectAllFiles() {
    _selectedFiles.clear();
  }

  /// 檢查文件是否被選中
  bool isFileSelected(FileItem fileItem) {
    return _selectedFiles.contains(fileItem);
  }

  /// 打開文件
  Future<void> openFile(FileItem fileItem) async {
    try {
      // 添加到最近文件
      await _fileManagerService.addToRecent(fileItem);

      // 根據文件類型打開相應的播放器
      switch (fileItem.type) {
        case FileType.audio:
          Get.toNamed('/music');
          break;
        case FileType.video:
          Get.toNamed('/video');
          break;
        case FileType.pdf:
          Get.toNamed('/pdf');
          break;
        default:
          Get.snackbar('提示', '不支持的文件類型');
      }
    } catch (e) {
      Get.snackbar('錯誤', '打開文件失敗: $e');
    }
  }

  /// 重命名文件
  Future<void> renameFile(FileItem fileItem, String newName) async {
    try {
      final result = await _fileManagerService.renameFile(fileItem, newName);
      if (result != null) {
        Get.snackbar('成功', '文件重命名成功');
        _updateFilteredFiles();
      } else {
        Get.snackbar('錯誤', '文件重命名失敗');
      }
    } catch (e) {
      Get.snackbar('錯誤', '重命名文件時發生錯誤: $e');
    }
  }

  /// 刪除文件
  Future<void> deleteFile(FileItem fileItem) async {
    try {
      final success = await _fileManagerService.deleteFile(fileItem);
      if (success) {
        Get.snackbar('成功', '文件刪除成功');
        _selectedFiles.remove(fileItem);
        _updateFilteredFiles();
      } else {
        Get.snackbar('錯誤', '文件刪除失敗');
      }
    } catch (e) {
      Get.snackbar('錯誤', '刪除文件時發生錯誤: $e');
    }
  }

  /// 批量刪除文件
  Future<void> deleteSelectedFiles() async {
    try {
      final filesToDelete = List<FileItem>.from(_selectedFiles);
      var successCount = 0;

      for (final file in filesToDelete) {
        final success = await _fileManagerService.deleteFile(file);
        if (success) successCount++;
      }

      Get.snackbar('完成', '成功刪除 $successCount 個文件');
      exitSelectionMode();
      _updateFilteredFiles();
    } catch (e) {
      Get.snackbar('錯誤', '批量刪除文件時發生錯誤: $e');
    }
  }

  /// 移動文件
  Future<void> moveFile(FileItem fileItem, Directory targetDirectory) async {
    try {
      final result = await _fileManagerService.moveFile(
        fileItem,
        targetDirectory,
      );
      if (result != null) {
        Get.snackbar('成功', '文件移動成功');
        _updateFilteredFiles();
      } else {
        Get.snackbar('錯誤', '文件移動失敗');
      }
    } catch (e) {
      Get.snackbar('錯誤', '移動文件時發生錯誤: $e');
    }
  }

  /// 複製文件
  Future<void> copyFile(FileItem fileItem, Directory targetDirectory) async {
    try {
      final result = await _fileManagerService.copyFile(
        fileItem,
        targetDirectory,
      );
      if (result != null) {
        Get.snackbar('成功', '文件複製成功');
        _updateFilteredFiles();
      } else {
        Get.snackbar('錯誤', '文件複製失敗');
      }
    } catch (e) {
      Get.snackbar('錯誤', '複製文件時發生錯誤: $e');
    }
  }

  /// 切換收藏狀態
  Future<void> toggleFavorite(FileItem fileItem) async {
    try {
      if (fileItem.isFavorite) {
        await _fileManagerService.removeFromFavorites(fileItem);
        Get.snackbar('提示', '已從收藏中移除');
      } else {
        await _fileManagerService.addToFavorites(fileItem);
        Get.snackbar('提示', '已添加到收藏');
      }
      _updateFilteredFiles();
    } catch (e) {
      Get.snackbar('錯誤', '切換收藏狀態時發生錯誤: $e');
    }
  }

  /// 顯示重命名對話框
  void showRenameDialog(FileItem fileItem) {
    final controller = TextEditingController(
      text: fileItem.nameWithoutExtension,
    );

    Get.dialog(
      AlertDialog(
        title: const Text('重命名文件'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '新文件名',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          TextButton(
            onPressed: () {
              final newName = controller.text.trim();
              if (newName.isNotEmpty) {
                Get.back();
                renameFile(fileItem, newName);
              }
            },
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }

  /// 顯示刪除確認對話框
  void showDeleteConfirmDialog(FileItem fileItem) {
    Get.dialog(
      AlertDialog(
        title: const Text('刪除文件'),
        content: Text('確定要刪除文件 "${fileItem.fileName}" 嗎？\n此操作無法撤銷。'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          TextButton(
            onPressed: () {
              Get.back();
              deleteFile(fileItem);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('刪除'),
          ),
        ],
      ),
    );
  }

  /// 顯示批量刪除確認對話框
  void showBatchDeleteConfirmDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('批量刪除'),
        content: Text('確定要刪除選中的 ${_selectedFiles.length} 個文件嗎？\n此操作無法撤銷。'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          TextButton(
            onPressed: () {
              Get.back();
              deleteSelectedFiles();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('刪除'),
          ),
        ],
      ),
    );
  }

  /// 顯示文件屬性對話框
  void showFilePropertiesDialog(FileItem fileItem) {
    Get.dialog(
      AlertDialog(
        title: Text(fileItem.fileName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPropertyRow('類型', fileItem.type.displayName),
            _buildPropertyRow('大小', fileItem.formattedFileSize),
            _buildPropertyRow('創建時間', fileItem.createdAt.toString()),
            _buildPropertyRow('修改時間', fileItem.modifiedAt.toString()),
            if (fileItem.lastAccessedAt != null)
              _buildPropertyRow('最後訪問', fileItem.lastAccessedAt.toString()),
            _buildPropertyRow('路徑', fileItem.path),
            if (fileItem.hasTags)
              _buildPropertyRow('標籤', fileItem.tags.join(', ')),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('關閉')),
        ],
      ),
    );
  }

  /// 構建屬性行
  Widget _buildPropertyRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// 獲取文件統計信息
  Map<String, dynamic> getFileStatistics() {
    return _fileManagerService.getFileStatistics();
  }
}
