import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/pdf_file.dart';
import '../services/pdf_reader_service.dart';
import '../services/storage_service.dart';

/// PDF控制器
/// 管理PDF閱讀相關的UI狀態和用戶交互
class PdfController extends GetxController {
  final PdfReaderService _pdfReaderService = Get.find<PdfReaderService>();
  final StorageService _storageService = Get.find<StorageService>();

  // PDF文件列表
  final _pdfFiles = <PdfFile>[].obs;
  final _isLoading = false.obs;
  final _selectedFiles = <PdfFile>[].obs;
  final _isSelectionMode = false.obs;

  // Getters
  List<PdfFile> get pdfFiles => _pdfFiles.toList();
  bool get isLoading => _isLoading.value;
  List<PdfFile> get selectedFiles => _selectedFiles.toList();
  bool get isSelectionMode => _isSelectionMode.value;

  // 閱讀器狀態
  PdfFile? get currentPdfFile => _pdfReaderService.currentPdfFile;
  PdfFile? get currentPdf => _pdfReaderService.currentPdfFile;
  int get currentPage => _pdfReaderService.currentPage;
  int get totalPages => _pdfReaderService.totalPages;
  bool get isReaderLoading => _pdfReaderService.isLoading;
  String? get errorMessage => _pdfReaderService.errorMessage;
  PdfReadingSettings get readingSettings => _pdfReaderService.readingSettings;

  // 書籤相關
  final _isCurrentPageBookmarked = false.obs;
  bool get isCurrentPageBookmarked => _isCurrentPageBookmarked.value;

  // 控制器顯示狀態
  final _showControls = true.obs;
  bool get showControls => _showControls.value;

  // 閱讀進度
  double get readProgress => _pdfReaderService.readProgress;

  @override
  void onInit() {
    super.onInit();
    _loadPdfFiles();
  }

  /// 載入PDF文件
  Future<void> loadPdfFiles() async {
    await _loadPdfFiles();
  }

  /// 內部載入PDF文件方法
  Future<void> _loadPdfFiles() async {
    try {
      _isLoading.value = true;

      final files = await _storageService.getPdfFiles();
      final pdfFileList = files.map((file) => PdfFile.fromFile(file)).toList();

      // 按文件名排序
      pdfFileList.sort((a, b) => a.fileName.compareTo(b.fileName));

      _pdfFiles.value = pdfFileList;
    } catch (e) {
      Get.snackbar(
        '載入失敗',
        '無法載入PDF文件: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// 打開PDF文件
  Future<void> openPdf(PdfFile pdfFile) async {
    final success = await _pdfReaderService.openPdf(pdfFile);

    if (success) {
      // 導航到PDF閱讀器頁面
      Get.toNamed('/pdf/reader');
    } else {
      Get.snackbar('打開失敗', '無法打開該PDF文件', snackPosition: SnackPosition.BOTTOM);
    }
  }

  /// 關閉PDF
  Future<void> closePdf() async {
    await _pdfReaderService.closePdf();
  }

  /// 跳轉到指定頁面
  Future<void> goToPage(int page) async {
    await _pdfReaderService.goToPage(page);
  }

  /// 下一頁
  Future<void> nextPage() async {
    await _pdfReaderService.nextPage();
  }

  /// 上一頁
  Future<void> previousPage() async {
    await _pdfReaderService.previousPage();
  }

  /// 第一頁
  Future<void> firstPage() async {
    await _pdfReaderService.firstPage();
  }

  /// 最後一頁
  Future<void> lastPage() async {
    await _pdfReaderService.lastPage();
  }

  /// 放大
  Future<void> zoomIn() async {
    await _pdfReaderService.zoomIn();
  }

  /// 縮小
  Future<void> zoomOut() async {
    await _pdfReaderService.zoomOut();
  }

  /// 重置縮放
  Future<void> resetZoom() async {
    await _pdfReaderService.resetZoom();
  }

  /// 切換書籤
  void toggleBookmark() {
    _pdfReaderService.toggleBookmark(currentPage);
  }

  /// 檢查當前頁是否有書籤
  bool get hasCurrentBookmark {
    return _pdfReaderService.hasBookmarkAt(currentPage);
  }

  /// 獲取所有書籤
  List<int> get bookmarks {
    return _pdfReaderService.bookmarks;
  }

  /// 跳轉到書籤頁面
  Future<void> goToBookmark(int page) async {
    await goToPage(page);
  }

  /// 更新頁面適配模式
  void updateFitMode(PageFitMode fitMode) {
    _pdfReaderService.updateFitMode(fitMode);
  }

  /// 更新閱讀方向
  void updateReadingDirection(ReadingDirection direction) {
    _pdfReaderService.updateReadingDirection(direction);
  }

  /// 更新亮度
  void updateBrightness(double brightness) {
    _pdfReaderService.updateBrightness(brightness);
  }

  /// 切換控制器顯示狀態
  void toggleControlsVisibility() {
    _showControls.value = !_showControls.value;
  }

  /// 顯示控制器
  void showControlsPanel() {
    _showControls.value = true;
  }

  /// 隱藏控制器
  void hideControlsPanel() {
    _showControls.value = false;
  }

  /// 進入選擇模式
  void enterSelectionMode() {
    _isSelectionMode.value = true;
    _selectedFiles.clear();
  }

  /// 退出選擇模式
  void exitSelectionMode() {
    _isSelectionMode.value = false;
    _selectedFiles.clear();
  }

  /// 切換文件選擇狀態
  void toggleFileSelection(PdfFile pdfFile) {
    if (_selectedFiles.contains(pdfFile)) {
      _selectedFiles.remove(pdfFile);
    } else {
      _selectedFiles.add(pdfFile);
    }
  }

  /// 全選
  void selectAll() {
    _selectedFiles.value = pdfFiles.toList();
  }

  /// 取消全選
  void deselectAll() {
    _selectedFiles.clear();
  }

  /// 刪除選中的文件
  Future<void> deleteSelectedFiles() async {
    if (selectedFiles.isEmpty) return;

    try {
      final filesToDelete = selectedFiles.toList();

      // 確認對話框
      final confirmed =
          await Get.dialog<bool>(
            AlertDialog(
              title: const Text('確認刪除'),
              content: Text('確定要刪除 ${filesToDelete.length} 個PDF文件嗎？'),
              actions: [
                TextButton(
                  onPressed: () => Get.back(result: false),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () => Get.back(result: true),
                  child: const Text('刪除'),
                ),
              ],
            ),
          ) ??
          false;

      if (!confirmed) return;

      // 刪除文件
      int deletedCount = 0;
      for (final pdfFile in filesToDelete) {
        final file = File(pdfFile.path);
        final success = await _storageService.deleteFile(file);
        if (success) {
          deletedCount++;
          _pdfFiles.remove(pdfFile);
        }
      }

      exitSelectionMode();

      Get.snackbar(
        '刪除完成',
        '成功刪除 $deletedCount 個文件',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        '刪除失敗',
        '刪除文件時發生錯誤: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// 檢查文件是否被選中
  bool isFileSelected(PdfFile pdfFile) {
    return _selectedFiles.contains(pdfFile);
  }

  /// 檢查是否為當前打開的文件
  bool isCurrentPdf(PdfFile pdfFile) {
    return currentPdfFile?.path == pdfFile.path;
  }

  /// 獲取閱讀進度文字
  String get readProgressText => _pdfReaderService.readProgressText;

  /// 檢查是否可以翻到下一頁
  bool get canGoNext => _pdfReaderService.canGoNext;

  /// 檢查是否可以翻到上一頁
  bool get canGoPrevious => _pdfReaderService.canGoPrevious;

  /// 顯示頁面跳轉對話框
  void showGoToPageDialog() {
    final textController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('跳轉到頁面'),
        content: TextField(
          controller: textController,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: '頁碼 (1-$totalPages)',
            border: const OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          TextButton(
            onPressed: () {
              final page = int.tryParse(textController.text);
              if (page != null && page >= 1 && page <= totalPages) {
                goToPage(page);
                Get.back();
              } else {
                Get.snackbar(
                  '無效頁碼',
                  '請輸入1到$totalPages之間的頁碼',
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
            },
            child: const Text('跳轉'),
          ),
        ],
      ),
    );
  }

  /// 切換當前頁面書籤
  void toggleCurrentPageBookmark() {
    // 簡化實現 - 顯示提示
    Get.snackbar('書籤', '書籤功能開發中', snackPosition: SnackPosition.BOTTOM);
  }

  /// 顯示搜索對話框
  void showSearchDialog() {
    Get.snackbar('搜索', '搜索功能開發中', snackPosition: SnackPosition.BOTTOM);
  }

  /// 顯示目錄
  void showOutline() {
    Get.snackbar('目錄', '目錄功能開發中', snackPosition: SnackPosition.BOTTOM);
  }

  /// 顯示書籤列表
  void showBookmarks() {
    Get.snackbar('書籤', '書籤列表功能開發中', snackPosition: SnackPosition.BOTTOM);
  }

  /// 顯示閱讀器設置
  void showReaderSettings() {
    Get.snackbar('設置', '閱讀器設置功能開發中', snackPosition: SnackPosition.BOTTOM);
  }
}
