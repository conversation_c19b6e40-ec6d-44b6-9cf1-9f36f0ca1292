import 'package:get/get.dart';
import '../services/storage_service.dart';
import '../services/notification_service.dart';
import '../services/http_server_service.dart';
import '../services/audio_player_service.dart';
import '../services/video_player_service.dart';
import '../services/pdf_reader_service.dart';
import '../services/audio_metadata_service.dart';
import '../services/playlist_service.dart';
import '../services/video_playlist_service.dart';
import '../services/video_folder_service.dart';
import '../services/music_folder_service.dart';
import '../services/file_manager_service.dart';
import '../controllers/app_controller.dart';
import '../controllers/file_manager_controller.dart';
import '../utils/app_config.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // 工具服務
    Get.put<AppConfig>(AppConfig(), permanent: true);

    // 核心服務
    Get.lazyPut<StorageService>(() => StorageService(), fenix: true);
    Get.lazyPut<NotificationService>(() => NotificationService(), fenix: true);
    Get.lazyPut<HttpServerService>(() => HttpServerService(), fenix: true);
    Get.lazyPut<AudioMetadataService>(
      () => AudioMetadataService(),
      fenix: true,
    );
    Get.lazyPut<PlaylistService>(() => PlaylistService(), fenix: true);
    Get.lazyPut<VideoPlaylistService>(
      () => VideoPlaylistService(),
      fenix: true,
    );
    Get.lazyPut<VideoFolderService>(
      () => VideoFolderService(Get.find<StorageService>()),
      fenix: true,
    );
    Get.lazyPut<MusicFolderService>(
      () => MusicFolderService(Get.find<StorageService>()),
      fenix: true,
    );
    Get.lazyPut<AudioPlayerService>(() => AudioPlayerService(), fenix: true);
    Get.lazyPut<VideoPlayerService>(() => VideoPlayerService(), fenix: true);
    Get.lazyPut<PdfReaderService>(() => PdfReaderService(), fenix: true);
    Get.lazyPut<FileManagerService>(() => FileManagerService(), fenix: true);

    // 應用控制器
    Get.lazyPut<AppController>(() => AppController(), fenix: true);
    Get.lazyPut<FileManagerController>(
      () => FileManagerController(),
      fenix: true,
    );
  }
}
