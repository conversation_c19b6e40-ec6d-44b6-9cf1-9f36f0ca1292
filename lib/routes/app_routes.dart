import 'package:get/get.dart';
import '../views/home/<USER>';
import '../views/music/music_manager_view.dart';
import '../views/video/video_manager_view.dart';
import '../views/pdf/pdf_view.dart';
import '../views/pdf/pdf_reader_view.dart';
import '../views/server/server_view.dart';
import '../views/file/file_manager_view.dart';
import '../bindings/home_binding.dart';
import '../bindings/music_binding.dart';
import '../bindings/video_binding.dart';
import '../bindings/pdf_binding.dart';
import '../bindings/server_binding.dart';

class AppRoutes {
  static const String home = '/home';
  static const String music = '/music';
  static const String video = '/video';
  static const String pdf = '/pdf';
  static const String pdfReader = '/pdf/reader';
  static const String server = '/server';
  static const String fileManager = '/file-manager';

  static List<GetPage> routes = [
    GetPage(name: home, page: () => const HomeView(), binding: HomeBinding()),
    GetPage(
      name: music,
      page: () => const MusicManagerView(),
      binding: MusicBinding(),
    ),
    GetPage(
      name: video,
      page: () => const VideoManagerView(),
      binding: VideoBinding(),
    ),
    GetPage(name: pdf, page: () => const PdfView(), binding: PdfBinding()),
    GetPage(
      name: pdfReader,
      page: () => const PdfReaderView(),
      binding: PdfBinding(),
    ),
    GetPage(
      name: server,
      page: () => const ServerView(),
      binding: ServerBinding(),
    ),
    GetPage(name: fileManager, page: () => const FileManagerView()),
  ];
}
