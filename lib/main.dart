import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'routes/app_routes.dart';
import 'bindings/initial_binding.dart';
import 'services/storage_service.dart';
import 'services/notification_service.dart';
import 'utils/app_theme.dart';
import 'utils/logger.dart';
import 'utils/performance_monitor.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 設置支持的設備方向
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // 初始化日誌系統
  Logger.setDebugMode(true); // 在生產環境中設為false

  // 初始化性能監控
  PerformanceMonitor.setEnabled(true);

  Logger.info('Application starting...');

  // 初始化背景音頻播放
  await JustAudioBackground.init(
    androidNotificationChannelId: 'com.manlebox.audio',
    androidNotificationChannelName: 'ManLeBox Audio',
    androidNotificationChannelDescription: 'ManLeBox音樂播放通知',
    androidNotificationOngoing: true,
    androidStopForegroundOnPause: true,
    androidNotificationClickStartsActivity: true,
    androidNotificationIcon: 'mipmap/ic_launcher',
    notificationColor: const Color(0xFF2196F3),
    androidShowNotificationBadge: true,
  );

  // 初始化存儲服務
  await Get.putAsync(() => StorageService().init());

  // 初始化通知服務
  await Get.putAsync(() => NotificationService().init());

  // 請求必要權限
  await _requestPermissions();

  Logger.info('Application initialized successfully');

  runApp(const ManLeBoxApp());
}

Future<void> _requestPermissions() async {
  await [
    Permission.storage,
    Permission.manageExternalStorage,
    Permission.notification,
    Permission.audio,
    // 添加本地網絡權限請求
    if (Platform.isIOS) Permission.locationWhenInUse, // 觸發本地網絡權限
  ].request();

  // 主動嘗試使用本地網絡功能以觸發權限請求
  if (Platform.isIOS) {
    try {
      await NetworkInterface.list();
    } catch (e) {
      Logger.debug('Triggered local network permission: $e');
    }
  }
}

class ManLeBoxApp extends StatelessWidget {
  const ManLeBoxApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Man Le Box',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      initialRoute: AppRoutes.home,
      getPages: AppRoutes.routes,
      initialBinding: InitialBinding(),
      debugShowCheckedModeBanner: false,
    );
  }
}
