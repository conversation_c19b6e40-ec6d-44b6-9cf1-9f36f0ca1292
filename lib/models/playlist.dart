import 'audio_file.dart';

/// 播放列表數據模型
/// 包含播放列表的基本信息和歌曲列表
class Playlist {
  /// 播放列表ID
  final String id;

  /// 播放列表名稱
  final String name;

  /// 描述
  final String? description;

  /// 封面圖片路徑
  final String? coverImagePath;

  /// 音頻文件列表
  final List<AudioFile> audioFiles;

  /// 創建時間
  final DateTime createdAt;

  /// 最後修改時間
  final DateTime updatedAt;

  /// 是否為系統預設播放列表
  final bool isSystem;

  /// 播放次數
  final int playCount;

  const Playlist({
    required this.id,
    required this.name,
    this.description,
    this.coverImagePath,
    required this.audioFiles,
    required this.createdAt,
    required this.updatedAt,
    this.isSystem = false,
    this.playCount = 0,
  });

  /// 創建新的播放列表
  factory Playlist.create({
    required String name,
    String? description,
    String? coverImagePath,
    List<AudioFile>? audioFiles,
  }) {
    final now = DateTime.now();
    return Playlist(
      id: _generateId(),
      name: name,
      description: description,
      coverImagePath: coverImagePath,
      audioFiles: audioFiles ?? [],
      createdAt: now,
      updatedAt: now,
    );
  }

  /// 創建系統預設播放列表
  factory Playlist.system({
    required String id,
    required String name,
    String? description,
    List<AudioFile>? audioFiles,
  }) {
    final now = DateTime.now();
    return Playlist(
      id: id,
      name: name,
      description: description,
      audioFiles: audioFiles ?? [],
      createdAt: now,
      updatedAt: now,
      isSystem: true,
    );
  }

  /// 從JSON創建Playlist對象
  factory Playlist.fromJson(Map<String, dynamic> json) {
    return Playlist(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      coverImagePath: json['coverImagePath'] as String?,
      audioFiles: (json['audioFiles'] as List<dynamic>)
          .map((item) => AudioFile.fromJson(item as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isSystem: json['isSystem'] as bool? ?? false,
      playCount: json['playCount'] as int? ?? 0,
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'coverImagePath': coverImagePath,
      'audioFiles': audioFiles.map((file) => file.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isSystem': isSystem,
      'playCount': playCount,
    };
  }

  /// 複製並更新部分屬性
  Playlist copyWith({
    String? id,
    String? name,
    String? description,
    String? coverImagePath,
    List<AudioFile>? audioFiles,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSystem,
    int? playCount,
  }) {
    return Playlist(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      coverImagePath: coverImagePath ?? this.coverImagePath,
      audioFiles: audioFiles ?? this.audioFiles,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSystem: isSystem ?? this.isSystem,
      playCount: playCount ?? this.playCount,
    );
  }

  /// 添加音頻文件
  Playlist addAudioFile(AudioFile audioFile) {
    if (audioFiles.contains(audioFile)) return this;

    final newAudioFiles = List<AudioFile>.from(audioFiles)..add(audioFile);
    return copyWith(audioFiles: newAudioFiles, updatedAt: DateTime.now());
  }

  /// 添加多個音頻文件
  Playlist addAudioFiles(List<AudioFile> files) {
    final newAudioFiles = List<AudioFile>.from(audioFiles);
    for (final file in files) {
      if (!newAudioFiles.contains(file)) {
        newAudioFiles.add(file);
      }
    }

    return copyWith(audioFiles: newAudioFiles, updatedAt: DateTime.now());
  }

  /// 移除音頻文件
  Playlist removeAudioFile(AudioFile audioFile) {
    final newAudioFiles = List<AudioFile>.from(audioFiles)..remove(audioFile);
    return copyWith(audioFiles: newAudioFiles, updatedAt: DateTime.now());
  }

  /// 移除多個音頻文件
  Playlist removeAudioFiles(List<AudioFile> files) {
    final newAudioFiles = List<AudioFile>.from(audioFiles);
    newAudioFiles.removeWhere((file) => files.contains(file));

    return copyWith(audioFiles: newAudioFiles, updatedAt: DateTime.now());
  }

  /// 移動音頻文件位置
  Playlist moveAudioFile(int oldIndex, int newIndex) {
    if (oldIndex < 0 ||
        oldIndex >= audioFiles.length ||
        newIndex < 0 ||
        newIndex >= audioFiles.length) {
      return this;
    }

    final newAudioFiles = List<AudioFile>.from(audioFiles);
    final file = newAudioFiles.removeAt(oldIndex);
    newAudioFiles.insert(newIndex, file);

    return copyWith(audioFiles: newAudioFiles, updatedAt: DateTime.now());
  }

  /// 清空播放列表
  Playlist clear() {
    return copyWith(audioFiles: [], updatedAt: DateTime.now());
  }

  /// 打亂播放列表順序
  Playlist shuffle() {
    final newAudioFiles = List<AudioFile>.from(audioFiles)..shuffle();
    return copyWith(audioFiles: newAudioFiles, updatedAt: DateTime.now());
  }

  /// 按標題排序
  Playlist sortByTitle({bool ascending = true}) {
    final newAudioFiles = List<AudioFile>.from(audioFiles);
    newAudioFiles.sort(
      (a, b) =>
          ascending ? a.title.compareTo(b.title) : b.title.compareTo(a.title),
    );

    return copyWith(audioFiles: newAudioFiles, updatedAt: DateTime.now());
  }

  /// 按藝術家排序
  Playlist sortByArtist({bool ascending = true}) {
    final newAudioFiles = List<AudioFile>.from(audioFiles);
    newAudioFiles.sort(
      (a, b) => ascending
          ? a.artist.compareTo(b.artist)
          : b.artist.compareTo(a.artist),
    );

    return copyWith(audioFiles: newAudioFiles, updatedAt: DateTime.now());
  }

  /// 按專輯排序
  Playlist sortByAlbum({bool ascending = true}) {
    final newAudioFiles = List<AudioFile>.from(audioFiles);
    newAudioFiles.sort(
      (a, b) =>
          ascending ? a.album.compareTo(b.album) : b.album.compareTo(a.album),
    );

    return copyWith(audioFiles: newAudioFiles, updatedAt: DateTime.now());
  }

  /// 按時長排序
  Playlist sortByDuration({bool ascending = true}) {
    final newAudioFiles = List<AudioFile>.from(audioFiles);
    newAudioFiles.sort((a, b) {
      final aDuration = a.duration ?? 0;
      final bDuration = b.duration ?? 0;
      return ascending
          ? aDuration.compareTo(bDuration)
          : bDuration.compareTo(aDuration);
    });

    return copyWith(audioFiles: newAudioFiles, updatedAt: DateTime.now());
  }

  /// 增加播放次數
  Playlist incrementPlayCount() {
    return copyWith(playCount: playCount + 1);
  }

  /// 獲取總時長
  int get totalDuration {
    return audioFiles
        .where((file) => file.duration != null)
        .fold(0, (sum, file) => sum + file.duration!);
  }

  /// 格式化總時長
  String get formattedTotalDuration {
    final totalSeconds = totalDuration ~/ 1000;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;

    if (hours > 0) {
      return '$hours小時$minutes分鐘';
    } else {
      return '$minutes分鐘';
    }
  }

  /// 獲取歌曲數量
  int get songCount => audioFiles.length;

  /// 獲取總文件大小
  int get totalFileSize {
    return audioFiles.fold(0, (sum, file) => sum + file.fileSize);
  }

  /// 格式化總文件大小
  String get formattedTotalFileSize {
    final size = totalFileSize;
    if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// 檢查是否包含指定音頻文件
  bool contains(AudioFile audioFile) {
    return audioFiles.contains(audioFile);
  }

  /// 檢查是否為空
  bool get isEmpty => audioFiles.isEmpty;

  /// 檢查是否不為空
  bool get isNotEmpty => audioFiles.isNotEmpty;

  /// 生成唯一ID
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Playlist && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Playlist(id: $id, name: $name, songCount: $songCount)';
  }
}
