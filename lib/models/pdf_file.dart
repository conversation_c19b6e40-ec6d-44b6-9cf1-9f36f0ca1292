import 'dart:io';

/// PDF文件數據模型
/// 包含PDF文件的基本信息和閱讀記錄
class PdfFile {
  /// 文件路徑
  final String path;
  
  /// 文件名
  final String fileName;
  
  /// 漫畫標題
  final String title;
  
  /// 漫畫描述
  final String? description;
  
  /// 作者
  final String? author;
  
  /// 總頁數
  final int? totalPages;
  
  /// 文件大小（字節）
  final int fileSize;
  
  /// 封面縮略圖路徑
  final String? coverImagePath;
  
  /// 創建時間
  final DateTime createdAt;
  
  /// 最後閱讀時間
  final DateTime? lastReadAt;
  
  /// 當前閱讀頁數（從1開始）
  final int currentPage;
  
  /// 閱讀次數
  final int readCount;
  
  /// 是否已完成閱讀
  final bool isCompleted;
  
  /// 書籤頁面列表
  final List<int> bookmarks;

  const PdfFile({
    required this.path,
    required this.fileName,
    required this.title,
    this.description,
    this.author,
    this.totalPages,
    required this.fileSize,
    this.coverImagePath,
    required this.createdAt,
    this.lastReadAt,
    this.currentPage = 1,
    this.readCount = 0,
    this.isCompleted = false,
    this.bookmarks = const [],
  });

  /// 從文件創建PdfFile對象
  factory PdfFile.fromFile(File file) {
    final fileName = file.path.split('/').last;
    final nameWithoutExtension = fileName.contains('.')
        ? fileName.substring(0, fileName.lastIndexOf('.'))
        : fileName;
    
    return PdfFile(
      path: file.path,
      fileName: fileName,
      title: nameWithoutExtension,
      fileSize: file.lengthSync(),
      createdAt: file.lastModifiedSync(),
    );
  }

  /// 從JSON創建PdfFile對象
  factory PdfFile.fromJson(Map<String, dynamic> json) {
    return PdfFile(
      path: json['path'] as String,
      fileName: json['fileName'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      author: json['author'] as String?,
      totalPages: json['totalPages'] as int?,
      fileSize: json['fileSize'] as int,
      coverImagePath: json['coverImagePath'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastReadAt: json['lastReadAt'] != null 
          ? DateTime.parse(json['lastReadAt'] as String)
          : null,
      currentPage: json['currentPage'] as int? ?? 1,
      readCount: json['readCount'] as int? ?? 0,
      isCompleted: json['isCompleted'] as bool? ?? false,
      bookmarks: (json['bookmarks'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList() ?? [],
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'path': path,
      'fileName': fileName,
      'title': title,
      'description': description,
      'author': author,
      'totalPages': totalPages,
      'fileSize': fileSize,
      'coverImagePath': coverImagePath,
      'createdAt': createdAt.toIso8601String(),
      'lastReadAt': lastReadAt?.toIso8601String(),
      'currentPage': currentPage,
      'readCount': readCount,
      'isCompleted': isCompleted,
      'bookmarks': bookmarks,
    };
  }

  /// 複製並更新部分屬性
  PdfFile copyWith({
    String? path,
    String? fileName,
    String? title,
    String? description,
    String? author,
    int? totalPages,
    int? fileSize,
    String? coverImagePath,
    DateTime? createdAt,
    DateTime? lastReadAt,
    int? currentPage,
    int? readCount,
    bool? isCompleted,
    List<int>? bookmarks,
  }) {
    return PdfFile(
      path: path ?? this.path,
      fileName: fileName ?? this.fileName,
      title: title ?? this.title,
      description: description ?? this.description,
      author: author ?? this.author,
      totalPages: totalPages ?? this.totalPages,
      fileSize: fileSize ?? this.fileSize,
      coverImagePath: coverImagePath ?? this.coverImagePath,
      createdAt: createdAt ?? this.createdAt,
      lastReadAt: lastReadAt ?? this.lastReadAt,
      currentPage: currentPage ?? this.currentPage,
      readCount: readCount ?? this.readCount,
      isCompleted: isCompleted ?? this.isCompleted,
      bookmarks: bookmarks ?? this.bookmarks,
    );
  }

  /// 格式化文件大小
  String get formattedFileSize {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 獲取閱讀進度百分比
  double get readProgress {
    if (totalPages == null || totalPages == 0) return 0.0;
    return (currentPage / totalPages!).clamp(0.0, 1.0);
  }

  /// 獲取閱讀進度文字
  String get readProgressText {
    if (totalPages == null) return '第 $currentPage 頁';
    return '第 $currentPage 頁 / 共 $totalPages 頁';
  }

  /// 獲取閱讀進度百分比文字
  String get readProgressPercentage {
    return '${(readProgress * 100).round()}%';
  }

  /// 獲取文件擴展名
  String get extension {
    return fileName.contains('.') 
        ? fileName.substring(fileName.lastIndexOf('.'))
        : '';
  }

  /// 檢查文件是否存在
  bool get exists {
    return File(path).existsSync();
  }

  /// 檢查是否有閱讀記錄
  bool get hasReadHistory {
    return lastReadAt != null && readCount > 0;
  }

  /// 檢查是否有書籤
  bool get hasBookmarks {
    return bookmarks.isNotEmpty;
  }

  /// 檢查指定頁面是否有書籤
  bool hasBookmarkAt(int page) {
    return bookmarks.contains(page);
  }

  /// 添加書籤
  PdfFile addBookmark(int page) {
    if (hasBookmarkAt(page)) return this;
    final newBookmarks = List<int>.from(bookmarks)..add(page);
    newBookmarks.sort();
    return copyWith(bookmarks: newBookmarks);
  }

  /// 移除書籤
  PdfFile removeBookmark(int page) {
    if (!hasBookmarkAt(page)) return this;
    final newBookmarks = List<int>.from(bookmarks)..remove(page);
    return copyWith(bookmarks: newBookmarks);
  }

  /// 切換書籤狀態
  PdfFile toggleBookmark(int page) {
    return hasBookmarkAt(page) ? removeBookmark(page) : addBookmark(page);
  }

  /// 更新閱讀進度
  PdfFile updateReadProgress(int page) {
    final now = DateTime.now();
    final newIsCompleted = totalPages != null && page >= totalPages!;
    
    return copyWith(
      currentPage: page,
      lastReadAt: now,
      readCount: readCount + 1,
      isCompleted: newIsCompleted,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PdfFile && other.path == path;
  }

  @override
  int get hashCode => path.hashCode;

  @override
  String toString() {
    return 'PdfFile(title: $title, currentPage: $currentPage, totalPages: $totalPages)';
  }
}

/// PDF閱讀設置
class PdfReadingSettings {
  /// 頁面適配模式
  final PageFitMode fitMode;
  
  /// 閱讀方向
  final ReadingDirection direction;
  
  /// 是否顯示頁碼
  final bool showPageNumber;
  
  /// 是否自動保存進度
  final bool autoSaveProgress;
  
  /// 背景顏色
  final int backgroundColor;
  
  /// 亮度
  final double brightness;

  const PdfReadingSettings({
    this.fitMode = PageFitMode.fitWidth,
    this.direction = ReadingDirection.leftToRight,
    this.showPageNumber = true,
    this.autoSaveProgress = true,
    this.backgroundColor = 0xFFFFFFFF,
    this.brightness = 1.0,
  });

  PdfReadingSettings copyWith({
    PageFitMode? fitMode,
    ReadingDirection? direction,
    bool? showPageNumber,
    bool? autoSaveProgress,
    int? backgroundColor,
    double? brightness,
  }) {
    return PdfReadingSettings(
      fitMode: fitMode ?? this.fitMode,
      direction: direction ?? this.direction,
      showPageNumber: showPageNumber ?? this.showPageNumber,
      autoSaveProgress: autoSaveProgress ?? this.autoSaveProgress,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      brightness: brightness ?? this.brightness,
    );
  }
}

/// 頁面適配模式
enum PageFitMode {
  /// 適應寬度
  fitWidth,
  
  /// 適應高度
  fitHeight,
  
  /// 適應頁面
  fitPage,
  
  /// 原始大小
  originalSize,
}

/// 閱讀方向
enum ReadingDirection {
  /// 從左到右
  leftToRight,
  
  /// 從右到左
  rightToLeft,
  
  /// 垂直滾動
  vertical,
}

/// 頁面適配模式擴展
extension PageFitModeExtension on PageFitMode {
  String get displayName {
    switch (this) {
      case PageFitMode.fitWidth:
        return '適應寬度';
      case PageFitMode.fitHeight:
        return '適應高度';
      case PageFitMode.fitPage:
        return '適應頁面';
      case PageFitMode.originalSize:
        return '原始大小';
    }
  }
}

/// 閱讀方向擴展
extension ReadingDirectionExtension on ReadingDirection {
  String get displayName {
    switch (this) {
      case ReadingDirection.leftToRight:
        return '從左到右';
      case ReadingDirection.rightToLeft:
        return '從右到左';
      case ReadingDirection.vertical:
        return '垂直滾動';
    }
  }
}
