import 'dart:io';

/// 視頻文件數據模型
/// 包含視頻文件的基本信息和元數據
class VideoFile {
  /// 文件路徑
  final String path;

  /// 文件名
  final String fileName;

  /// 視頻標題
  final String title;

  /// 視頻描述
  final String? description;

  /// 視頻時長（毫秒）
  final int? duration;

  /// 文件大小（字節）
  final int fileSize;

  /// 視頻寬度
  final int? width;

  /// 視頻高度
  final int? height;

  /// 縮略圖路徑
  final String? thumbnailPath;

  /// 創建時間
  final DateTime createdAt;

  /// 最後播放時間
  final DateTime? lastPlayedAt;

  /// 播放次數
  final int playCount;

  /// 最後播放位置（毫秒）
  final int lastPlayPosition;

  const VideoFile({
    required this.path,
    required this.fileName,
    required this.title,
    this.description,
    this.duration,
    required this.fileSize,
    this.width,
    this.height,
    this.thumbnailPath,
    required this.createdAt,
    this.lastPlayedAt,
    this.playCount = 0,
    this.lastPlayPosition = 0,
  });

  /// 獲取File對象
  File get file => File(path);

  /// 從文件創建VideoFile對象
  factory VideoFile.fromFile(File file) {
    final fileName = file.path.split('/').last;
    final nameWithoutExtension = fileName.contains('.')
        ? fileName.substring(0, fileName.lastIndexOf('.'))
        : fileName;

    return VideoFile(
      path: file.path,
      fileName: fileName,
      title: nameWithoutExtension,
      fileSize: file.lengthSync(),
      createdAt: file.lastModifiedSync(),
    );
  }

  /// 從JSON創建VideoFile對象
  factory VideoFile.fromJson(Map<String, dynamic> json) {
    return VideoFile(
      path: json['path'] as String,
      fileName: json['fileName'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      duration: json['duration'] as int?,
      fileSize: json['fileSize'] as int,
      width: json['width'] as int?,
      height: json['height'] as int?,
      thumbnailPath: json['thumbnailPath'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastPlayedAt: json['lastPlayedAt'] != null
          ? DateTime.parse(json['lastPlayedAt'] as String)
          : null,
      playCount: json['playCount'] as int? ?? 0,
      lastPlayPosition: json['lastPlayPosition'] as int? ?? 0,
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'path': path,
      'fileName': fileName,
      'title': title,
      'description': description,
      'duration': duration,
      'fileSize': fileSize,
      'width': width,
      'height': height,
      'thumbnailPath': thumbnailPath,
      'createdAt': createdAt.toIso8601String(),
      'lastPlayedAt': lastPlayedAt?.toIso8601String(),
      'playCount': playCount,
      'lastPlayPosition': lastPlayPosition,
    };
  }

  /// 複製並更新部分屬性
  VideoFile copyWith({
    String? path,
    String? fileName,
    String? title,
    String? description,
    int? duration,
    int? fileSize,
    int? width,
    int? height,
    String? thumbnailPath,
    DateTime? createdAt,
    DateTime? lastPlayedAt,
    int? playCount,
    int? lastPlayPosition,
  }) {
    return VideoFile(
      path: path ?? this.path,
      fileName: fileName ?? this.fileName,
      title: title ?? this.title,
      description: description ?? this.description,
      duration: duration ?? this.duration,
      fileSize: fileSize ?? this.fileSize,
      width: width ?? this.width,
      height: height ?? this.height,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      createdAt: createdAt ?? this.createdAt,
      lastPlayedAt: lastPlayedAt ?? this.lastPlayedAt,
      playCount: playCount ?? this.playCount,
      lastPlayPosition: lastPlayPosition ?? this.lastPlayPosition,
    );
  }

  /// 格式化文件大小
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    }
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    }
    if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 格式化時長
  String get formattedDuration {
    if (duration == null) return '--:--';

    final totalSeconds = duration! ~/ 1000;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;
    final seconds = totalSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// 格式化播放位置
  String get formattedLastPlayPosition {
    final totalSeconds = lastPlayPosition ~/ 1000;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;
    final seconds = totalSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// 獲取視頻分辨率字符串
  String get resolutionString {
    if (width == null || height == null) return '未知';
    return '${width}x$height';
  }

  /// 獲取視頻寬高比
  double? get aspectRatio {
    if (width == null || height == null || height == 0) return null;
    return width! / height!;
  }

  /// 獲取文件擴展名
  String get extension {
    return fileName.contains('.')
        ? fileName.substring(fileName.lastIndexOf('.'))
        : '';
  }

  /// 檢查文件是否存在
  bool get exists {
    return File(path).existsSync();
  }

  /// 檢查是否有播放記錄
  bool get hasPlayHistory {
    return lastPlayedAt != null && playCount > 0;
  }

  /// 獲取播放進度百分比
  double get playProgress {
    if (duration == null || duration == 0) return 0.0;
    return (lastPlayPosition / duration!).clamp(0.0, 1.0);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoFile && other.path == path;
  }

  @override
  int get hashCode => path.hashCode;

  @override
  String toString() {
    return 'VideoFile(title: $title, duration: $formattedDuration, size: $formattedFileSize)';
  }
}
