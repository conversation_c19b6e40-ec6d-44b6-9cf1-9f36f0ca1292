/// 歌詞行數據模型
/// 包含單行歌詞的時間戳和文本內容
class LyricLine {
  /// 時間戳（毫秒）
  final int timestamp;
  
  /// 歌詞文本
  final String text;
  
  /// 是否為空行
  final bool isEmpty;

  const LyricLine({
    required this.timestamp,
    required this.text,
    this.isEmpty = false,
  });

  /// 創建空行
  factory LyricLine.empty(int timestamp) {
    return LyricLine(
      timestamp: timestamp,
      text: '',
      isEmpty: true,
    );
  }

  /// 從LRC格式解析歌詞行
  /// 格式: [mm:ss.xx]歌詞文本 或 [mm:ss]歌詞文本
  factory LyricLine.fromLrc(String lrcLine) {
    final regex = RegExp(r'\[(\d{2}):(\d{2})(?:\.(\d{2}))?\](.*)');
    final match = regex.firstMatch(lrcLine.trim());
    
    if (match == null) {
      // 如果不匹配LRC格式，返回時間戳為0的歌詞行
      return LyricLine(
        timestamp: 0,
        text: lrcLine.trim(),
      );
    }

    final minutes = int.parse(match.group(1)!);
    final seconds = int.parse(match.group(2)!);
    final centiseconds = match.group(3) != null ? int.parse(match.group(3)!) : 0;
    final text = match.group(4)!.trim();

    final timestamp = (minutes * 60 * 1000) + (seconds * 1000) + (centiseconds * 10);

    return LyricLine(
      timestamp: timestamp,
      text: text,
      isEmpty: text.isEmpty,
    );
  }

  /// 轉換為LRC格式
  String toLrc() {
    final minutes = timestamp ~/ (60 * 1000);
    final seconds = (timestamp % (60 * 1000)) ~/ 1000;
    final centiseconds = (timestamp % 1000) ~/ 10;
    
    return '[${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}.${centiseconds.toString().padLeft(2, '0')}]$text';
  }

  /// 格式化時間戳為可讀格式
  String get formattedTime {
    final minutes = timestamp ~/ (60 * 1000);
    final seconds = (timestamp % (60 * 1000)) ~/ 1000;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LyricLine &&
        other.timestamp == timestamp &&
        other.text == text;
  }

  @override
  int get hashCode => Object.hash(timestamp, text);

  @override
  String toString() {
    return 'LyricLine(timestamp: $timestamp, text: $text)';
  }
}

/// 歌詞數據模型
/// 包含完整的歌詞信息和元數據
class Lyric {
  /// 歌詞行列表
  final List<LyricLine> lines;
  
  /// 歌曲標題
  final String? title;
  
  /// 藝術家
  final String? artist;
  
  /// 專輯
  final String? album;
  
  /// 歌詞作者
  final String? lyricist;
  
  /// 作曲者
  final String? composer;
  
  /// 偏移量（毫秒）
  final int offset;

  const Lyric({
    required this.lines,
    this.title,
    this.artist,
    this.album,
    this.lyricist,
    this.composer,
    this.offset = 0,
  });

  /// 創建空歌詞
  factory Lyric.empty() {
    return const Lyric(lines: []);
  }

  /// 從LRC文件內容解析歌詞
  factory Lyric.fromLrc(String lrcContent) {
    final lines = <LyricLine>[];
    String? title;
    String? artist;
    String? album;
    String? lyricist;
    String? composer;
    int offset = 0;

    for (final line in lrcContent.split('\n')) {
      final trimmedLine = line.trim();
      if (trimmedLine.isEmpty) continue;

      // 解析元數據標籤
      if (trimmedLine.startsWith('[ti:')) {
        title = _extractTagValue(trimmedLine, 'ti');
      } else if (trimmedLine.startsWith('[ar:')) {
        artist = _extractTagValue(trimmedLine, 'ar');
      } else if (trimmedLine.startsWith('[al:')) {
        album = _extractTagValue(trimmedLine, 'al');
      } else if (trimmedLine.startsWith('[by:')) {
        lyricist = _extractTagValue(trimmedLine, 'by');
      } else if (trimmedLine.startsWith('[au:')) {
        composer = _extractTagValue(trimmedLine, 'au');
      } else if (trimmedLine.startsWith('[offset:')) {
        final offsetStr = _extractTagValue(trimmedLine, 'offset');
        offset = int.tryParse(offsetStr ?? '0') ?? 0;
      } else if (trimmedLine.contains(']')) {
        // 解析歌詞行
        try {
          final lyricLine = LyricLine.fromLrc(trimmedLine);
          lines.add(lyricLine);
        } catch (e) {
          // 忽略無法解析的行
        }
      }
    }

    // 按時間戳排序
    lines.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    return Lyric(
      lines: lines,
      title: title,
      artist: artist,
      album: album,
      lyricist: lyricist,
      composer: composer,
      offset: offset,
    );
  }

  /// 提取標籤值
  static String? _extractTagValue(String line, String tag) {
    final regex = RegExp('\\[$tag:(.*)\\]');
    final match = regex.firstMatch(line);
    return match?.group(1)?.trim();
  }

  /// 轉換為LRC格式
  String toLrc() {
    final buffer = StringBuffer();
    
    // 添加元數據
    if (title != null) buffer.writeln('[ti:$title]');
    if (artist != null) buffer.writeln('[ar:$artist]');
    if (album != null) buffer.writeln('[al:$album]');
    if (lyricist != null) buffer.writeln('[by:$lyricist]');
    if (composer != null) buffer.writeln('[au:$composer]');
    if (offset != 0) buffer.writeln('[offset:$offset]');
    
    buffer.writeln();
    
    // 添加歌詞行
    for (final line in lines) {
      buffer.writeln(line.toLrc());
    }
    
    return buffer.toString();
  }

  /// 根據當前播放位置獲取當前歌詞行索引
  int getCurrentLineIndex(int currentPosition) {
    if (lines.isEmpty) return -1;
    
    final adjustedPosition = currentPosition + offset;
    
    for (int i = lines.length - 1; i >= 0; i--) {
      if (lines[i].timestamp <= adjustedPosition) {
        return i;
      }
    }
    
    return -1;
  }

  /// 根據當前播放位置獲取當前歌詞行
  LyricLine? getCurrentLine(int currentPosition) {
    final index = getCurrentLineIndex(currentPosition);
    return index >= 0 ? lines[index] : null;
  }

  /// 根據當前播放位置獲取下一行歌詞
  LyricLine? getNextLine(int currentPosition) {
    final currentIndex = getCurrentLineIndex(currentPosition);
    final nextIndex = currentIndex + 1;
    return nextIndex < lines.length ? lines[nextIndex] : null;
  }

  /// 獲取指定範圍內的歌詞行
  List<LyricLine> getLinesInRange(int startIndex, int count) {
    if (startIndex < 0 || startIndex >= lines.length) return [];
    
    final endIndex = (startIndex + count).clamp(0, lines.length);
    return lines.sublist(startIndex, endIndex);
  }

  /// 檢查是否有歌詞
  bool get hasLyrics => lines.isNotEmpty;

  /// 獲取歌詞總時長
  int get totalDuration {
    if (lines.isEmpty) return 0;
    return lines.last.timestamp;
  }

  /// 複製並更新部分屬性
  Lyric copyWith({
    List<LyricLine>? lines,
    String? title,
    String? artist,
    String? album,
    String? lyricist,
    String? composer,
    int? offset,
  }) {
    return Lyric(
      lines: lines ?? this.lines,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      lyricist: lyricist ?? this.lyricist,
      composer: composer ?? this.composer,
      offset: offset ?? this.offset,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Lyric &&
        other.lines.length == lines.length &&
        other.title == title &&
        other.artist == artist &&
        other.album == album &&
        other.offset == offset;
  }

  @override
  int get hashCode {
    return Object.hash(lines.length, title, artist, album, offset);
  }

  @override
  String toString() {
    return 'Lyric(lines: ${lines.length}, title: $title, artist: $artist)';
  }
}
