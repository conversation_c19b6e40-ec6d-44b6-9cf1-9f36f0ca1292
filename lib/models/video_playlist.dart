import 'video_file.dart';

/// 視頻播放列表數據模型
/// 包含播放列表的基本信息和視頻文件列表
class VideoPlaylist {
  /// 播放列表ID
  final String id;

  /// 播放列表名稱
  final String name;

  /// 描述
  final String? description;

  /// 封面圖片路徑
  final String? coverImagePath;

  /// 視頻文件列表
  final List<VideoFile> videoFiles;

  /// 創建時間
  final DateTime createdAt;

  /// 最後修改時間
  final DateTime updatedAt;

  /// 是否為系統預設播放列表
  final bool isSystem;

  /// 播放次數
  final int playCount;

  /// 播放模式
  final VideoPlaylistMode playMode;

  const VideoPlaylist({
    required this.id,
    required this.name,
    this.description,
    this.coverImagePath,
    required this.videoFiles,
    required this.createdAt,
    required this.updatedAt,
    this.isSystem = false,
    this.playCount = 0,
    this.playMode = VideoPlaylistMode.sequence,
  });

  /// 創建新的播放列表
  factory VideoPlaylist.create({
    required String name,
    String? description,
    String? coverImagePath,
    List<VideoFile>? videoFiles,
    VideoPlaylistMode? playMode,
  }) {
    final now = DateTime.now();
    return VideoPlaylist(
      id: _generateId(),
      name: name,
      description: description,
      coverImagePath: coverImagePath,
      videoFiles: videoFiles ?? [],
      createdAt: now,
      updatedAt: now,
      playMode: playMode ?? VideoPlaylistMode.sequence,
    );
  }

  /// 創建系統預設播放列表
  factory VideoPlaylist.system({
    required String id,
    required String name,
    String? description,
    List<VideoFile>? videoFiles,
  }) {
    final now = DateTime.now();
    return VideoPlaylist(
      id: id,
      name: name,
      description: description,
      videoFiles: videoFiles ?? [],
      createdAt: now,
      updatedAt: now,
      isSystem: true,
    );
  }

  /// 從JSON創建VideoPlaylist對象
  factory VideoPlaylist.fromJson(Map<String, dynamic> json) {
    return VideoPlaylist(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      coverImagePath: json['coverImagePath'] as String?,
      videoFiles: (json['videoFiles'] as List<dynamic>)
          .map((item) => VideoFile.fromJson(item as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isSystem: json['isSystem'] as bool? ?? false,
      playCount: json['playCount'] as int? ?? 0,
      playMode: VideoPlaylistMode.values.firstWhere(
        (mode) => mode.name == (json['playMode'] as String? ?? 'sequence'),
        orElse: () => VideoPlaylistMode.sequence,
      ),
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'coverImagePath': coverImagePath,
      'videoFiles': videoFiles.map((file) => file.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isSystem': isSystem,
      'playCount': playCount,
      'playMode': playMode.name,
    };
  }

  /// 複製並更新部分屬性
  VideoPlaylist copyWith({
    String? id,
    String? name,
    String? description,
    String? coverImagePath,
    List<VideoFile>? videoFiles,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSystem,
    int? playCount,
    VideoPlaylistMode? playMode,
  }) {
    return VideoPlaylist(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      coverImagePath: coverImagePath ?? this.coverImagePath,
      videoFiles: videoFiles ?? this.videoFiles,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSystem: isSystem ?? this.isSystem,
      playCount: playCount ?? this.playCount,
      playMode: playMode ?? this.playMode,
    );
  }

  /// 添加視頻文件
  VideoPlaylist addVideoFile(VideoFile videoFile) {
    if (videoFiles.contains(videoFile)) return this;

    final newVideoFiles = List<VideoFile>.from(videoFiles)..add(videoFile);
    return copyWith(videoFiles: newVideoFiles, updatedAt: DateTime.now());
  }

  /// 添加多個視頻文件
  VideoPlaylist addVideoFiles(List<VideoFile> files) {
    final newVideoFiles = List<VideoFile>.from(videoFiles);
    for (final file in files) {
      if (!newVideoFiles.contains(file)) {
        newVideoFiles.add(file);
      }
    }

    return copyWith(videoFiles: newVideoFiles, updatedAt: DateTime.now());
  }

  /// 移除視頻文件
  VideoPlaylist removeVideoFile(VideoFile videoFile) {
    final newVideoFiles = List<VideoFile>.from(videoFiles)..remove(videoFile);
    return copyWith(videoFiles: newVideoFiles, updatedAt: DateTime.now());
  }

  /// 移除多個視頻文件
  VideoPlaylist removeVideoFiles(List<VideoFile> files) {
    final newVideoFiles = List<VideoFile>.from(videoFiles);
    newVideoFiles.removeWhere((file) => files.contains(file));

    return copyWith(videoFiles: newVideoFiles, updatedAt: DateTime.now());
  }

  /// 移動視頻文件位置
  VideoPlaylist moveVideoFile(int oldIndex, int newIndex) {
    if (oldIndex < 0 ||
        oldIndex >= videoFiles.length ||
        newIndex < 0 ||
        newIndex >= videoFiles.length) {
      return this;
    }

    final newVideoFiles = List<VideoFile>.from(videoFiles);
    final file = newVideoFiles.removeAt(oldIndex);
    newVideoFiles.insert(newIndex, file);

    return copyWith(videoFiles: newVideoFiles, updatedAt: DateTime.now());
  }

  /// 清空播放列表
  VideoPlaylist clear() {
    return copyWith(videoFiles: [], updatedAt: DateTime.now());
  }

  /// 打亂播放列表順序
  VideoPlaylist shuffle() {
    final newVideoFiles = List<VideoFile>.from(videoFiles)..shuffle();
    return copyWith(videoFiles: newVideoFiles, updatedAt: DateTime.now());
  }

  /// 按標題排序
  VideoPlaylist sortByTitle({bool ascending = true}) {
    final newVideoFiles = List<VideoFile>.from(videoFiles);
    newVideoFiles.sort(
      (a, b) =>
          ascending ? a.title.compareTo(b.title) : b.title.compareTo(a.title),
    );

    return copyWith(videoFiles: newVideoFiles, updatedAt: DateTime.now());
  }

  /// 按文件大小排序
  VideoPlaylist sortByFileSize({bool ascending = true}) {
    final newVideoFiles = List<VideoFile>.from(videoFiles);
    newVideoFiles.sort(
      (a, b) => ascending
          ? a.fileSize.compareTo(b.fileSize)
          : b.fileSize.compareTo(a.fileSize),
    );

    return copyWith(videoFiles: newVideoFiles, updatedAt: DateTime.now());
  }

  /// 按時長排序
  VideoPlaylist sortByDuration({bool ascending = true}) {
    final newVideoFiles = List<VideoFile>.from(videoFiles);
    newVideoFiles.sort((a, b) {
      final aDuration = a.duration ?? 0;
      final bDuration = b.duration ?? 0;
      return ascending
          ? aDuration.compareTo(bDuration)
          : bDuration.compareTo(aDuration);
    });

    return copyWith(videoFiles: newVideoFiles, updatedAt: DateTime.now());
  }

  /// 按創建時間排序
  VideoPlaylist sortByCreatedAt({bool ascending = true}) {
    final newVideoFiles = List<VideoFile>.from(videoFiles);
    newVideoFiles.sort(
      (a, b) => ascending
          ? a.createdAt.compareTo(b.createdAt)
          : b.createdAt.compareTo(a.createdAt),
    );

    return copyWith(videoFiles: newVideoFiles, updatedAt: DateTime.now());
  }

  /// 增加播放次數
  VideoPlaylist incrementPlayCount() {
    return copyWith(playCount: playCount + 1);
  }

  /// 獲取總時長
  int get totalDuration {
    return videoFiles
        .where((file) => file.duration != null)
        .fold(0, (sum, file) => sum + file.duration!);
  }

  /// 格式化總時長
  String get formattedTotalDuration {
    final totalSeconds = totalDuration ~/ 1000;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;

    if (hours > 0) {
      return '$hours小時$minutes分鐘';
    } else {
      return '$minutes分鐘';
    }
  }

  /// 獲取視頻數量
  int get videoCount => videoFiles.length;

  /// 獲取總文件大小
  int get totalFileSize {
    return videoFiles.fold(0, (sum, file) => sum + file.fileSize);
  }

  /// 格式化總文件大小
  String get formattedTotalFileSize {
    final size = totalFileSize;
    if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// 檢查是否包含指定視頻文件
  bool contains(VideoFile videoFile) {
    return videoFiles.contains(videoFile);
  }

  /// 檢查是否為空
  bool get isEmpty => videoFiles.isEmpty;

  /// 檢查是否不為空
  bool get isNotEmpty => videoFiles.isNotEmpty;

  /// 生成唯一ID
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoPlaylist && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VideoPlaylist(id: $id, name: $name, videoCount: $videoCount)';
  }
}

/// 視頻播放列表播放模式
enum VideoPlaylistMode {
  /// 順序播放
  sequence,

  /// 單個循環
  repeatOne,

  /// 列表循環
  repeatAll,

  /// 隨機播放
  shuffle,
}

/// 視頻播放列表播放模式擴展
extension VideoPlaylistModeExtension on VideoPlaylistMode {
  /// 獲取顯示名稱
  String get displayName {
    switch (this) {
      case VideoPlaylistMode.sequence:
        return '順序播放';
      case VideoPlaylistMode.repeatOne:
        return '單個循環';
      case VideoPlaylistMode.repeatAll:
        return '列表循環';
      case VideoPlaylistMode.shuffle:
        return '隨機播放';
    }
  }

  /// 獲取圖標
  String get iconName {
    switch (this) {
      case VideoPlaylistMode.sequence:
        return 'playlist_play';
      case VideoPlaylistMode.repeatOne:
        return 'repeat_one';
      case VideoPlaylistMode.repeatAll:
        return 'repeat';
      case VideoPlaylistMode.shuffle:
        return 'shuffle';
    }
  }

  /// 獲取下一個播放模式
  VideoPlaylistMode get next {
    switch (this) {
      case VideoPlaylistMode.sequence:
        return VideoPlaylistMode.repeatAll;
      case VideoPlaylistMode.repeatAll:
        return VideoPlaylistMode.repeatOne;
      case VideoPlaylistMode.repeatOne:
        return VideoPlaylistMode.shuffle;
      case VideoPlaylistMode.shuffle:
        return VideoPlaylistMode.sequence;
    }
  }
}
