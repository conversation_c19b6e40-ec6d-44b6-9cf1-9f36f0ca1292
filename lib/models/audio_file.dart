import 'dart:io';

/// 音頻文件數據模型
/// 包含音頻文件的基本信息和元數據
class AudioFile {
  /// 文件路徑
  final String path;

  /// 文件名
  final String fileName;

  /// 歌曲標題
  final String title;

  /// 藝術家
  final String artist;

  /// 專輯名稱
  final String album;

  /// 歌曲時長（毫秒）
  final int? duration;

  /// 文件大小（字節）
  final int fileSize;

  /// 專輯封面路徑
  final String? albumArtPath;

  /// 創建時間
  final DateTime createdAt;

  /// 最後播放時間
  final DateTime? lastPlayedAt;

  /// 播放次數
  final int playCount;

  const AudioFile({
    required this.path,
    required this.fileName,
    required this.title,
    required this.artist,
    required this.album,
    this.duration,
    required this.fileSize,
    this.albumArtPath,
    required this.createdAt,
    this.lastPlayedAt,
    this.playCount = 0,
  });

  /// 從文件創建AudioFile對象
  factory AudioFile.fromFile(File file) {
    final fileName = file.path.split('/').last;
    final nameWithoutExtension = fileName.contains('.')
        ? fileName.substring(0, fileName.lastIndexOf('.'))
        : fileName;

    return AudioFile(
      path: file.path,
      fileName: fileName,
      title: nameWithoutExtension,
      artist: '未知藝術家',
      album: '未知專輯',
      fileSize: file.lengthSync(),
      createdAt: file.lastModifiedSync(),
    );
  }

  /// 從JSON創建AudioFile對象
  factory AudioFile.fromJson(Map<String, dynamic> json) {
    return AudioFile(
      path: json['path'] as String,
      fileName: json['fileName'] as String,
      title: json['title'] as String,
      artist: json['artist'] as String,
      album: json['album'] as String,
      duration: json['duration'] as int?,
      fileSize: json['fileSize'] as int,
      albumArtPath: json['albumArtPath'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastPlayedAt: json['lastPlayedAt'] != null
          ? DateTime.parse(json['lastPlayedAt'] as String)
          : null,
      playCount: json['playCount'] as int? ?? 0,
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'path': path,
      'fileName': fileName,
      'title': title,
      'artist': artist,
      'album': album,
      'duration': duration,
      'fileSize': fileSize,
      'albumArtPath': albumArtPath,
      'createdAt': createdAt.toIso8601String(),
      'lastPlayedAt': lastPlayedAt?.toIso8601String(),
      'playCount': playCount,
    };
  }

  /// 複製並更新部分屬性
  AudioFile copyWith({
    String? path,
    String? fileName,
    String? title,
    String? artist,
    String? album,
    int? duration,
    int? fileSize,
    String? albumArtPath,
    DateTime? createdAt,
    DateTime? lastPlayedAt,
    int? playCount,
  }) {
    return AudioFile(
      path: path ?? this.path,
      fileName: fileName ?? this.fileName,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      duration: duration ?? this.duration,
      fileSize: fileSize ?? this.fileSize,
      albumArtPath: albumArtPath ?? this.albumArtPath,
      createdAt: createdAt ?? this.createdAt,
      lastPlayedAt: lastPlayedAt ?? this.lastPlayedAt,
      playCount: playCount ?? this.playCount,
    );
  }

  /// 格式化文件大小
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    }
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    }
    if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 格式化時長
  String get formattedDuration {
    if (duration == null) return '--:--';

    final totalSeconds = duration! ~/ 1000;
    final minutes = totalSeconds ~/ 60;
    final seconds = totalSeconds % 60;

    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// 獲取文件擴展名
  String get extension {
    return fileName.contains('.')
        ? fileName.substring(fileName.lastIndexOf('.'))
        : '';
  }

  /// 檢查文件是否存在
  bool get exists {
    return File(path).existsSync();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AudioFile && other.path == path;
  }

  @override
  int get hashCode => path.hashCode;

  @override
  String toString() {
    return 'AudioFile(title: $title, artist: $artist, album: $album)';
  }
}
