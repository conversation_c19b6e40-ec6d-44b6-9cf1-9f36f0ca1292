import 'dart:io';

/// 文件項目數據模型
/// 統一的文件管理數據結構
class FileItem {
  /// 文件路徑
  final String path;
  
  /// 文件名
  final String fileName;
  
  /// 文件類型
  final FileType type;
  
  /// 文件大小（字節）
  final int fileSize;
  
  /// 創建時間
  final DateTime createdAt;
  
  /// 最後修改時間
  final DateTime modifiedAt;
  
  /// 最後訪問時間
  final DateTime? lastAccessedAt;
  
  /// 文件標籤
  final List<String> tags;
  
  /// 是否為收藏
  final bool isFavorite;
  
  /// 文件描述
  final String? description;
  
  /// 縮略圖路徑
  final String? thumbnailPath;
  
  /// 文件元數據
  final Map<String, dynamic> metadata;

  const FileItem({
    required this.path,
    required this.fileName,
    required this.type,
    required this.fileSize,
    required this.createdAt,
    required this.modifiedAt,
    this.lastAccessedAt,
    this.tags = const [],
    this.isFavorite = false,
    this.description,
    this.thumbnailPath,
    this.metadata = const {},
  });

  /// 從文件創建FileItem對象
  factory FileItem.fromFile(File file) {
    final fileName = file.path.split('/').last;
    final extension = fileName.contains('.') 
        ? fileName.substring(fileName.lastIndexOf('.')).toLowerCase()
        : '';
    
    return FileItem(
      path: file.path,
      fileName: fileName,
      type: FileType.fromExtension(extension),
      fileSize: file.lengthSync(),
      createdAt: file.lastModifiedSync(),
      modifiedAt: file.lastModifiedSync(),
    );
  }

  /// 從JSON創建FileItem對象
  factory FileItem.fromJson(Map<String, dynamic> json) {
    return FileItem(
      path: json['path'] as String,
      fileName: json['fileName'] as String,
      type: FileType.values.firstWhere(
        (type) => type.name == (json['type'] as String),
        orElse: () => FileType.unknown,
      ),
      fileSize: json['fileSize'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      modifiedAt: DateTime.parse(json['modifiedAt'] as String),
      lastAccessedAt: json['lastAccessedAt'] != null
          ? DateTime.parse(json['lastAccessedAt'] as String)
          : null,
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      isFavorite: json['isFavorite'] as bool? ?? false,
      description: json['description'] as String?,
      thumbnailPath: json['thumbnailPath'] as String?,
      metadata: (json['metadata'] as Map<String, dynamic>?) ?? {},
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'path': path,
      'fileName': fileName,
      'type': type.name,
      'fileSize': fileSize,
      'createdAt': createdAt.toIso8601String(),
      'modifiedAt': modifiedAt.toIso8601String(),
      'lastAccessedAt': lastAccessedAt?.toIso8601String(),
      'tags': tags,
      'isFavorite': isFavorite,
      'description': description,
      'thumbnailPath': thumbnailPath,
      'metadata': metadata,
    };
  }

  /// 複製並更新部分屬性
  FileItem copyWith({
    String? path,
    String? fileName,
    FileType? type,
    int? fileSize,
    DateTime? createdAt,
    DateTime? modifiedAt,
    DateTime? lastAccessedAt,
    List<String>? tags,
    bool? isFavorite,
    String? description,
    String? thumbnailPath,
    Map<String, dynamic>? metadata,
  }) {
    return FileItem(
      path: path ?? this.path,
      fileName: fileName ?? this.fileName,
      type: type ?? this.type,
      fileSize: fileSize ?? this.fileSize,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
      description: description ?? this.description,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 獲取文件擴展名
  String get extension {
    return fileName.contains('.') 
        ? fileName.substring(fileName.lastIndexOf('.'))
        : '';
  }

  /// 獲取不含擴展名的文件名
  String get nameWithoutExtension {
    return fileName.contains('.')
        ? fileName.substring(0, fileName.lastIndexOf('.'))
        : fileName;
  }

  /// 格式化文件大小
  String get formattedFileSize {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    }
    if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 格式化修改時間
  String get formattedModifiedTime {
    final now = DateTime.now();
    final difference = now.difference(modifiedAt);
    
    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} 年前';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} 個月前';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} 天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} 小時前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} 分鐘前';
    } else {
      return '剛剛';
    }
  }

  /// 檢查文件是否存在
  bool get exists {
    return File(path).existsSync();
  }

  /// 檢查是否有標籤
  bool get hasTags {
    return tags.isNotEmpty;
  }

  /// 檢查是否包含指定標籤
  bool hasTag(String tag) {
    return tags.contains(tag);
  }

  /// 添加標籤
  FileItem addTag(String tag) {
    if (hasTag(tag)) return this;
    final newTags = List<String>.from(tags)..add(tag);
    return copyWith(tags: newTags);
  }

  /// 移除標籤
  FileItem removeTag(String tag) {
    final newTags = List<String>.from(tags)..remove(tag);
    return copyWith(tags: newTags);
  }

  /// 切換收藏狀態
  FileItem toggleFavorite() {
    return copyWith(isFavorite: !isFavorite);
  }

  /// 更新最後訪問時間
  FileItem updateLastAccessed() {
    return copyWith(lastAccessedAt: DateTime.now());
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FileItem && other.path == path;
  }

  @override
  int get hashCode => path.hashCode;

  @override
  String toString() {
    return 'FileItem(fileName: $fileName, type: ${type.name}, size: $formattedFileSize)';
  }
}

/// 文件類型枚舉
enum FileType {
  audio,
  video,
  pdf,
  image,
  document,
  archive,
  unknown;

  /// 從文件擴展名獲取文件類型
  static FileType fromExtension(String extension) {
    switch (extension.toLowerCase()) {
      case '.mp3':
      case '.m4a':
      case '.aac':
      case '.wav':
      case '.flac':
      case '.ogg':
      case '.wma':
        return FileType.audio;
      
      case '.mp4':
      case '.avi':
      case '.mov':
      case '.mkv':
      case '.wmv':
      case '.flv':
      case '.webm':
      case '.m4v':
        return FileType.video;
      
      case '.pdf':
        return FileType.pdf;
      
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
      case '.bmp':
      case '.webp':
      case '.svg':
        return FileType.image;
      
      case '.txt':
      case '.doc':
      case '.docx':
      case '.xls':
      case '.xlsx':
      case '.ppt':
      case '.pptx':
      case '.rtf':
        return FileType.document;
      
      case '.zip':
      case '.rar':
      case '.7z':
      case '.tar':
      case '.gz':
        return FileType.archive;
      
      default:
        return FileType.unknown;
    }
  }

  /// 獲取文件類型顯示名稱
  String get displayName {
    switch (this) {
      case FileType.audio:
        return '音頻';
      case FileType.video:
        return '視頻';
      case FileType.pdf:
        return 'PDF';
      case FileType.image:
        return '圖片';
      case FileType.document:
        return '文檔';
      case FileType.archive:
        return '壓縮包';
      case FileType.unknown:
        return '未知';
    }
  }

  /// 獲取文件類型圖標
  String get iconName {
    switch (this) {
      case FileType.audio:
        return 'audio_file';
      case FileType.video:
        return 'video_file';
      case FileType.pdf:
        return 'picture_as_pdf';
      case FileType.image:
        return 'image';
      case FileType.document:
        return 'description';
      case FileType.archive:
        return 'archive';
      case FileType.unknown:
        return 'insert_drive_file';
    }
  }

  /// 獲取支持的文件擴展名
  List<String> get supportedExtensions {
    switch (this) {
      case FileType.audio:
        return ['.mp3', '.m4a', '.aac', '.wav', '.flac', '.ogg', '.wma'];
      case FileType.video:
        return ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'];
      case FileType.pdf:
        return ['.pdf'];
      case FileType.image:
        return ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];
      case FileType.document:
        return ['.txt', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.rtf'];
      case FileType.archive:
        return ['.zip', '.rar', '.7z', '.tar', '.gz'];
      case FileType.unknown:
        return [];
    }
  }
}

/// 文件排序類型
enum FileSortType {
  name,
  size,
  type,
  modifiedTime,
  createdTime,
}

/// 文件排序類型擴展
extension FileSortTypeExtension on FileSortType {
  /// 獲取排序類型顯示名稱
  String get displayName {
    switch (this) {
      case FileSortType.name:
        return '名稱';
      case FileSortType.size:
        return '大小';
      case FileSortType.type:
        return '類型';
      case FileSortType.modifiedTime:
        return '修改時間';
      case FileSortType.createdTime:
        return '創建時間';
    }
  }
}
