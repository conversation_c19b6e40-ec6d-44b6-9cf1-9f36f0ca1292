import 'dart:io';
import 'video_file.dart';

/// 視頻文件夾數據模型
/// 用於組織和管理視頻文件的文件夾結構
class VideoFolder {
  /// 文件夾ID
  final String id;

  /// 文件夾名稱
  final String name;

  /// 文件夾路徑
  final String path;

  /// 父文件夾ID（null表示根文件夾）
  final String? parentId;

  /// 文件夾描述
  final String? description;

  /// 創建時間
  final DateTime createdAt;

  /// 更新時間
  final DateTime updatedAt;

  /// 是否為系統文件夾
  final bool isSystem;

  /// 文件夾顏色（用於UI顯示）
  final int? color;

  /// 文件夾圖標（用於UI顯示）
  final String? icon;

  const VideoFolder({
    required this.id,
    required this.name,
    required this.path,
    this.parentId,
    this.description,
    required this.createdAt,
    required this.updatedAt,
    this.isSystem = false,
    this.color,
    this.icon,
  });

  /// 獲取Directory對象
  Directory get directory => Directory(path);

  /// 檢查文件夾是否存在
  bool get exists => directory.existsSync();

  /// 從JSON創建VideoFolder對象
  factory VideoFolder.fromJson(Map<String, dynamic> json) {
    return VideoFolder(
      id: json['id'] as String,
      name: json['name'] as String,
      path: json['path'] as String,
      parentId: json['parentId'] as String?,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isSystem: json['isSystem'] as bool? ?? false,
      color: json['color'] as int?,
      icon: json['icon'] as String?,
    );
  }

  /// 轉換為JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'path': path,
      'parentId': parentId,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isSystem': isSystem,
      'color': color,
      'icon': icon,
    };
  }

  /// 複製並更新部分屬性
  VideoFolder copyWith({
    String? id,
    String? name,
    String? path,
    String? parentId,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSystem,
    int? color,
    String? icon,
  }) {
    return VideoFolder(
      id: id ?? this.id,
      name: name ?? this.name,
      path: path ?? this.path,
      parentId: parentId ?? this.parentId,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSystem: isSystem ?? this.isSystem,
      color: color ?? this.color,
      icon: icon ?? this.icon,
    );
  }

  /// 創建系統默認文件夾
  static VideoFolder createSystemFolder({
    required String id,
    required String name,
    required String path,
    String? description,
    int? color,
    String? icon,
  }) {
    final now = DateTime.now();
    return VideoFolder(
      id: id,
      name: name,
      path: path,
      description: description,
      createdAt: now,
      updatedAt: now,
      isSystem: true,
      color: color,
      icon: icon,
    );
  }

  /// 創建用戶文件夾
  static VideoFolder createUserFolder({
    required String id,
    required String name,
    required String path,
    String? parentId,
    String? description,
    int? color,
    String? icon,
  }) {
    final now = DateTime.now();
    return VideoFolder(
      id: id,
      name: name,
      path: path,
      parentId: parentId,
      description: description,
      createdAt: now,
      updatedAt: now,
      isSystem: false,
      color: color,
      icon: icon,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoFolder && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VideoFolder(id: $id, name: $name, path: $path)';
  }
}

/// 視頻文件夾樹節點
/// 用於構建文件夾樹狀結構
class VideoFolderNode {
  /// 文件夾信息
  final VideoFolder folder;

  /// 子文件夾
  final List<VideoFolderNode> children;

  /// 文件夾中的視頻文件
  final List<VideoFile> videoFiles;

  /// 父節點
  VideoFolderNode? parent;

  VideoFolderNode({
    required this.folder,
    this.children = const [],
    this.videoFiles = const [],
    this.parent,
  });

  /// 是否為根節點
  bool get isRoot => parent == null;

  /// 是否為葉子節點
  bool get isLeaf => children.isEmpty;

  /// 獲取節點深度
  int get depth {
    int depth = 0;
    VideoFolderNode? current = parent;
    while (current != null) {
      depth++;
      current = current.parent;
    }
    return depth;
  }

  /// 獲取文件夾路徑
  String get fullPath {
    if (isRoot) return folder.name;
    return '${parent!.fullPath}/${folder.name}';
  }

  /// 添加子文件夾
  void addChild(VideoFolderNode child) {
    child.parent = this;
    children.add(child);
  }

  /// 移除子文件夾
  void removeChild(VideoFolderNode child) {
    child.parent = null;
    children.remove(child);
  }

  /// 查找子文件夾
  VideoFolderNode? findChild(String folderId) {
    for (final child in children) {
      if (child.folder.id == folderId) return child;
      final found = child.findChild(folderId);
      if (found != null) return found;
    }
    return null;
  }

  /// 獲取所有後代文件夾
  List<VideoFolderNode> getAllDescendants() {
    final descendants = <VideoFolderNode>[];
    for (final child in children) {
      descendants.add(child);
      descendants.addAll(child.getAllDescendants());
    }
    return descendants;
  }

  /// 獲取文件夾中的視頻文件總數
  int get totalVideoCount {
    int count = videoFiles.length;
    for (final child in children) {
      count += child.totalVideoCount;
    }
    return count;
  }

  @override
  String toString() {
    return 'VideoFolderNode(folder: ${folder.name}, children: ${children.length}, videos: ${videoFiles.length})';
  }
}
