/// 視頻播放器狀態枚舉
/// 定義視頻播放器的各種狀態
enum VideoPlayerState {
  /// 停止狀態
  stopped,
  
  /// 播放中
  playing,
  
  /// 暫停中
  paused,
  
  /// 緩衝中
  buffering,
  
  /// 載入中
  loading,
  
  /// 錯誤狀態
  error,
}

/// 視頻播放模式枚舉
/// 定義不同的視頻播放模式
enum VideoPlayMode {
  /// 順序播放
  sequence,
  
  /// 單個循環
  repeatOne,
  
  /// 列表循環
  repeatAll,
}

/// 視頻播放器顯示模式枚舉
enum VideoDisplayMode {
  /// 正常模式
  normal,
  
  /// 全屏模式
  fullscreen,
  
  /// 畫中畫模式
  pictureInPicture,
}

/// 視頻播放模式擴展方法
extension VideoPlayModeExtension on VideoPlayMode {
  /// 獲取播放模式的顯示名稱
  String get displayName {
    switch (this) {
      case VideoPlayMode.sequence:
        return '順序播放';
      case VideoPlayMode.repeatOne:
        return '單個循環';
      case VideoPlayMode.repeatAll:
        return '列表循環';
    }
  }

  /// 獲取播放模式的圖標
  String get icon {
    switch (this) {
      case VideoPlayMode.sequence:
        return '▶️';
      case VideoPlayMode.repeatOne:
        return '🔂';
      case VideoPlayMode.repeatAll:
        return '🔁';
    }
  }

  /// 獲取下一個播放模式
  VideoPlayMode get next {
    switch (this) {
      case VideoPlayMode.sequence:
        return VideoPlayMode.repeatAll;
      case VideoPlayMode.repeatAll:
        return VideoPlayMode.repeatOne;
      case VideoPlayMode.repeatOne:
        return VideoPlayMode.sequence;
    }
  }
}

/// 視頻播放器狀態擴展方法
extension VideoPlayerStateExtension on VideoPlayerState {
  /// 獲取播放器狀態的顯示名稱
  String get displayName {
    switch (this) {
      case VideoPlayerState.stopped:
        return '已停止';
      case VideoPlayerState.playing:
        return '播放中';
      case VideoPlayerState.paused:
        return '已暫停';
      case VideoPlayerState.buffering:
        return '緩衝中';
      case VideoPlayerState.loading:
        return '載入中';
      case VideoPlayerState.error:
        return '錯誤';
    }
  }

  /// 檢查是否正在播放
  bool get isPlaying => this == VideoPlayerState.playing;

  /// 檢查是否已暫停
  bool get isPaused => this == VideoPlayerState.paused;

  /// 檢查是否已停止
  bool get isStopped => this == VideoPlayerState.stopped;

  /// 檢查是否正在載入
  bool get isLoading => this == VideoPlayerState.loading || this == VideoPlayerState.buffering;

  /// 檢查是否有錯誤
  bool get hasError => this == VideoPlayerState.error;
}

/// 視頻顯示模式擴展方法
extension VideoDisplayModeExtension on VideoDisplayMode {
  /// 獲取顯示模式的名稱
  String get displayName {
    switch (this) {
      case VideoDisplayMode.normal:
        return '正常模式';
      case VideoDisplayMode.fullscreen:
        return '全屏模式';
      case VideoDisplayMode.pictureInPicture:
        return '畫中畫模式';
    }
  }

  /// 檢查是否為全屏模式
  bool get isFullscreen => this == VideoDisplayMode.fullscreen;

  /// 檢查是否為畫中畫模式
  bool get isPictureInPicture => this == VideoDisplayMode.pictureInPicture;

  /// 檢查是否為正常模式
  bool get isNormal => this == VideoDisplayMode.normal;
}

/// 視頻播放器完整狀態類
/// 包含當前播放的所有狀態信息
class VideoPlayerFullState {
  /// 播放器狀態
  final VideoPlayerState playerState;
  
  /// 當前播放的視頻文件路徑
  final String? currentVideoPath;
  
  /// 當前播放位置（毫秒）
  final int currentPosition;
  
  /// 視頻總時長（毫秒）
  final int totalDuration;
  
  /// 播放模式
  final VideoPlayMode playMode;
  
  /// 顯示模式
  final VideoDisplayMode displayMode;
  
  /// 音量（0.0 - 1.0）
  final double volume;
  
  /// 播放速度（0.25 - 2.0）
  final double speed;
  
  /// 是否靜音
  final bool isMuted;
  
  /// 是否顯示控制器
  final bool showControls;
  
  /// 錯誤訊息
  final String? errorMessage;

  const VideoPlayerFullState({
    this.playerState = VideoPlayerState.stopped,
    this.currentVideoPath,
    this.currentPosition = 0,
    this.totalDuration = 0,
    this.playMode = VideoPlayMode.sequence,
    this.displayMode = VideoDisplayMode.normal,
    this.volume = 1.0,
    this.speed = 1.0,
    this.isMuted = false,
    this.showControls = true,
    this.errorMessage,
  });

  /// 複製並更新部分屬性
  VideoPlayerFullState copyWith({
    VideoPlayerState? playerState,
    String? currentVideoPath,
    int? currentPosition,
    int? totalDuration,
    VideoPlayMode? playMode,
    VideoDisplayMode? displayMode,
    double? volume,
    double? speed,
    bool? isMuted,
    bool? showControls,
    String? errorMessage,
  }) {
    return VideoPlayerFullState(
      playerState: playerState ?? this.playerState,
      currentVideoPath: currentVideoPath ?? this.currentVideoPath,
      currentPosition: currentPosition ?? this.currentPosition,
      totalDuration: totalDuration ?? this.totalDuration,
      playMode: playMode ?? this.playMode,
      displayMode: displayMode ?? this.displayMode,
      volume: volume ?? this.volume,
      speed: speed ?? this.speed,
      isMuted: isMuted ?? this.isMuted,
      showControls: showControls ?? this.showControls,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// 格式化當前播放位置
  String get formattedCurrentPosition {
    final totalSeconds = currentPosition ~/ 1000;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;
    final seconds = totalSeconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// 格式化總時長
  String get formattedTotalDuration {
    final totalSeconds = totalDuration ~/ 1000;
    final hours = totalSeconds ~/ 3600;
    final minutes = (totalSeconds % 3600) ~/ 60;
    final seconds = totalSeconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// 獲取播放進度（0.0 - 1.0）
  double get progress {
    if (totalDuration == 0) return 0.0;
    return (currentPosition / totalDuration).clamp(0.0, 1.0);
  }

  /// 檢查是否有視頻正在播放
  bool get hasCurrentVideo => currentVideoPath != null;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoPlayerFullState &&
        other.playerState == playerState &&
        other.currentVideoPath == currentVideoPath &&
        other.currentPosition == currentPosition &&
        other.totalDuration == totalDuration &&
        other.playMode == playMode &&
        other.displayMode == displayMode &&
        other.volume == volume &&
        other.speed == speed &&
        other.isMuted == isMuted &&
        other.showControls == showControls &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return Object.hash(
      playerState,
      currentVideoPath,
      currentPosition,
      totalDuration,
      playMode,
      displayMode,
      volume,
      speed,
      isMuted,
      showControls,
      errorMessage,
    );
  }

  @override
  String toString() {
    return 'VideoPlayerFullState('
        'playerState: $playerState, '
        'currentVideoPath: $currentVideoPath, '
        'currentPosition: $currentPosition, '
        'totalDuration: $totalDuration, '
        'playMode: $playMode, '
        'displayMode: $displayMode, '
        'volume: $volume, '
        'speed: $speed, '
        'isMuted: $isMuted, '
        'showControls: $showControls, '
        'errorMessage: $errorMessage'
        ')';
  }
}
