/// 播放器狀態枚舉
/// 定義音頻播放器的各種狀態
enum PlayerState {
  /// 停止狀態
  stopped,
  
  /// 播放中
  playing,
  
  /// 暫停中
  paused,
  
  /// 緩衝中
  buffering,
  
  /// 載入中
  loading,
  
  /// 錯誤狀態
  error,
}

/// 播放模式枚舉
/// 定義不同的播放模式
enum PlayMode {
  /// 順序播放
  sequence,
  
  /// 單曲循環
  repeatOne,
  
  /// 列表循環
  repeatAll,
  
  /// 隨機播放
  shuffle,
}

/// 播放模式擴展方法
extension PlayModeExtension on PlayMode {
  /// 獲取播放模式的顯示名稱
  String get displayName {
    switch (this) {
      case PlayMode.sequence:
        return '順序播放';
      case PlayMode.repeatOne:
        return '單曲循環';
      case PlayMode.repeatAll:
        return '列表循環';
      case PlayMode.shuffle:
        return '隨機播放';
    }
  }

  /// 獲取播放模式的圖標
  String get icon {
    switch (this) {
      case PlayMode.sequence:
        return '▶️';
      case PlayMode.repeatOne:
        return '🔂';
      case PlayMode.repeatAll:
        return '🔁';
      case PlayMode.shuffle:
        return '🔀';
    }
  }

  /// 獲取下一個播放模式
  PlayMode get next {
    switch (this) {
      case PlayMode.sequence:
        return PlayMode.repeatAll;
      case PlayMode.repeatAll:
        return PlayMode.repeatOne;
      case PlayMode.repeatOne:
        return PlayMode.shuffle;
      case PlayMode.shuffle:
        return PlayMode.sequence;
    }
  }
}

/// 播放器狀態擴展方法
extension PlayerStateExtension on PlayerState {
  /// 獲取播放器狀態的顯示名稱
  String get displayName {
    switch (this) {
      case PlayerState.stopped:
        return '已停止';
      case PlayerState.playing:
        return '播放中';
      case PlayerState.paused:
        return '已暫停';
      case PlayerState.buffering:
        return '緩衝中';
      case PlayerState.loading:
        return '載入中';
      case PlayerState.error:
        return '錯誤';
    }
  }

  /// 檢查是否正在播放
  bool get isPlaying => this == PlayerState.playing;

  /// 檢查是否已暫停
  bool get isPaused => this == PlayerState.paused;

  /// 檢查是否已停止
  bool get isStopped => this == PlayerState.stopped;

  /// 檢查是否正在載入
  bool get isLoading => this == PlayerState.loading || this == PlayerState.buffering;

  /// 檢查是否有錯誤
  bool get hasError => this == PlayerState.error;
}

/// 音頻播放器完整狀態類
/// 包含當前播放的所有狀態信息
class AudioPlayerState {
  /// 播放器狀態
  final PlayerState playerState;
  
  /// 當前播放的音頻文件
  final String? currentAudioPath;
  
  /// 當前播放位置（毫秒）
  final int currentPosition;
  
  /// 音頻總時長（毫秒）
  final int totalDuration;
  
  /// 播放模式
  final PlayMode playMode;
  
  /// 音量（0.0 - 1.0）
  final double volume;
  
  /// 播放速度（0.5 - 2.0）
  final double speed;
  
  /// 是否靜音
  final bool isMuted;
  
  /// 錯誤訊息
  final String? errorMessage;

  const AudioPlayerState({
    this.playerState = PlayerState.stopped,
    this.currentAudioPath,
    this.currentPosition = 0,
    this.totalDuration = 0,
    this.playMode = PlayMode.sequence,
    this.volume = 1.0,
    this.speed = 1.0,
    this.isMuted = false,
    this.errorMessage,
  });

  /// 複製並更新部分屬性
  AudioPlayerState copyWith({
    PlayerState? playerState,
    String? currentAudioPath,
    int? currentPosition,
    int? totalDuration,
    PlayMode? playMode,
    double? volume,
    double? speed,
    bool? isMuted,
    String? errorMessage,
  }) {
    return AudioPlayerState(
      playerState: playerState ?? this.playerState,
      currentAudioPath: currentAudioPath ?? this.currentAudioPath,
      currentPosition: currentPosition ?? this.currentPosition,
      totalDuration: totalDuration ?? this.totalDuration,
      playMode: playMode ?? this.playMode,
      volume: volume ?? this.volume,
      speed: speed ?? this.speed,
      isMuted: isMuted ?? this.isMuted,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// 格式化當前播放位置
  String get formattedCurrentPosition {
    final totalSeconds = currentPosition ~/ 1000;
    final minutes = totalSeconds ~/ 60;
    final seconds = totalSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// 格式化總時長
  String get formattedTotalDuration {
    final totalSeconds = totalDuration ~/ 1000;
    final minutes = totalSeconds ~/ 60;
    final seconds = totalSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// 獲取播放進度（0.0 - 1.0）
  double get progress {
    if (totalDuration == 0) return 0.0;
    return (currentPosition / totalDuration).clamp(0.0, 1.0);
  }

  /// 檢查是否有音頻正在播放
  bool get hasCurrentAudio => currentAudioPath != null;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AudioPlayerState &&
        other.playerState == playerState &&
        other.currentAudioPath == currentAudioPath &&
        other.currentPosition == currentPosition &&
        other.totalDuration == totalDuration &&
        other.playMode == playMode &&
        other.volume == volume &&
        other.speed == speed &&
        other.isMuted == isMuted &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return Object.hash(
      playerState,
      currentAudioPath,
      currentPosition,
      totalDuration,
      playMode,
      volume,
      speed,
      isMuted,
      errorMessage,
    );
  }

  @override
  String toString() {
    return 'AudioPlayerState('
        'playerState: $playerState, '
        'currentAudioPath: $currentAudioPath, '
        'currentPosition: $currentPosition, '
        'totalDuration: $totalDuration, '
        'playMode: $playMode, '
        'volume: $volume, '
        'speed: $speed, '
        'isMuted: $isMuted, '
        'errorMessage: $errorMessage'
        ')';
  }
}
