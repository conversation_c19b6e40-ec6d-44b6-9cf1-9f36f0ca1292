import 'package:get/get.dart';
import 'logger.dart';

/// 錯誤處理工具類
/// 提供統一的錯誤處理和用戶提示
class ErrorUtils {
  /// 顯示錯誤提示
  static void showError(String title, String message, {Duration? duration}) {
    if (!Get.testMode) {
      Get.snackbar(
        title,
        message,
        snackPosition: SnackPosition.BOTTOM,
        duration: duration ?? const Duration(seconds: 3),
        backgroundColor: Get.theme.colorScheme.error.withValues(alpha: 0.9),
        colorText: Get.theme.colorScheme.onError,
      );
    }
    Logger.error('$title: $message');
  }

  /// 顯示成功提示
  static void showSuccess(String title, String message, {Duration? duration}) {
    if (!Get.testMode) {
      Get.snackbar(
        title,
        message,
        snackPosition: SnackPosition.BOTTOM,
        duration: duration ?? const Duration(seconds: 2),
        backgroundColor: Get.theme.colorScheme.primary.withValues(alpha: 0.9),
        colorText: Get.theme.colorScheme.onPrimary,
      );
    }
    Logger.info('$title: $message');
  }

  /// 顯示警告提示
  static void showWarning(String title, String message, {Duration? duration}) {
    if (!Get.testMode) {
      Get.snackbar(
        title,
        message,
        snackPosition: SnackPosition.BOTTOM,
        duration: duration ?? const Duration(seconds: 3),
        backgroundColor: Get.theme.colorScheme.secondary.withValues(alpha: 0.9),
        colorText: Get.theme.colorScheme.onSecondary,
      );
    }
    Logger.warning('$title: $message');
  }

  /// 安全執行異步操作
  static Future<T?> safeExecute<T>(
    Future<T> Function() operation, {
    String? context,
    bool showErrorSnackbar = true,
    T? defaultValue,
  }) async {
    try {
      return await operation();
    } catch (e, stackTrace) {
      final errorContext = context ?? '操作';
      Logger.error('$errorContext失敗', error: e, stackTrace: stackTrace);
      
      if (showErrorSnackbar) {
        showError('操作失敗', '$errorContext時發生錯誤');
      }
      
      return defaultValue;
    }
  }

  /// 安全執行同步操作
  static T? safeExecuteSync<T>(
    T Function() operation, {
    String? context,
    bool showErrorSnackbar = true,
    T? defaultValue,
  }) {
    try {
      return operation();
    } catch (e, stackTrace) {
      final errorContext = context ?? '操作';
      Logger.error('$errorContext失敗', error: e, stackTrace: stackTrace);
      
      if (showErrorSnackbar) {
        showError('操作失敗', '$errorContext時發生錯誤');
      }
      
      return defaultValue;
    }
  }

  /// 處理文件操作錯誤
  static void handleFileError(String operation, String filePath, Object error) {
    String message;
    
    if (error.toString().contains('Permission denied')) {
      message = '沒有權限訪問文件';
    } else if (error.toString().contains('No such file or directory')) {
      message = '文件不存在';
    } else if (error.toString().contains('File exists')) {
      message = '文件已存在';
    } else {
      message = '文件操作失敗';
    }
    
    showError('$operation失敗', message);
    Logger.error('文件操作失敗: $operation - $filePath', error: error);
  }

  /// 處理網絡錯誤
  static void handleNetworkError(Object error) {
    String message;
    
    if (error.toString().contains('SocketException')) {
      message = '網絡連接失敗，請檢查網絡設置';
    } else if (error.toString().contains('TimeoutException')) {
      message = '網絡請求超時，請稍後重試';
    } else if (error.toString().contains('FormatException')) {
      message = '數據格式錯誤';
    } else {
      message = '網絡請求失敗';
    }
    
    showError('網絡錯誤', message);
    Logger.error('網絡錯誤', error: error);
  }

  /// 處理播放器錯誤
  static void handlePlayerError(String playerType, Object error) {
    String message;
    
    if (error.toString().contains('codec')) {
      message = '不支持的文件格式';
    } else if (error.toString().contains('permission')) {
      message = '沒有權限訪問文件';
    } else if (error.toString().contains('not found')) {
      message = '文件不存在或已被移動';
    } else {
      message = '播放失敗';
    }
    
    showError('$playerType播放錯誤', message);
    Logger.error('$playerType播放錯誤', error: error);
  }
}
