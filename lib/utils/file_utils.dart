import 'dart:io';
import 'package:path/path.dart' as path;
import 'logger.dart';

/// 文件操作工具類
/// 提供通用的文件操作方法，減少重複代碼
class FileUtils {
  /// 檢查文件是否存在
  static bool exists(String filePath) {
    return File(filePath).existsSync();
  }

  /// 檢查目錄是否存在
  static bool directoryExists(String dirPath) {
    return Directory(dirPath).existsSync();
  }

  /// 創建目錄（如果不存在）
  static Future<Directory> ensureDirectory(String dirPath) async {
    final dir = Directory(dirPath);
    if (!dir.existsSync()) {
      await dir.create(recursive: true);
    }
    return dir;
  }

  /// 安全移動文件（先嘗試rename，失敗則copy+delete）
  static Future<bool> moveFile(String sourcePath, String targetPath) async {
    try {
      final sourceFile = File(sourcePath);
      if (!sourceFile.existsSync()) {
        Logger.warning('源文件不存在: $sourcePath');
        return false;
      }

      // 確保目標目錄存在
      final targetDir = Directory(path.dirname(targetPath));
      await ensureDirectory(targetDir.path);

      // 檢查目標文件是否已存在
      if (File(targetPath).existsSync()) {
        Logger.warning('目標文件已存在: $targetPath');
        return false;
      }

      // 嘗試直接移動
      try {
        await sourceFile.rename(targetPath);
        Logger.debug('文件移動成功 (rename): $sourcePath -> $targetPath');
        return true;
      } catch (e) {
        // rename失敗，嘗試copy+delete
        Logger.debug('rename失敗，嘗試copy+delete: $e');
        await sourceFile.copy(targetPath);
        await sourceFile.delete();
        Logger.debug('文件移動成功 (copy+delete): $sourcePath -> $targetPath');
        return true;
      }
    } catch (e) {
      Logger.error('文件移動失敗: $sourcePath -> $targetPath', error: e);
      return false;
    }
  }

  /// 安全複製文件
  static Future<bool> copyFile(String sourcePath, String targetPath) async {
    try {
      final sourceFile = File(sourcePath);
      if (!sourceFile.existsSync()) {
        Logger.warning('源文件不存在: $sourcePath');
        return false;
      }

      // 確保目標目錄存在
      final targetDir = Directory(path.dirname(targetPath));
      await ensureDirectory(targetDir.path);

      await sourceFile.copy(targetPath);
      Logger.debug('文件複製成功: $sourcePath -> $targetPath');
      return true;
    } catch (e) {
      Logger.error('文件複製失敗: $sourcePath -> $targetPath', error: e);
      return false;
    }
  }

  /// 安全刪除文件
  static Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        Logger.debug('文件不存在，無需刪除: $filePath');
        return true;
      }

      await file.delete();
      Logger.debug('文件刪除成功: $filePath');
      return true;
    } catch (e) {
      Logger.error('文件刪除失敗: $filePath', error: e);
      return false;
    }
  }

  /// 生成唯一文件名（如果文件已存在）
  static String generateUniqueFileName(String dirPath, String fileName) {
    final extension = path.extension(fileName);
    final nameWithoutExt = path.basenameWithoutExtension(fileName);
    
    String newPath = path.join(dirPath, fileName);
    int counter = 1;
    
    while (File(newPath).existsSync()) {
      final newFileName = '${nameWithoutExt}_$counter$extension';
      newPath = path.join(dirPath, newFileName);
      counter++;
    }
    
    return newPath;
  }

  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 獲取文件擴展名（小寫）
  static String getExtension(String filePath) {
    return path.extension(filePath).toLowerCase();
  }

  /// 檢查文件是否為指定類型
  static bool isFileType(String filePath, List<String> extensions) {
    final ext = getExtension(filePath);
    return extensions.contains(ext);
  }

  /// 音頻文件擴展名
  static const audioExtensions = ['.mp3', '.wav', '.flac', '.aac', '.m4a', '.ogg'];
  
  /// 視頻文件擴展名
  static const videoExtensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv'];
  
  /// PDF文件擴展名
  static const pdfExtensions = ['.pdf'];

  /// 檢查是否為音頻文件
  static bool isAudioFile(String filePath) {
    return isFileType(filePath, audioExtensions);
  }

  /// 檢查是否為視頻文件
  static bool isVideoFile(String filePath) {
    return isFileType(filePath, videoExtensions);
  }

  /// 檢查是否為PDF文件
  static bool isPdfFile(String filePath) {
    return isFileType(filePath, pdfExtensions);
  }
}
