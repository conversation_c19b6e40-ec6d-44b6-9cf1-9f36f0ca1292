import 'dart:io';
import 'package:get/get.dart';
import '../services/http_server_service.dart';
import 'logger.dart';

/// 服務器診斷工具
class ServerDiagnostics {
  /// 運行完整的服務器診斷
  static Future<Map<String, dynamic>> runDiagnostics() async {
    final results = <String, dynamic>{};
    
    Logger.info('Starting server diagnostics...');
    
    // 檢查平台信息
    results['platform'] = _getPlatformInfo();
    
    // 檢查網絡接口
    results['networkInterfaces'] = await _checkNetworkInterfaces();
    
    // 檢查端口可用性
    results['portAvailability'] = await _checkPortAvailability();
    
    // 檢查服務器狀態
    results['serverStatus'] = _checkServerStatus();
    
    Logger.info('Server diagnostics completed');
    return results;
  }
  
  /// 獲取平台信息
  static Map<String, dynamic> _getPlatformInfo() {
    return {
      'isIOS': Platform.isIOS,
      'isAndroid': Platform.isAndroid,
      'operatingSystem': Platform.operatingSystem,
      'operatingSystemVersion': Platform.operatingSystemVersion,
    };
  }
  
  /// 檢查網絡接口
  static Future<Map<String, dynamic>> _checkNetworkInterfaces() async {
    final result = <String, dynamic>{
      'available': false,
      'interfaces': <Map<String, dynamic>>[],
      'error': null,
    };
    
    try {
      final interfaces = await NetworkInterface.list();
      result['available'] = interfaces.isNotEmpty;
      
      for (final interface in interfaces) {
        final interfaceInfo = <String, dynamic>{
          'name': interface.name,
          'addresses': <Map<String, dynamic>>[],
        };
        
        for (final address in interface.addresses) {
          interfaceInfo['addresses'].add({
            'address': address.address,
            'isLoopback': address.isLoopback,
            'type': address.type.toString(),
            'isIPv4': address.type == InternetAddressType.IPv4,
          });
        }
        
        result['interfaces'].add(interfaceInfo);
      }
      
      Logger.info('Found ${interfaces.length} network interfaces');
    } catch (e) {
      result['error'] = e.toString();
      Logger.error('Error checking network interfaces', error: e);
    }
    
    return result;
  }
  
  /// 檢查端口可用性
  static Future<Map<String, dynamic>> _checkPortAvailability() async {
    final result = <String, dynamic>{
      'testedPorts': <Map<String, dynamic>>[],
      'availablePorts': <int>[],
      'error': null,
    };
    
    final portsToTest = [8080, 8081, 8082, 3000, 4000, 5000];
    
    try {
      for (final port in portsToTest) {
        final portResult = <String, dynamic>{
          'port': port,
          'available': false,
          'error': null,
        };
        
        try {
          final socket = await ServerSocket.bind(InternetAddress.anyIPv4, port);
          await socket.close();
          portResult['available'] = true;
          result['availablePorts'].add(port);
        } catch (e) {
          portResult['error'] = e.toString();
        }
        
        result['testedPorts'].add(portResult);
      }
    } catch (e) {
      result['error'] = e.toString();
      Logger.error('Error checking port availability', error: e);
    }
    
    return result;
  }
  
  /// 檢查服務器狀態
  static Map<String, dynamic> _checkServerStatus() {
    final result = <String, dynamic>{
      'isServiceRegistered': false,
      'isRunning': false,
      'serverUrl': null,
      'serverPort': null,
    };
    
    try {
      if (Get.isRegistered<HttpServerService>()) {
        result['isServiceRegistered'] = true;
        final serverService = Get.find<HttpServerService>();
        result['isRunning'] = serverService.isRunning;
        result['serverUrl'] = serverService.serverUrl;
        result['serverPort'] = serverService.serverPort;
      }
    } catch (e) {
      result['error'] = e.toString();
    }
    
    return result;
  }
  
  /// 生成診斷報告
  static String generateReport(Map<String, dynamic> diagnostics) {
    final buffer = StringBuffer();
    
    buffer.writeln('=== 服務器診斷報告 ===\n');
    
    // 平台信息
    final platform = diagnostics['platform'] as Map<String, dynamic>;
    buffer.writeln('平台信息:');
    buffer.writeln('  操作系統: ${platform['operatingSystem']}');
    buffer.writeln('  版本: ${platform['operatingSystemVersion']}');
    buffer.writeln('  iOS: ${platform['isIOS']}');
    buffer.writeln('  Android: ${platform['isAndroid']}');
    buffer.writeln();
    
    // 服務器狀態
    final serverStatus = diagnostics['serverStatus'] as Map<String, dynamic>;
    buffer.writeln('服務器狀態:');
    buffer.writeln('  服務已註冊: ${serverStatus['isServiceRegistered']}');
    buffer.writeln('  正在運行: ${serverStatus['isRunning']}');
    buffer.writeln('  服務器URL: ${serverStatus['serverUrl'] ?? '未設置'}');
    buffer.writeln('  端口: ${serverStatus['serverPort'] ?? '未設置'}');
    buffer.writeln();
    
    // 網絡接口
    final networkInterfaces = diagnostics['networkInterfaces'] as Map<String, dynamic>;
    buffer.writeln('網絡接口:');
    if (networkInterfaces['available'] == true) {
      final interfaces = networkInterfaces['interfaces'] as List;
      buffer.writeln('  找到 ${interfaces.length} 個網絡接口');
      
      for (final interface in interfaces) {
        final interfaceMap = interface as Map<String, dynamic>;
        buffer.writeln('  - ${interfaceMap['name']}:');
        
        final addresses = interfaceMap['addresses'] as List;
        for (final address in addresses) {
          final addressMap = address as Map<String, dynamic>;
          if (addressMap['isIPv4'] == true && addressMap['isLoopback'] == false) {
            buffer.writeln('    IPv4: ${addressMap['address']}');
          }
        }
      }
    } else {
      buffer.writeln('  錯誤: ${networkInterfaces['error']}');
    }
    buffer.writeln();
    
    // 端口可用性
    final portAvailability = diagnostics['portAvailability'] as Map<String, dynamic>;
    buffer.writeln('端口可用性:');
    final availablePorts = portAvailability['availablePorts'] as List;
    if (availablePorts.isNotEmpty) {
      buffer.writeln('  可用端口: ${availablePorts.join(', ')}');
    } else {
      buffer.writeln('  沒有找到可用端口');
    }
    
    return buffer.toString();
  }
}
