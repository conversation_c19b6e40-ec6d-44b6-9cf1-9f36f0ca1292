import 'dart:async';
import 'dart:collection';
import 'logger.dart';

/// 性能指標
class PerformanceMetric {
  final String name;
  final DateTime startTime;
  final DateTime endTime;
  final Duration duration;
  final Map<String, dynamic> metadata;

  const PerformanceMetric({
    required this.name,
    required this.startTime,
    required this.endTime,
    required this.duration,
    this.metadata = const {},
  });

  @override
  String toString() {
    return 'PerformanceMetric(name: $name, duration: ${duration.inMilliseconds}ms)';
  }
}

/// 性能監控器
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final Map<String, DateTime> _startTimes = {};
  final Queue<PerformanceMetric> _metrics = Queue();
  static const int _maxMetrics = 100;
  static bool _isEnabled = true;

  /// 啟用/禁用性能監控
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// 開始計時
  void startTimer(String name) {
    if (!_isEnabled) return;
    _startTimes[name] = DateTime.now();
  }

  /// 結束計時
  void endTimer(String name, {Map<String, dynamic>? metadata}) {
    if (!_isEnabled) return;

    final startTime = _startTimes.remove(name);
    if (startTime == null) {
      Logger.warning('Timer "$name" was not started');
      return;
    }

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    final metric = PerformanceMetric(
      name: name,
      startTime: startTime,
      endTime: endTime,
      duration: duration,
      metadata: metadata ?? {},
    );

    _addMetric(metric);

    // 記錄慢操作
    if (duration.inMilliseconds > 1000) {
      Logger.warning('Slow operation detected: $metric');
    } else {
      Logger.debug('Performance: $metric');
    }
  }

  /// 測量操作執行時間
  T measure<T>(
    String name,
    T Function() operation, {
    Map<String, dynamic>? metadata,
  }) {
    if (!_isEnabled) return operation();

    startTimer(name);
    try {
      return operation();
    } finally {
      endTimer(name, metadata: metadata);
    }
  }

  /// 測量異步操作執行時間
  Future<T> measureAsync<T>(
    String name,
    Future<T> Function() operation, {
    Map<String, dynamic>? metadata,
  }) async {
    if (!_isEnabled) return await operation();

    startTimer(name);
    try {
      return await operation();
    } finally {
      endTimer(name, metadata: metadata);
    }
  }

  /// 添加指標
  void _addMetric(PerformanceMetric metric) {
    _metrics.add(metric);

    // 保持指標數量在限制內
    while (_metrics.length > _maxMetrics) {
      _metrics.removeFirst();
    }
  }

  /// 獲取所有指標
  List<PerformanceMetric> getMetrics() {
    return _metrics.toList();
  }

  /// 獲取指定名稱的指標
  List<PerformanceMetric> getMetricsByName(String name) {
    return _metrics.where((metric) => metric.name == name).toList();
  }

  /// 獲取平均執行時間
  Duration? getAverageTime(String name) {
    final metrics = getMetricsByName(name);
    if (metrics.isEmpty) return null;

    final totalMs = metrics.fold<int>(
      0,
      (sum, metric) => sum + metric.duration.inMilliseconds,
    );
    return Duration(milliseconds: totalMs ~/ metrics.length);
  }

  /// 獲取最大執行時間
  Duration? getMaxTime(String name) {
    final metrics = getMetricsByName(name);
    if (metrics.isEmpty) return null;

    return metrics.map((m) => m.duration).reduce((a, b) => a > b ? a : b);
  }

  /// 獲取最小執行時間
  Duration? getMinTime(String name) {
    final metrics = getMetricsByName(name);
    if (metrics.isEmpty) return null;

    return metrics.map((m) => m.duration).reduce((a, b) => a < b ? a : b);
  }

  /// 清除所有指標
  void clearMetrics() {
    _metrics.clear();
    _startTimes.clear();
  }

  /// 生成性能報告
  String generateReport() {
    if (_metrics.isEmpty) {
      return 'No performance metrics available';
    }

    final buffer = StringBuffer();
    buffer.writeln('Performance Report');
    buffer.writeln('==================');
    buffer.writeln('Total metrics: ${_metrics.length}');
    buffer.writeln();

    // 按名稱分組統計
    final groupedMetrics = <String, List<PerformanceMetric>>{};
    for (final metric in _metrics) {
      groupedMetrics.putIfAbsent(metric.name, () => []).add(metric);
    }

    for (final entry in groupedMetrics.entries) {
      final name = entry.key;
      final metrics = entry.value;

      final avgTime = getAverageTime(name);
      final maxTime = getMaxTime(name);
      final minTime = getMinTime(name);

      buffer.writeln('Operation: $name');
      buffer.writeln('  Count: ${metrics.length}');
      buffer.writeln('  Average: ${avgTime?.inMilliseconds}ms');
      buffer.writeln('  Max: ${maxTime?.inMilliseconds}ms');
      buffer.writeln('  Min: ${minTime?.inMilliseconds}ms');
      buffer.writeln();
    }

    return buffer.toString();
  }
}

/// 性能監控擴展
extension PerformanceExtension on Object {
  /// 測量方法執行時間
  T measurePerformance<T>(String name, T Function() operation) {
    return PerformanceMonitor().measure('$runtimeType.$name', operation);
  }

  /// 測量異步方法執行時間
  Future<T> measurePerformanceAsync<T>(
    String name,
    Future<T> Function() operation,
  ) {
    return PerformanceMonitor().measureAsync('$runtimeType.$name', operation);
  }
}

/// 性能監控裝飾器
class PerformanceDecorator {
  /// 裝飾函數以添加性能監控
  static T Function() decorate<T>(String name, T Function() function) {
    return () => PerformanceMonitor().measure(name, function);
  }

  /// 裝飾異步函數以添加性能監控
  static Future<T> Function() decorateAsync<T>(
    String name,
    Future<T> Function() function,
  ) {
    return () => PerformanceMonitor().measureAsync(name, function);
  }
}
