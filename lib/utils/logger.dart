import 'dart:developer' as developer;

/// 日誌級別
enum LogLevel {
  debug,
  info,
  warning,
  error,
}

/// 日誌服務
/// 提供統一的日誌記錄功能，在生產環境中可以控制日誌輸出
class Logger {
  static const String _tag = 'ManLeBox';
  static bool _isDebugMode = true;

  /// 設置調試模式
  static void setDebugMode(bool isDebug) {
    _isDebugMode = isDebug;
  }

  /// 調試日誌
  static void debug(String message, {String? tag}) {
    _log(LogLevel.debug, message, tag: tag);
  }

  /// 信息日誌
  static void info(String message, {String? tag}) {
    _log(LogLevel.info, message, tag: tag);
  }

  /// 警告日誌
  static void warning(String message, {String? tag}) {
    _log(LogLevel.warning, message, tag: tag);
  }

  /// 錯誤日誌
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// 內部日誌記錄方法
  static void _log(
    LogLevel level,
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (!_isDebugMode && level == LogLevel.debug) {
      return;
    }

    final logTag = tag ?? _tag;
    final timestamp = DateTime.now().toIso8601String();
    final levelStr = level.name.toUpperCase();
    
    final logMessage = '[$timestamp] [$levelStr] [$logTag] $message';
    
    if (error != null) {
      developer.log(
        logMessage,
        name: logTag,
        error: error,
        stackTrace: stackTrace,
        level: _getLevelValue(level),
      );
    } else {
      developer.log(
        logMessage,
        name: logTag,
        level: _getLevelValue(level),
      );
    }
  }

  /// 獲取日誌級別數值
  static int _getLevelValue(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500;
      case LogLevel.info:
        return 800;
      case LogLevel.warning:
        return 900;
      case LogLevel.error:
        return 1000;
    }
  }
}

/// 日誌擴展方法
extension LoggerExtension on Object {
  /// 調試日誌
  void logDebug(String message) {
    Logger.debug(message, tag: runtimeType.toString());
  }

  /// 信息日誌
  void logInfo(String message) {
    Logger.info(message, tag: runtimeType.toString());
  }

  /// 警告日誌
  void logWarning(String message) {
    Logger.warning(message, tag: runtimeType.toString());
  }

  /// 錯誤日誌
  void logError(String message, {Object? error, StackTrace? stackTrace}) {
    Logger.error(message, tag: runtimeType.toString(), error: error, stackTrace: stackTrace);
  }
}
