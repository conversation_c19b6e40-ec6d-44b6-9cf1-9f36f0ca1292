import 'package:get/get.dart';
import 'logger.dart';

/// 應用配置管理
/// 統一管理應用的各種配置選項
class AppConfig extends GetxService {
  static AppConfig get instance => Get.find<AppConfig>();

  // 調試配置
  final _isDebugMode = true.obs;
  final _enablePerformanceMonitoring = true.obs;
  final _enableDetailedLogging = true.obs;

  // 播放器配置
  final _autoPlayNext = true.obs;
  final _shuffleMode = false.obs;
  final _repeatMode = RepeatMode.none.obs;
  final _crossfadeDuration = 3.obs; // 秒

  // 服務器配置
  final _serverPort = 8080.obs;
  final _enableServerAutoStart = false.obs;
  final _maxUploadSize = 100.obs; // MB

  // 文件管理配置
  final _autoScanOnStartup = true.obs;
  final _maxRecentFiles = 50.obs;
  final _enableFileWatcher = true.obs;

  // UI配置
  final _themeMode = AppThemeMode.system.obs;
  final _enableAnimations = true.obs;
  final _showFileExtensions = false.obs;

  // 性能配置
  final _maxCacheSize = 500.obs; // MB
  final _enableImageCache = true.obs;
  final _preloadNextTrack = true.obs;

  /// 初始化配置
  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadConfig();
    _setupConfigWatchers();
    Logger.info('App config initialized');
  }

  // Getters
  bool get isDebugMode => _isDebugMode.value;
  bool get enablePerformanceMonitoring => _enablePerformanceMonitoring.value;
  bool get enableDetailedLogging => _enableDetailedLogging.value;
  
  bool get autoPlayNext => _autoPlayNext.value;
  bool get shuffleMode => _shuffleMode.value;
  RepeatMode get repeatMode => _repeatMode.value;
  int get crossfadeDuration => _crossfadeDuration.value;
  
  int get serverPort => _serverPort.value;
  bool get enableServerAutoStart => _enableServerAutoStart.value;
  int get maxUploadSize => _maxUploadSize.value;
  
  bool get autoScanOnStartup => _autoScanOnStartup.value;
  int get maxRecentFiles => _maxRecentFiles.value;
  bool get enableFileWatcher => _enableFileWatcher.value;
  
  AppThemeMode get themeMode => _themeMode.value;
  bool get enableAnimations => _enableAnimations.value;
  bool get showFileExtensions => _showFileExtensions.value;
  
  int get maxCacheSize => _maxCacheSize.value;
  bool get enableImageCache => _enableImageCache.value;
  bool get preloadNextTrack => _preloadNextTrack.value;

  // Setters
  void setDebugMode(bool value) {
    _isDebugMode.value = value;
    Logger.setDebugMode(value);
    _saveConfig();
  }

  void setPerformanceMonitoring(bool value) {
    _enablePerformanceMonitoring.value = value;
    _saveConfig();
  }

  void setDetailedLogging(bool value) {
    _enableDetailedLogging.value = value;
    _saveConfig();
  }

  void setAutoPlayNext(bool value) {
    _autoPlayNext.value = value;
    _saveConfig();
  }

  void setShuffleMode(bool value) {
    _shuffleMode.value = value;
    _saveConfig();
  }

  void setRepeatMode(RepeatMode mode) {
    _repeatMode.value = mode;
    _saveConfig();
  }

  void setCrossfadeDuration(int seconds) {
    if (seconds >= 0 && seconds <= 10) {
      _crossfadeDuration.value = seconds;
      _saveConfig();
    }
  }

  void setServerPort(int port) {
    if (port >= 1024 && port <= 65535) {
      _serverPort.value = port;
      _saveConfig();
    }
  }

  void setServerAutoStart(bool value) {
    _enableServerAutoStart.value = value;
    _saveConfig();
  }

  void setMaxUploadSize(int sizeMB) {
    if (sizeMB > 0 && sizeMB <= 1000) {
      _maxUploadSize.value = sizeMB;
      _saveConfig();
    }
  }

  void setAutoScanOnStartup(bool value) {
    _autoScanOnStartup.value = value;
    _saveConfig();
  }

  void setMaxRecentFiles(int count) {
    if (count > 0 && count <= 100) {
      _maxRecentFiles.value = count;
      _saveConfig();
    }
  }

  void setFileWatcher(bool value) {
    _enableFileWatcher.value = value;
    _saveConfig();
  }

  void setThemeMode(AppThemeMode mode) {
    _themeMode.value = mode;
    _saveConfig();
  }

  void setAnimations(bool value) {
    _enableAnimations.value = value;
    _saveConfig();
  }

  void setShowFileExtensions(bool value) {
    _showFileExtensions.value = value;
    _saveConfig();
  }

  void setMaxCacheSize(int sizeMB) {
    if (sizeMB > 0 && sizeMB <= 2000) {
      _maxCacheSize.value = sizeMB;
      _saveConfig();
    }
  }

  void setImageCache(bool value) {
    _enableImageCache.value = value;
    _saveConfig();
  }

  void setPreloadNextTrack(bool value) {
    _preloadNextTrack.value = value;
    _saveConfig();
  }

  /// 重置為默認配置
  void resetToDefaults() {
    _isDebugMode.value = true;
    _enablePerformanceMonitoring.value = true;
    _enableDetailedLogging.value = true;
    
    _autoPlayNext.value = true;
    _shuffleMode.value = false;
    _repeatMode.value = RepeatMode.none;
    _crossfadeDuration.value = 3;
    
    _serverPort.value = 8080;
    _enableServerAutoStart.value = false;
    _maxUploadSize.value = 100;
    
    _autoScanOnStartup.value = true;
    _maxRecentFiles.value = 50;
    _enableFileWatcher.value = true;
    
    _themeMode.value = AppThemeMode.system;
    _enableAnimations.value = true;
    _showFileExtensions.value = false;
    
    _maxCacheSize.value = 500;
    _enableImageCache.value = true;
    _preloadNextTrack.value = true;
    
    _saveConfig();
    Logger.info('Config reset to defaults');
  }

  /// 載入配置
  Future<void> _loadConfig() async {
    try {
      // 這裡可以從SharedPreferences或其他存儲載入配置
      // 暫時使用默認值
      Logger.debug('Config loaded from storage');
    } catch (e) {
      Logger.error('Error loading config', error: e);
    }
  }

  /// 保存配置
  void _saveConfig() {
    try {
      // 這裡可以保存到SharedPreferences或其他存儲
      Logger.debug('Config saved to storage');
    } catch (e) {
      Logger.error('Error saving config', error: e);
    }
  }

  /// 設置配置監聽器
  void _setupConfigWatchers() {
    // 監聽調試模式變化
    ever(_isDebugMode, (bool value) {
      Logger.setDebugMode(value);
    });
  }

  /// 獲取配置摘要
  Map<String, dynamic> getConfigSummary() {
    return {
      'debug': {
        'isDebugMode': isDebugMode,
        'enablePerformanceMonitoring': enablePerformanceMonitoring,
        'enableDetailedLogging': enableDetailedLogging,
      },
      'player': {
        'autoPlayNext': autoPlayNext,
        'shuffleMode': shuffleMode,
        'repeatMode': repeatMode.name,
        'crossfadeDuration': crossfadeDuration,
      },
      'server': {
        'serverPort': serverPort,
        'enableServerAutoStart': enableServerAutoStart,
        'maxUploadSize': maxUploadSize,
      },
      'fileManager': {
        'autoScanOnStartup': autoScanOnStartup,
        'maxRecentFiles': maxRecentFiles,
        'enableFileWatcher': enableFileWatcher,
      },
      'ui': {
        'themeMode': themeMode.name,
        'enableAnimations': enableAnimations,
        'showFileExtensions': showFileExtensions,
      },
      'performance': {
        'maxCacheSize': maxCacheSize,
        'enableImageCache': enableImageCache,
        'preloadNextTrack': preloadNextTrack,
      },
    };
  }
}

/// 重複模式
enum RepeatMode {
  none,
  one,
  all,
}

/// 主題模式
enum AppThemeMode {
  light,
  dark,
  system,
}
