import 'dart:io';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart' as just_audio;
import '../models/audio_file.dart';
import '../utils/logger.dart';
import '../models/lyric.dart';
import 'audio_cache_service.dart';
import 'storage_service.dart';

/// 音頻元數據解析服務
/// 負責解析音頻文件的元數據信息，包括標題、藝術家、專輯、封面等
class AudioMetadataService extends GetxService {
  late final AudioCacheService _cacheService;

  @override
  Future<void> onInit() async {
    super.onInit();
    _cacheService = AudioCacheService(Get.find<StorageService>());
    await _cacheService.loadCache();
    Logger.info('Audio metadata service initialized');
  }

  /// 解析音頻文件元數據
  Future<AudioFile> parseAudioMetadata(File file) async {
    try {
      // 檢查緩存
      final cachedFile = _cacheService.getCachedAudioFile(file.path);
      if (cachedFile != null && !_cacheService.needsUpdate(file.path)) {
        return cachedFile;
      }

      // 獲取基本文件信息
      final fileName = file.path.split('/').last;
      final fileSize = file.lengthSync();
      final createdAt = file.lastModifiedSync();

      // 從文件名提取基本信息
      String title = _extractTitleFromFileName(fileName);
      String artist = '未知藝術家';
      String album = '未知專輯';

      // 獲取音樂時長
      int? duration = await _getAudioDuration(file.path);

      final audioFile = AudioFile(
        path: file.path,
        fileName: fileName,
        title: title,
        artist: artist,
        album: album,
        duration: duration,
        fileSize: fileSize,
        albumArtPath: null, // 暫時無法獲取封面
        createdAt: createdAt,
      );

      // 緩存結果
      _cacheService.cacheAudioFile(audioFile);

      return audioFile;
    } catch (e) {
      Logger.error('Error parsing audio metadata', error: e);
      // 如果解析失敗，返回基本信息
      return AudioFile.fromFile(file);
    }
  }

  /// 批量解析音頻文件元數據
  Future<List<AudioFile>> parseAudioMetadataBatch(List<File> files) async {
    final audioFiles = <AudioFile>[];

    for (final file in files) {
      try {
        final audioFile = await parseAudioMetadata(file);
        audioFiles.add(audioFile);
      } catch (e) {
        Logger.error('Error parsing file ${file.path}', error: e);
        // 添加基本信息的文件
        audioFiles.add(AudioFile.fromFile(file));
      }
    }

    return audioFiles;
  }

  /// 更新音頻文件元數據
  Future<bool> updateAudioMetadata(
    AudioFile audioFile, {
    String? title,
    String? artist,
    String? album,
    String? genre,
    int? year,
    int? trackNumber,
  }) async {
    try {
      // TODO: 實現元數據寫入功能
      // 可以使用第三方庫或自定義實現來寫入ID3標籤
      Logger.debug('Updating metadata for: ${audioFile.title}');
      return true; // 暫時返回成功
    } catch (e) {
      Logger.error('Error updating audio metadata', error: e);
      return false;
    }
  }

  /// 讀取歌詞文件
  Future<Lyric?> readLyricFile(String audioFilePath) async {
    try {
      // 嘗試多種歌詞文件格式
      final lyricPaths = _getLyricFilePaths(audioFilePath);

      for (final lyricPath in lyricPaths) {
        final lyricFile = File(lyricPath);
        if (lyricFile.existsSync()) {
          final content = await lyricFile.readAsString();
          return Lyric.fromLrc(content);
        }
      }

      return null;
    } catch (e) {
      Logger.error('Error reading lyric file', error: e);
      return null;
    }
  }

  /// 保存歌詞文件
  Future<bool> saveLyricFile(String audioFilePath, Lyric lyric) async {
    try {
      final lyricPath = _getLyricFilePath(audioFilePath);
      final lyricFile = File(lyricPath);

      // 確保目錄存在
      await lyricFile.parent.create(recursive: true);

      // 寫入歌詞內容
      await lyricFile.writeAsString(lyric.toLrc());
      return true;
    } catch (e) {
      Logger.error('Error saving lyric file', error: e);
      return false;
    }
  }

  /// 搜索在線歌詞
  Future<Lyric?> searchOnlineLyrics(String title, String artist) async {
    try {
      // TODO: 實現在線歌詞搜索
      // 這裡可以集成歌詞API服務，如網易雲音樂、QQ音樂等
      Logger.debug('Searching online lyrics for: $title - $artist');
      return null;
    } catch (e) {
      Logger.error('Error searching online lyrics', error: e);
      return null;
    }
  }

  /// 獲取音頻文件的頻譜數據
  Future<List<double>?> getAudioSpectrum(String audioFilePath) async {
    try {
      // TODO: 實現音頻頻譜分析
      // 可以使用FFT算法分析音頻頻譜
      Logger.debug('Getting audio spectrum for: $audioFilePath');
      return null;
    } catch (e) {
      Logger.error('Error getting audio spectrum', error: e);
      return null;
    }
  }

  /// 檢測音頻文件的BPM（每分鐘節拍數）
  Future<double?> detectBPM(String audioFilePath) async {
    try {
      // TODO: 實現BPM檢測
      // 可以使用節拍檢測算法
      Logger.debug('Detecting BPM for: $audioFilePath');
      return null;
    } catch (e) {
      Logger.error('Error detecting BPM', error: e);
      return null;
    }
  }

  /// 從文件名提取標題
  String _extractTitleFromFileName(String fileName) {
    // 移除文件擴展名
    String title = fileName.contains('.')
        ? fileName.substring(0, fileName.lastIndexOf('.'))
        : fileName;

    // 移除常見的前綴和後綴
    title = title.replaceAll(RegExp(r'^\d+[\.\-\s]*'), ''); // 移除開頭的數字
    title = title.replaceAll(RegExp(r'\s*\[.*?\]\s*'), ''); // 移除方括號內容
    title = title.replaceAll(RegExp(r'\s*\(.*?\)\s*'), ''); // 移除圓括號內容
    title = title.trim();

    return title.isNotEmpty ? title : fileName;
  }

  /// 獲取歌詞文件路徑列表
  List<String> _getLyricFilePaths(String audioFilePath) {
    final audioFile = File(audioFilePath);
    final baseName = audioFile.path.substring(
      0,
      audioFile.path.lastIndexOf('.'),
    );

    return [
      '$baseName.lrc',
      '$baseName.txt',
      '${audioFile.parent.path}/lyrics/${audioFile.path.split('/').last.split('.').first}.lrc',
    ];
  }

  /// 獲取歌詞文件保存路徑
  String _getLyricFilePath(String audioFilePath) {
    final audioFile = File(audioFilePath);
    final baseName = audioFile.path.substring(
      0,
      audioFile.path.lastIndexOf('.'),
    );
    return '$baseName.lrc';
  }

  /// 檢查音頻文件格式是否支持
  bool isSupportedAudioFormat(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    const supportedFormats = ['mp3', 'flac', 'wav', 'aac', 'm4a', 'ogg', 'wma'];
    return supportedFormats.contains(extension);
  }

  /// 獲取音頻文件格式信息
  Map<String, dynamic> getAudioFormatInfo(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;

    switch (extension) {
      case 'mp3':
        return {
          'format': 'MP3',
          'description': 'MPEG Audio Layer 3',
          'lossy': true,
          'quality': 'Good',
        };
      case 'flac':
        return {
          'format': 'FLAC',
          'description': 'Free Lossless Audio Codec',
          'lossy': false,
          'quality': 'Excellent',
        };
      case 'wav':
        return {
          'format': 'WAV',
          'description': 'Waveform Audio File Format',
          'lossy': false,
          'quality': 'Excellent',
        };
      case 'aac':
      case 'm4a':
        return {
          'format': 'AAC',
          'description': 'Advanced Audio Coding',
          'lossy': true,
          'quality': 'Very Good',
        };
      case 'ogg':
        return {
          'format': 'OGG',
          'description': 'Ogg Vorbis',
          'lossy': true,
          'quality': 'Good',
        };
      default:
        return {
          'format': extension.toUpperCase(),
          'description': 'Unknown Format',
          'lossy': null,
          'quality': 'Unknown',
        };
    }
  }

  /// 獲取音頻文件時長（毫秒）
  Future<int?> _getAudioDuration(String filePath) async {
    just_audio.AudioPlayer? player;
    try {
      player = just_audio.AudioPlayer();
      await player.setFilePath(filePath);

      // 等待播放器準備完成，最多等待3秒
      int attempts = 0;
      while (player.duration == null && attempts < 30) {
        await Future.delayed(const Duration(milliseconds: 100));
        attempts++;
      }

      final duration = player.duration;
      return duration?.inMilliseconds;
    } catch (e) {
      Logger.error('Failed to get audio duration: $e');
      return null;
    } finally {
      await player?.dispose();
    }
  }

  /// 保存緩存
  Future<void> saveCache() async {
    await _cacheService.saveCache();
  }

  /// 清理緩存
  Future<void> cleanupCache() async {
    await _cacheService.cleanupCache();
  }
}
