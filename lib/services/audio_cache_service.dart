import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import '../models/audio_file.dart';
import '../utils/logger.dart';
import 'storage_service.dart';

/// 音頻元數據緩存服務
/// 緩存音頻文件的元數據，避免重複解析
class AudioCacheService {
  static const String _cacheFileName = 'audio_metadata_cache.json';
  final StorageService _storageService;
  
  // 內存緩存
  final Map<String, AudioFile> _memoryCache = {};

  AudioCacheService(this._storageService);

  /// 獲取緩存文件路徑
  String get _cacheFilePath {
    return path.join(_storageService.appDocumentsDir.path, _cacheFileName);
  }

  /// 載入緩存
  Future<void> loadCache() async {
    try {
      final file = File(_cacheFilePath);
      if (!file.existsSync()) return;

      final jsonString = await file.readAsString();
      final jsonMap = json.decode(jsonString) as Map<String, dynamic>;

      _memoryCache.clear();
      for (final entry in jsonMap.entries) {
        try {
          final audioFile = AudioFile.fromJson(entry.value as Map<String, dynamic>);
          _memoryCache[entry.key] = audioFile;
        } catch (e) {
          Logger.error('Failed to parse cached audio file: ${entry.key}, error: $e');
        }
      }

      Logger.info('Loaded ${_memoryCache.length} audio files from cache');
    } catch (e) {
      Logger.error('Failed to load audio cache: $e');
    }
  }

  /// 保存緩存
  Future<void> saveCache() async {
    try {
      final file = File(_cacheFilePath);
      final jsonMap = <String, dynamic>{};

      for (final entry in _memoryCache.entries) {
        jsonMap[entry.key] = entry.value.toJson();
      }

      final jsonString = json.encode(jsonMap);
      await file.writeAsString(jsonString);

      Logger.info('Saved ${_memoryCache.length} audio files to cache');
    } catch (e) {
      Logger.error('Failed to save audio cache: $e');
    }
  }

  /// 獲取緩存的音頻文件
  AudioFile? getCachedAudioFile(String filePath) {
    return _memoryCache[filePath];
  }

  /// 緩存音頻文件
  void cacheAudioFile(AudioFile audioFile) {
    _memoryCache[audioFile.path] = audioFile;
  }

  /// 檢查文件是否需要更新緩存
  bool needsUpdate(String filePath) {
    final cachedFile = _memoryCache[filePath];
    if (cachedFile == null) return true;

    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        // 文件不存在，移除緩存
        _memoryCache.remove(filePath);
        return false;
      }

      final lastModified = file.lastModifiedSync();
      // 如果文件修改時間比緩存的創建時間新，需要更新
      return lastModified.isAfter(cachedFile.createdAt);
    } catch (e) {
      Logger.error('Failed to check file modification time: $e');
      return true;
    }
  }

  /// 移除緩存項目
  void removeCachedFile(String filePath) {
    _memoryCache.remove(filePath);
  }

  /// 清空緩存
  void clearCache() {
    _memoryCache.clear();
  }

  /// 獲取緩存統計信息
  Map<String, dynamic> getCacheStats() {
    int totalFiles = _memoryCache.length;
    int filesWithDuration = _memoryCache.values
        .where((file) => file.duration != null)
        .length;

    return {
      'totalFiles': totalFiles,
      'filesWithDuration': filesWithDuration,
      'cacheHitRate': totalFiles > 0 ? filesWithDuration / totalFiles : 0.0,
    };
  }

  /// 批量更新緩存
  Future<void> batchUpdateCache(List<AudioFile> audioFiles) async {
    for (final audioFile in audioFiles) {
      cacheAudioFile(audioFile);
    }
    await saveCache();
  }

  /// 清理無效的緩存項目
  Future<void> cleanupCache() async {
    final keysToRemove = <String>[];

    for (final filePath in _memoryCache.keys) {
      final file = File(filePath);
      if (!file.existsSync()) {
        keysToRemove.add(filePath);
      }
    }

    for (final key in keysToRemove) {
      _memoryCache.remove(key);
    }

    if (keysToRemove.isNotEmpty) {
      await saveCache();
      Logger.info('Cleaned up ${keysToRemove.length} invalid cache entries');
    }
  }
}
