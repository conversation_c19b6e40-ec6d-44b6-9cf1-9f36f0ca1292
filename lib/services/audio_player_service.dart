import 'dart:async';
import 'dart:io';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart' as just_audio;
import 'package:just_audio_background/just_audio_background.dart';
import 'package:audio_session/audio_session.dart';
import '../models/audio_file.dart';
import '../models/player_state.dart' as app_state;
import '../models/lyric.dart';

import 'notification_service.dart';
import '../utils/logger.dart';
import 'audio_metadata_service.dart';
import 'playlist_service.dart';

/// 音頻播放服務
/// 負責音頻播放的核心功能，包括播放控制、後台播放、通知管理等
class AudioPlayerService extends GetxService {
  late just_audio.AudioPlayer _audioPlayer;
  late AudioSession _audioSession;

  final NotificationService _notificationService =
      Get.find<NotificationService>();

  // 響應式狀態
  final _playerState = app_state.AudioPlayerState().obs;
  final _currentPlaylist = <AudioFile>[].obs;
  final _currentIndex = (-1).obs;
  final _isShuffleEnabled = false.obs;
  final _currentLyric = Rx<Lyric?>(null);

  // 服務依賴
  AudioMetadataService? _metadataService;
  PlaylistService? _playlistService;

  // Getters
  app_state.AudioPlayerState get playerState => _playerState.value;
  List<AudioFile> get currentPlaylist => _currentPlaylist.toList();
  int get currentIndex => _currentIndex.value;
  AudioFile? get currentAudio =>
      currentIndex >= 0 && currentIndex < currentPlaylist.length
      ? currentPlaylist[currentIndex]
      : null;
  bool get isShuffleEnabled => _isShuffleEnabled.value;
  Lyric? get currentLyric => _currentLyric.value;

  // 流訂閱
  StreamSubscription<just_audio.PlayerState>? _playerStateSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeAudioPlayer();
    _initializeServices();
  }

  @override
  void onClose() {
    _dispose();
    super.onClose();
  }

  /// 初始化服務依賴
  void _initializeServices() {
    try {
      _metadataService = Get.find<AudioMetadataService>();
      _playlistService = Get.find<PlaylistService>();
    } catch (e) {
      // 服務可能還未初始化，稍後再試
      Logger.warning('Services not yet available: $e');
    }
  }

  /// 初始化音頻播放器
  Future<void> _initializeAudioPlayer() async {
    try {
      // 初始化音頻會話，配置為音樂播放模式，支持後台播放
      _audioSession = await AudioSession.instance;
      await _audioSession.configure(AudioSessionConfiguration.music());

      // 初始化音頻播放器，啟用背景播放和中斷處理
      _audioPlayer = just_audio.AudioPlayer(
        handleInterruptions: true,
        androidApplyAudioAttributes: true,
        handleAudioSessionActivation: true,
        audioPipeline: just_audio.AudioPipeline(androidAudioEffects: []),
      );

      // 監聽播放器狀態變化
      _playerStateSubscription = _audioPlayer.playerStateStream.listen((state) {
        _updatePlayerState(state);
      });

      // 監聽播放位置變化
      _positionSubscription = _audioPlayer.positionStream.listen((position) {
        _playerState.value = _playerState.value.copyWith(
          currentPosition: position.inMilliseconds,
        );
      });

      // 監聽時長變化
      _durationSubscription = _audioPlayer.durationStream.listen((duration) {
        if (duration != null) {
          _playerState.value = _playerState.value.copyWith(
            totalDuration: duration.inMilliseconds,
          );
        }
      });

      // 監聽播放完成事件
      _audioPlayer.playerStateStream.listen((state) {
        if (state.processingState == just_audio.ProcessingState.completed) {
          _onAudioCompleted();
        }
      });

      // 監聽當前播放索引變化
      _audioPlayer.currentIndexStream.listen((index) {
        if (index != null && index != _currentIndex.value) {
          _currentIndex.value = index;
          Logger.info('Current index changed to: $index');

          // 更新當前音頻路徑
          if (index < currentPlaylist.length) {
            final currentAudio = currentPlaylist[index];
            _playerState.value = _playerState.value.copyWith(
              currentAudioPath: currentAudio.path,
            );

            // 載入新歌曲的歌詞
            _loadLyric(currentAudio);

            // 更新通知
            _updateNotification();
          }
        }
      });

      Logger.info(
        'Audio player service initialized with background playback support',
      );
    } catch (e) {
      Logger.error('Error initializing audio player', error: e);
      _playerState.value = _playerState.value.copyWith(
        playerState: app_state.PlayerState.error,
        errorMessage: '初始化音頻播放器失敗: $e',
      );
    }
  }

  /// 更新播放器狀態
  void _updatePlayerState(just_audio.PlayerState state) {
    app_state.PlayerState newState;

    switch (state.processingState) {
      case just_audio.ProcessingState.idle:
        newState = app_state.PlayerState.stopped;
        break;
      case just_audio.ProcessingState.loading:
        newState = app_state.PlayerState.loading;
        break;
      case just_audio.ProcessingState.buffering:
        newState = app_state.PlayerState.buffering;
        break;
      case just_audio.ProcessingState.ready:
        newState = state.playing
            ? app_state.PlayerState.playing
            : app_state.PlayerState.paused;
        break;
      case just_audio.ProcessingState.completed:
        newState = app_state.PlayerState.stopped;
        break;
    }

    _playerState.value = _playerState.value.copyWith(playerState: newState);

    // 更新通知
    _updateNotification();
  }

  /// 播放音頻文件
  Future<bool> playAudio(AudioFile audioFile) async {
    try {
      _playerState.value = _playerState.value.copyWith(
        playerState: app_state.PlayerState.loading,
        currentAudioPath: audioFile.path,
        errorMessage: null,
      );

      // 檢查文件是否存在
      if (!File(audioFile.path).existsSync()) {
        throw Exception('音頻文件不存在: ${audioFile.path}');
      }

      // 設置音頻源
      await _audioPlayer.setAudioSource(
        just_audio.AudioSource.file(
          audioFile.path,
          tag: MediaItem(
            id: audioFile.path,
            title: audioFile.title,
            artist: audioFile.artist,
            album: audioFile.album,
            duration: audioFile.duration != null
                ? Duration(milliseconds: audioFile.duration!)
                : null,
            extras: {'playMode': _playerState.value.playMode.name},
          ),
        ),
      );

      // 載入歌詞
      await _loadLyric(audioFile);

      // 添加到最近播放
      _addToRecentPlaylist(audioFile);

      // 開始播放
      await _audioPlayer.play();

      return true;
    } catch (e) {
      Logger.error('Error playing audio', error: e);
      _playerState.value = _playerState.value.copyWith(
        playerState: app_state.PlayerState.error,
        errorMessage: '播放失敗: $e',
      );
      return false;
    }
  }

  /// 播放播放列表
  Future<bool> playPlaylist(
    List<AudioFile> playlist, {
    int startIndex = 0,
  }) async {
    try {
      if (playlist.isEmpty) {
        throw Exception('播放列表為空');
      }

      if (startIndex < 0 || startIndex >= playlist.length) {
        throw Exception('無效的起始索引');
      }

      _currentPlaylist.value = playlist;
      _currentIndex.value = startIndex;

      // 創建播放列表音頻源
      final audioSources = playlist.map((audioFile) {
        return just_audio.AudioSource.file(
          audioFile.path,
          tag: MediaItem(
            id: audioFile.path,
            title: audioFile.title,
            artist: audioFile.artist,
            album: audioFile.album,
            duration: audioFile.duration != null
                ? Duration(milliseconds: audioFile.duration!)
                : null,
            extras: {'playMode': _playerState.value.playMode.name},
          ),
        );
      }).toList();

      // 設置播放列表
      await _audioPlayer.setAudioSources(
        audioSources,
        initialIndex: startIndex,
      );

      // 開始播放
      await _audioPlayer.play();

      _playerState.value = _playerState.value.copyWith(
        playerState: app_state.PlayerState.playing,
        currentAudioPath: playlist[startIndex].path,
        errorMessage: null,
      );

      return true;
    } catch (e) {
      Logger.error('Error playing playlist', error: e);
      _playerState.value = _playerState.value.copyWith(
        playerState: app_state.PlayerState.error,
        errorMessage: '播放列表失敗: $e',
      );
      return false;
    }
  }

  /// 暫停播放
  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      Logger.error('Error pausing audio', error: e);
    }
  }

  /// 恢復播放
  Future<void> resume() async {
    try {
      await _audioPlayer.play();
    } catch (e) {
      Logger.error('Error resuming audio', error: e);
    }
  }

  /// 停止播放
  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _playerState.value = _playerState.value.copyWith(
        playerState: app_state.PlayerState.stopped,
        currentPosition: 0,
      );

      // 取消通知
      await _notificationService.cancelMusicNotification();
    } catch (e) {
      Logger.error('Error stopping audio', error: e);
    }
  }

  /// 播放/暫停切換
  Future<void> togglePlayPause() async {
    if (playerState.playerState.isPlaying) {
      await pause();
    } else if (playerState.playerState.isPaused) {
      await resume();
    }
  }

  /// 跳轉到指定位置
  Future<void> seekTo(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      Logger.error('Error seeking to position', error: e);
    }
  }

  /// 設置音量
  Future<void> setVolume(double volume) async {
    try {
      final clampedVolume = volume.clamp(0.0, 1.0);
      await _audioPlayer.setVolume(clampedVolume);
      _playerState.value = _playerState.value.copyWith(volume: clampedVolume);
    } catch (e) {
      Logger.error('Error setting volume', error: e);
    }
  }

  /// 設置播放速度
  Future<void> setSpeed(double speed) async {
    try {
      final clampedSpeed = speed.clamp(0.5, 2.0);
      await _audioPlayer.setSpeed(clampedSpeed);
      _playerState.value = _playerState.value.copyWith(speed: clampedSpeed);
    } catch (e) {
      Logger.error('Error setting speed', error: e);
    }
  }

  /// 切換播放模式
  void togglePlayMode() {
    final currentMode = _playerState.value.playMode;
    final nextMode = currentMode.next;
    _playerState.value = _playerState.value.copyWith(playMode: nextMode);

    // 根據播放模式設置循環模式
    switch (nextMode) {
      case app_state.PlayMode.sequence:
        _audioPlayer.setLoopMode(just_audio.LoopMode.off);
        _audioPlayer.setShuffleModeEnabled(false);
        break;
      case app_state.PlayMode.repeatOne:
        _audioPlayer.setLoopMode(just_audio.LoopMode.one);
        _audioPlayer.setShuffleModeEnabled(false);
        break;
      case app_state.PlayMode.repeatAll:
        _audioPlayer.setLoopMode(just_audio.LoopMode.all);
        _audioPlayer.setShuffleModeEnabled(false);
        break;
      case app_state.PlayMode.shuffle:
        _audioPlayer.setLoopMode(just_audio.LoopMode.all);
        _audioPlayer.setShuffleModeEnabled(true);
        break;
    }
  }

  /// 播放上一首
  Future<void> playPrevious() async {
    try {
      if (_audioPlayer.hasPrevious) {
        await _audioPlayer.seekToPrevious();
        _currentIndex.value = _audioPlayer.currentIndex ?? 0;
      } else if (currentPlaylist.isNotEmpty) {
        // 如果沒有上一首，循環到最後一首
        final lastIndex = currentPlaylist.length - 1;
        await _audioPlayer.seek(Duration.zero, index: lastIndex);
        _currentIndex.value = lastIndex;
      }
    } catch (e) {
      Logger.error('Error playing previous audio', error: e);
    }
  }

  /// 播放下一首
  Future<void> playNext() async {
    try {
      if (_audioPlayer.hasNext) {
        await _audioPlayer.seekToNext();
        _currentIndex.value = _audioPlayer.currentIndex ?? 0;
      } else if (currentPlaylist.isNotEmpty) {
        // 如果沒有下一首，循環到第一首
        await _audioPlayer.seek(Duration.zero, index: 0);
        _currentIndex.value = 0;
      }
    } catch (e) {
      Logger.error('Error playing next audio', error: e);
    }
  }

  /// 音頻播放完成處理
  void _onAudioCompleted() {
    // 播放列表的自動播放由just_audio的LoopMode處理
    // 這裡只需要更新當前索引
    _currentIndex.value = _audioPlayer.currentIndex ?? 0;
  }

  /// 更新通知
  Future<void> _updateNotification() async {
    if (currentAudio != null && playerState.playerState.isPlaying) {
      await _notificationService.showMusicNotification(
        title: currentAudio!.title,
        artist: currentAudio!.artist,
        album: currentAudio!.album,
        isPlaying: playerState.playerState.isPlaying,
      );
    }
  }

  /// 載入歌詞
  Future<void> _loadLyric(AudioFile audioFile) async {
    try {
      if (_metadataService != null) {
        final lyric = await _metadataService!.readLyricFile(audioFile.path);
        _currentLyric.value = lyric;
      }
    } catch (e) {
      Logger.error('Error loading lyric', error: e);
      _currentLyric.value = null;
    }
  }

  /// 添加到最近播放列表
  void _addToRecentPlaylist(AudioFile audioFile) {
    try {
      if (_playlistService != null) {
        _playlistService!.addToRecent(audioFile);
      }
    } catch (e) {
      Logger.error('Error adding to recent playlist', error: e);
    }
  }

  /// 獲取當前歌詞行
  LyricLine? getCurrentLyricLine() {
    if (_currentLyric.value == null) return null;
    return _currentLyric.value!.getCurrentLine(playerState.currentPosition);
  }

  /// 跳轉到歌詞時間點
  Future<void> seekToLyricTime(int timestamp) async {
    try {
      await _audioPlayer.seek(Duration(milliseconds: timestamp));
    } catch (e) {
      Logger.error('Error seeking to lyric time', error: e);
    }
  }

  /// 清理資源
  void _dispose() {
    _playerStateSubscription?.cancel();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _audioPlayer.dispose();
  }
}
