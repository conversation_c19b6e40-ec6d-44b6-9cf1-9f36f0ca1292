import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import '../models/video_folder.dart';
import '../models/video_file.dart';
import '../utils/logger.dart';
import '../utils/file_utils.dart';
import '../utils/error_utils.dart';
import 'storage_service.dart';

/// 視頻文件夾管理服務
/// 負責文件夾的創建、刪除、重命名等操作
class VideoFolderService {
  static const String _foldersFileName = 'video_folders.json';
  final StorageService _storageService;
  final Uuid _uuid = const Uuid();

  VideoFolderService(this._storageService);

  /// 獲取文件夾配置文件路徑
  String get _foldersFilePath {
    return path.join(_storageService.appDocumentsDir.path, _foldersFileName);
  }

  /// 載入所有文件夾
  Future<List<VideoFolder>> loadFolders() async {
    try {
      final filePath = _foldersFilePath;
      final file = File(filePath);

      if (!file.existsSync()) {
        // 創建默認文件夾
        final defaultFolders = await _createDefaultFolders();
        await _saveFolders(defaultFolders);
        return defaultFolders;
      }

      final jsonString = await file.readAsString();
      final jsonList = json.decode(jsonString) as List;

      return jsonList
          .map((json) => VideoFolder.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      Logger.error('Failed to load folders: $e');
      return await _createDefaultFolders();
    }
  }

  /// 保存文件夾列表
  Future<void> _saveFolders(List<VideoFolder> folders) async {
    try {
      final filePath = _foldersFilePath;
      final file = File(filePath);

      final jsonList = folders.map((folder) => folder.toJson()).toList();
      final jsonString = json.encode(jsonList);

      await file.writeAsString(jsonString);
    } catch (e) {
      Logger.error('Failed to save folders: $e');
      throw Exception('無法保存文件夾配置');
    }
  }

  /// 創建默認文件夾
  Future<List<VideoFolder>> _createDefaultFolders() async {
    final videoDir = _storageService.videoDir;
    final folders = <VideoFolder>[];

    // 根文件夾
    folders.add(
      VideoFolder.createSystemFolder(
        id: 'root',
        name: '所有視頻',
        path: videoDir.path,
        description: '所有視頻文件',
        color: 0xFF2196F3,
        icon: 'video_library',
      ),
    );

    // 最近添加
    folders.add(
      VideoFolder.createSystemFolder(
        id: 'recent',
        name: '最近添加',
        path: videoDir.path,
        description: '最近添加的視頻',
        color: 0xFF4CAF50,
        icon: 'schedule',
      ),
    );

    // 我的最愛
    folders.add(
      VideoFolder.createSystemFolder(
        id: 'favorites',
        name: '我的最愛',
        path: videoDir.path,
        description: '收藏的視頻',
        color: 0xFFFF5722,
        icon: 'favorite',
      ),
    );

    return folders;
  }

  /// 創建新文件夾
  Future<VideoFolder> createFolder({
    required String name,
    String? parentId,
    String? description,
    int? color,
    String? icon,
  }) async {
    try {
      // 生成唯一ID
      final id = _uuid.v4();

      // 確定父文件夾路徑
      String parentPath;
      if (parentId != null) {
        final folders = await loadFolders();
        final parentFolder = folders.firstWhere(
          (f) => f.id == parentId,
          orElse: () => throw Exception('父文件夾不存在'),
        );
        parentPath = parentFolder.path;
      } else {
        parentPath = _storageService.videoDir.path;
      }

      // 創建文件夾路徑
      final folderPath = path.join(parentPath, name);
      final directory = Directory(folderPath);

      // 檢查文件夾是否已存在
      if (directory.existsSync()) {
        throw Exception('文件夾已存在');
      }

      // 創建物理文件夾
      await directory.create(recursive: true);

      // 創建文件夾對象
      final folder = VideoFolder.createUserFolder(
        id: id,
        name: name,
        path: folderPath,
        parentId: parentId,
        description: description,
        color: color,
        icon: icon,
      );

      // 保存到配置文件
      final folders = await loadFolders();
      folders.add(folder);
      await _saveFolders(folders);

      Logger.info('Created folder: $name at $folderPath');
      return folder;
    } catch (e) {
      Logger.error('Failed to create folder: $e');
      throw Exception('創建文件夾失敗: $e');
    }
  }

  /// 重命名文件夾
  Future<VideoFolder> renameFolder(String folderId, String newName) async {
    try {
      final folders = await loadFolders();
      final folderIndex = folders.indexWhere((f) => f.id == folderId);

      if (folderIndex == -1) {
        throw Exception('文件夾不存在');
      }

      final folder = folders[folderIndex];

      if (folder.isSystem) {
        throw Exception('無法重命名系統文件夾');
      }

      // 計算新路徑
      final parentPath = path.dirname(folder.path);
      final newPath = path.join(parentPath, newName);

      // 檢查新路徑是否已存在
      if (Directory(newPath).existsSync()) {
        throw Exception('文件夾名稱已存在');
      }

      // 重命名物理文件夾
      await folder.directory.rename(newPath);

      // 更新文件夾對象
      final updatedFolder = folder.copyWith(
        name: newName,
        path: newPath,
        updatedAt: DateTime.now(),
      );

      folders[folderIndex] = updatedFolder;
      await _saveFolders(folders);

      Logger.info('Renamed folder: ${folder.name} -> $newName');
      return updatedFolder;
    } catch (e) {
      Logger.error('Failed to rename folder: $e');
      throw Exception('重命名文件夾失敗: $e');
    }
  }

  /// 刪除文件夾
  Future<bool> deleteFolder(String folderId, {bool deleteFiles = false}) async {
    try {
      final folders = await loadFolders();
      final folder = folders.firstWhere(
        (f) => f.id == folderId,
        orElse: () => throw Exception('文件夾不存在'),
      );

      if (folder.isSystem) {
        throw Exception('無法刪除系統文件夾');
      }

      // 檢查是否有子文件夾
      final hasChildren = folders.any((f) => f.parentId == folderId);
      if (hasChildren) {
        throw Exception('文件夾包含子文件夾，無法刪除');
      }

      // 檢查文件夾是否為空
      final directory = folder.directory;
      if (directory.existsSync()) {
        final contents = directory.listSync();
        if (contents.isNotEmpty && !deleteFiles) {
          throw Exception('文件夾不為空，無法刪除');
        }

        // 刪除物理文件夾
        await directory.delete(recursive: deleteFiles);
      }

      // 從配置中移除
      folders.removeWhere((f) => f.id == folderId);
      await _saveFolders(folders);

      Logger.info('Deleted folder: ${folder.name}');
      return true;
    } catch (e) {
      Logger.error('Failed to delete folder: $e');
      throw Exception('刪除文件夾失敗: $e');
    }
  }

  /// 移動文件夾
  Future<VideoFolder> moveFolder(String folderId, String? newParentId) async {
    try {
      final folders = await loadFolders();
      final folderIndex = folders.indexWhere((f) => f.id == folderId);

      if (folderIndex == -1) {
        throw Exception('文件夾不存在');
      }

      final folder = folders[folderIndex];

      if (folder.isSystem) {
        throw Exception('無法移動系統文件夾');
      }

      // 確定新父文件夾路徑
      String newParentPath;
      if (newParentId != null) {
        final parentFolder = folders.firstWhere(
          (f) => f.id == newParentId,
          orElse: () => throw Exception('目標文件夾不存在'),
        );
        newParentPath = parentFolder.path;
      } else {
        newParentPath = _storageService.videoDir.path;
      }

      // 計算新路徑
      final newPath = path.join(newParentPath, folder.name);

      // 檢查新路徑是否已存在
      if (Directory(newPath).existsSync()) {
        throw Exception('目標位置已存在同名文件夾');
      }

      // 移動物理文件夾
      await folder.directory.rename(newPath);

      // 更新文件夾對象
      final updatedFolder = folder.copyWith(
        path: newPath,
        parentId: newParentId,
        updatedAt: DateTime.now(),
      );

      folders[folderIndex] = updatedFolder;
      await _saveFolders(folders);

      Logger.info('Moved folder: ${folder.name} to $newPath');
      return updatedFolder;
    } catch (e) {
      Logger.error('Failed to move folder: $e');
      throw Exception('移動文件夾失敗: $e');
    }
  }

  /// 獲取文件夾中的視頻文件
  Future<List<VideoFile>> getFolderVideos(String folderId) async {
    try {
      final folders = await loadFolders();
      final folder = folders.firstWhere(
        (f) => f.id == folderId,
        orElse: () => throw Exception('文件夾不存在'),
      );

      if (!folder.directory.existsSync()) {
        return [];
      }

      final videoFiles = <VideoFile>[];
      final contents = folder.directory.listSync();

      for (final entity in contents) {
        if (entity is File && _isVideoFile(entity.path)) {
          videoFiles.add(VideoFile.fromFile(entity));
        }
      }

      // 按文件名排序
      videoFiles.sort((a, b) => a.fileName.compareTo(b.fileName));

      return videoFiles;
    } catch (e) {
      Logger.error('Failed to get folder videos: $e');
      return [];
    }
  }

  /// 檢查是否為視頻文件
  bool _isVideoFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    const videoExtensions = [
      '.mp4',
      '.avi',
      '.mov',
      '.mkv',
      '.wmv',
      '.flv',
      '.webm',
    ];
    return videoExtensions.contains(extension);
  }

  /// 移動視頻文件到文件夾
  Future<bool> moveVideoToFolder(
    VideoFile videoFile,
    String targetFolderId,
  ) async {
    return await ErrorUtils.safeExecute<bool>(
          () async {
            final folders = await loadFolders();
            final targetFolder = folders.firstWhere(
              (f) => f.id == targetFolderId,
              orElse: () => throw Exception('目標文件夾不存在'),
            );

            final targetPath = path.join(targetFolder.path, videoFile.fileName);

            // 使用FileUtils進行安全移動
            final success = await FileUtils.moveFile(
              videoFile.path,
              targetPath,
            );
            if (success) {
              Logger.info(
                'Moved video: ${videoFile.fileName} to ${targetFolder.name}',
              );
              ErrorUtils.showSuccess('移動成功', '視頻文件已移動到: ${targetFolder.name}');
            }
            return success;
          },
          context: '移動視頻文件',
          defaultValue: false,
        ) ??
        false;
  }
}
