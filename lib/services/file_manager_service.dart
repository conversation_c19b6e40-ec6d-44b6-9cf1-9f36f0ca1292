import 'dart:convert';
import 'dart:io';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import '../models/file_item.dart';
import '../utils/logger.dart';
import '../utils/file_utils.dart';
import '../utils/error_utils.dart';

/// 文件管理服務
/// 負責文件的增刪改查、分類管理、搜索等功能
class FileManagerService extends GetxService {
  // 響應式文件列表
  final _allFiles = <FileItem>[].obs;
  final _favoriteFiles = <FileItem>[].obs;
  final _recentFiles = <FileItem>[].obs;
  final _isLoading = false.obs;
  final _isScanning = false.obs;

  // 文件索引路徑
  late String _fileIndexPath;

  // 支持的文件類型
  final Set<String> _supportedExtensions = {
    // 音頻
    '.mp3', '.m4a', '.aac', '.wav', '.flac', '.ogg', '.wma',
    // 視頻
    '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v',
    // PDF
    '.pdf',
    // 圖片
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg',
    // 文檔
    '.txt', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.rtf',
    // 壓縮包
    '.zip', '.rar', '.7z', '.tar', '.gz',
  };

  // Getters
  List<FileItem> get allFiles => _allFiles.toList();
  List<FileItem> get favoriteFiles => _favoriteFiles.toList();
  List<FileItem> get recentFiles => _recentFiles.toList();
  bool get isLoading => _isLoading.value;
  bool get isScanning => _isScanning.value;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeService();
  }

  /// 初始化服務
  Future<void> _initializeService() async {
    try {
      // 獲取應用文檔目錄
      final appDir = await getApplicationDocumentsDirectory();
      _fileIndexPath = '${appDir.path}/file_index.json';

      // 載入文件索引
      await loadFileIndex();

      Logger.info('File manager service initialized');
    } catch (e) {
      Logger.error('Error initializing file manager service', error: e);
    }
  }

  /// 載入文件索引
  Future<void> loadFileIndex() async {
    try {
      _isLoading.value = true;

      final file = File(_fileIndexPath);
      if (file.existsSync()) {
        final jsonString = await file.readAsString();
        final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;

        final allFilesList =
            (jsonData['allFiles'] as List<dynamic>?)
                ?.map((item) => FileItem.fromJson(item as Map<String, dynamic>))
                .toList() ??
            [];

        final favoriteFilesList =
            (jsonData['favoriteFiles'] as List<dynamic>?)
                ?.map((item) => FileItem.fromJson(item as Map<String, dynamic>))
                .toList() ??
            [];

        final recentFilesList =
            (jsonData['recentFiles'] as List<dynamic>?)
                ?.map((item) => FileItem.fromJson(item as Map<String, dynamic>))
                .toList() ??
            [];

        // 過濾不存在的文件
        _allFiles.value = allFilesList.where((file) => file.exists).toList();
        _favoriteFiles.value = favoriteFilesList
            .where((file) => file.exists)
            .toList();
        _recentFiles.value = recentFilesList
            .where((file) => file.exists)
            .toList();
      }
    } catch (e) {
      Logger.error('Error loading file index', error: e);
    } finally {
      _isLoading.value = false;
    }
  }

  /// 保存文件索引
  Future<void> saveFileIndex() async {
    try {
      final file = File(_fileIndexPath);
      final jsonData = {
        'allFiles': _allFiles.map((file) => file.toJson()).toList(),
        'favoriteFiles': _favoriteFiles.map((file) => file.toJson()).toList(),
        'recentFiles': _recentFiles.map((file) => file.toJson()).toList(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };

      final jsonString = jsonEncode(jsonData);
      await file.writeAsString(jsonString);
    } catch (e) {
      Logger.error('Error saving file index', error: e);
    }
  }

  /// 掃描文件系統
  Future<void> scanFileSystem() async {
    try {
      _isScanning.value = true;

      final appDir = await getApplicationDocumentsDirectory();
      final directories = [
        Directory('${appDir.path}/music'),
        Directory('${appDir.path}/video'),
        Directory('${appDir.path}/pdf'),
      ];

      final foundFiles = <FileItem>[];

      for (final directory in directories) {
        if (directory.existsSync()) {
          await for (final entity in directory.list(recursive: true)) {
            if (entity is File) {
              final extension = entity.path.toLowerCase().substring(
                entity.path.lastIndexOf('.'),
              );

              if (_supportedExtensions.contains(extension)) {
                final fileItem = FileItem.fromFile(entity);
                foundFiles.add(fileItem);
              }
            }
          }
        }
      }

      // 更新文件列表
      _allFiles.value = foundFiles;

      // 更新收藏和最近文件列表
      _updateFavoriteFiles();
      _updateRecentFiles();

      // 保存索引
      await saveFileIndex();
    } catch (e) {
      Logger.error('Error scanning file system', error: e);
    } finally {
      _isScanning.value = false;
    }
  }

  /// 添加文件到索引
  Future<void> addFileToIndex(File file) async {
    try {
      final fileItem = FileItem.fromFile(file);

      // 檢查是否已存在
      if (!_allFiles.any((item) => item.path == fileItem.path)) {
        _allFiles.add(fileItem);
        await saveFileIndex();
      }
    } catch (e) {
      Logger.error('Error adding file to index', error: e);
    }
  }

  /// 從索引移除文件
  Future<void> removeFileFromIndex(String filePath) async {
    try {
      _allFiles.removeWhere((item) => item.path == filePath);
      _favoriteFiles.removeWhere((item) => item.path == filePath);
      _recentFiles.removeWhere((item) => item.path == filePath);
      await saveFileIndex();
    } catch (e) {
      Logger.error('Error removing file from index', error: e);
    }
  }

  /// 更新文件信息
  Future<void> updateFileItem(FileItem updatedFile) async {
    try {
      final index = _allFiles.indexWhere(
        (item) => item.path == updatedFile.path,
      );
      if (index >= 0) {
        _allFiles[index] = updatedFile;

        // 更新其他列表中的文件
        _updateFileInList(_favoriteFiles, updatedFile);
        _updateFileInList(_recentFiles, updatedFile);

        await saveFileIndex();
      }
    } catch (e) {
      Logger.error('Error updating file item', error: e);
    }
  }

  /// 在列表中更新文件
  void _updateFileInList(RxList<FileItem> list, FileItem updatedFile) {
    final index = list.indexWhere((item) => item.path == updatedFile.path);
    if (index >= 0) {
      list[index] = updatedFile;
    }
  }

  /// 刪除文件
  Future<bool> deleteFile(FileItem fileItem) async {
    try {
      final file = File(fileItem.path);
      if (file.existsSync()) {
        await file.delete();
        await removeFileFromIndex(fileItem.path);
        return true;
      }
      return false;
    } catch (e) {
      Logger.error('Error deleting file', error: e);
      return false;
    }
  }

  /// 重命名文件
  Future<FileItem?> renameFile(FileItem fileItem, String newName) async {
    return await ErrorUtils.safeExecute<FileItem?>(() async {
      if (!FileUtils.exists(fileItem.path)) {
        ErrorUtils.showError('重命名失敗', '源文件不存在');
        return null;
      }

      final file = File(fileItem.path);
      final directory = file.parent;
      final extension = fileItem.extension;
      final newFileName = newName.endsWith(extension)
          ? newName
          : '$newName$extension';
      final newPath = '${directory.path}/$newFileName';

      // 檢查新文件名是否已存在
      if (FileUtils.exists(newPath)) {
        ErrorUtils.showError('重命名失敗', '文件名已存在');
        return null;
      }

      // 重命名文件
      final renamedFile = await file.rename(newPath);

      // 更新文件項目
      final updatedFileItem = fileItem.copyWith(
        path: renamedFile.path,
        fileName: newFileName,
        modifiedAt: DateTime.now(),
      );

      // 移除舊的文件項目並添加新的
      await removeFileFromIndex(fileItem.path);
      _allFiles.add(updatedFileItem);
      await saveFileIndex();

      ErrorUtils.showSuccess('重命名成功', '文件已重命名為: $newFileName');
      return updatedFileItem;
    }, context: '重命名文件');
  }

  /// 移動文件
  Future<FileItem?> moveFile(
    FileItem fileItem,
    Directory targetDirectory,
  ) async {
    return await ErrorUtils.safeExecute<FileItem?>(() async {
      final newPath = '${targetDirectory.path}/${fileItem.fileName}';

      // 使用FileUtils進行安全移動
      final success = await FileUtils.moveFile(fileItem.path, newPath);
      if (!success) return null;

      // 更新文件項目
      final updatedFileItem = fileItem.copyWith(
        path: newPath,
        modifiedAt: DateTime.now(),
      );

      // 更新索引
      await removeFileFromIndex(fileItem.path);
      _allFiles.add(updatedFileItem);
      await saveFileIndex();

      ErrorUtils.showSuccess('移動成功', '文件已移動到: ${targetDirectory.path}');
      return updatedFileItem;
    }, context: '移動文件');
  }

  /// 複製文件
  Future<FileItem?> copyFile(
    FileItem fileItem,
    Directory targetDirectory,
  ) async {
    return await ErrorUtils.safeExecute<FileItem?>(() async {
      if (!FileUtils.exists(fileItem.path)) {
        ErrorUtils.showError('複製失敗', '源文件不存在');
        return null;
      }

      // 生成唯一文件名
      final newPath = FileUtils.generateUniqueFileName(
        targetDirectory.path,
        fileItem.fileName,
      );

      // 使用FileUtils進行安全複製
      final success = await FileUtils.copyFile(fileItem.path, newPath);
      if (!success) return null;

      // 創建新的文件項目
      final copiedFile = File(newPath);
      final newFileItem = FileItem.fromFile(copiedFile);

      // 添加到索引
      _allFiles.add(newFileItem);
      await saveFileIndex();

      ErrorUtils.showSuccess('複製成功', '文件已複製到: ${targetDirectory.path}');
      return newFileItem;
    }, context: '複製文件');
  }

  /// 添加到收藏
  Future<void> addToFavorites(FileItem fileItem) async {
    try {
      if (!_favoriteFiles.any((item) => item.path == fileItem.path)) {
        final updatedFile = fileItem.copyWith(isFavorite: true);
        _favoriteFiles.add(updatedFile);
        await updateFileItem(updatedFile);
      }
    } catch (e) {
      Logger.error('Error adding to favorites', error: e);
    }
  }

  /// 從收藏移除
  Future<void> removeFromFavorites(FileItem fileItem) async {
    try {
      _favoriteFiles.removeWhere((item) => item.path == fileItem.path);
      final updatedFile = fileItem.copyWith(isFavorite: false);
      await updateFileItem(updatedFile);
    } catch (e) {
      Logger.error('Error removing from favorites', error: e);
    }
  }

  /// 添加到最近文件
  Future<void> addToRecent(FileItem fileItem) async {
    try {
      // 移除已存在的項目
      _recentFiles.removeWhere((item) => item.path == fileItem.path);

      // 添加到開頭
      final updatedFile = fileItem.updateLastAccessed();
      _recentFiles.insert(0, updatedFile);

      // 限制最近文件數量
      if (_recentFiles.length > 50) {
        _recentFiles.removeRange(50, _recentFiles.length);
      }

      await updateFileItem(updatedFile);
    } catch (e) {
      Logger.error('Error adding to recent', error: e);
    }
  }

  /// 更新收藏文件列表
  void _updateFavoriteFiles() {
    _favoriteFiles.value = _allFiles.where((file) => file.isFavorite).toList();
  }

  /// 更新最近文件列表
  void _updateRecentFiles() {
    final recentPaths = _recentFiles.map((file) => file.path).toSet();
    _recentFiles.value = _allFiles
        .where((file) => recentPaths.contains(file.path))
        .toList();
  }

  /// 搜索文件
  List<FileItem> searchFiles(String query, {FileType? type}) {
    if (query.isEmpty) return allFiles;

    final lowerQuery = query.toLowerCase();
    return allFiles.where((file) {
      final matchesQuery =
          file.fileName.toLowerCase().contains(lowerQuery) ||
          (file.description?.toLowerCase().contains(lowerQuery) ?? false) ||
          file.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));

      final matchesType = type == null || file.type == type;

      return matchesQuery && matchesType;
    }).toList();
  }

  /// 按類型獲取文件
  List<FileItem> getFilesByType(FileType type) {
    return allFiles.where((file) => file.type == type).toList();
  }

  /// 按標籤獲取文件
  List<FileItem> getFilesByTag(String tag) {
    return allFiles.where((file) => file.hasTag(tag)).toList();
  }

  /// 獲取所有標籤
  List<String> getAllTags() {
    final tags = <String>{};
    for (final file in allFiles) {
      tags.addAll(file.tags);
    }
    return tags.toList()..sort();
  }

  /// 排序文件
  List<FileItem> sortFiles(
    List<FileItem> files,
    FileSortType sortType, {
    bool ascending = true,
  }) {
    final sortedFiles = List<FileItem>.from(files);

    switch (sortType) {
      case FileSortType.name:
        sortedFiles.sort(
          (a, b) => ascending
              ? a.fileName.compareTo(b.fileName)
              : b.fileName.compareTo(a.fileName),
        );
        break;
      case FileSortType.size:
        sortedFiles.sort(
          (a, b) => ascending
              ? a.fileSize.compareTo(b.fileSize)
              : b.fileSize.compareTo(a.fileSize),
        );
        break;
      case FileSortType.type:
        sortedFiles.sort(
          (a, b) => ascending
              ? a.type.name.compareTo(b.type.name)
              : b.type.name.compareTo(a.type.name),
        );
        break;
      case FileSortType.modifiedTime:
        sortedFiles.sort(
          (a, b) => ascending
              ? a.modifiedAt.compareTo(b.modifiedAt)
              : b.modifiedAt.compareTo(a.modifiedAt),
        );
        break;
      case FileSortType.createdTime:
        sortedFiles.sort(
          (a, b) => ascending
              ? a.createdAt.compareTo(b.createdAt)
              : b.createdAt.compareTo(a.createdAt),
        );
        break;
    }

    return sortedFiles;
  }

  /// 獲取文件統計信息
  Map<String, dynamic> getFileStatistics() {
    final stats = <String, dynamic>{};

    // 總文件數
    stats['totalFiles'] = allFiles.length;

    // 按類型統計
    for (final type in FileType.values) {
      final count = allFiles.where((file) => file.type == type).length;
      stats['${type.name}Count'] = count;
    }

    // 總文件大小
    final totalSize = allFiles.fold<int>(0, (sum, file) => sum + file.fileSize);
    stats['totalSize'] = totalSize;

    // 收藏文件數
    stats['favoriteCount'] = favoriteFiles.length;

    // 最近文件數
    stats['recentCount'] = recentFiles.length;

    return stats;
  }
}
