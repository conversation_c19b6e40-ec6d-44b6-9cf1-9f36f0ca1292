import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdfx/pdfx.dart' as pdfx;
import '../models/pdf_file.dart';
import '../utils/logger.dart';

/// PDF閱讀服務
/// 負責PDF文件的渲染、頁面管理和閱讀進度記錄
class PdfReaderService extends GetxService {
  pdfx.PdfController? _pdfController;

  // 響應式狀態
  final _currentPdfFile = Rx<PdfFile?>(null);
  final _currentPage = 1.obs;
  final _totalPages = 0.obs;
  final _isLoading = false.obs;
  final _readingSettings = const PdfReadingSettings().obs;
  final _errorMessage = Rx<String?>(null);

  // 流訂閱
  StreamSubscription<int>? _pageSubscription;

  // Getters
  PdfFile? get currentPdfFile => _currentPdfFile.value;
  int get currentPage => _currentPage.value;
  int get totalPages => _totalPages.value;
  bool get isLoading => _isLoading.value;
  PdfReadingSettings get readingSettings => _readingSettings.value;
  String? get errorMessage => _errorMessage.value;
  pdfx.PdfController? get pdfController => _pdfController;

  @override
  Future<void> onInit() async {
    super.onInit();
    _initializeService();
  }

  @override
  void onClose() {
    _dispose();
    super.onClose();
  }

  /// 初始化服務
  void _initializeService() {
    Logger.info('PDF reader service initialized');
  }

  /// 打開PDF文件
  Future<bool> openPdf(PdfFile pdfFile) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = null;

      // 檢查文件是否存在
      if (!File(pdfFile.path).existsSync()) {
        throw Exception('PDF文件不存在: ${pdfFile.path}');
      }

      // 釋放之前的控制器
      await _disposePdfController();

      // 創建新的PDF控制器
      _pdfController = pdfx.PdfController(
        document: pdfx.PdfDocument.openFile(pdfFile.path),
        initialPage: pdfFile.currentPage,
      );

      // 獲取總頁數
      final document = await _pdfController!.document;
      _totalPages.value = document.pagesCount;

      // 更新PDF文件信息
      final updatedPdfFile = pdfFile.copyWith(totalPages: _totalPages.value);
      _currentPdfFile.value = updatedPdfFile;

      // 設置當前頁面
      _currentPage.value = pdfFile.currentPage;

      // 監聽頁面變化
      _startPageListener();

      return true;
    } catch (e) {
      Logger.error('Error opening PDF', error: e);
      _errorMessage.value = '打開PDF失敗: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// 跳轉到指定頁面
  Future<void> goToPage(int page) async {
    if (_pdfController == null || page < 1 || page > _totalPages.value) {
      return;
    }

    try {
      await _pdfController!.animateToPage(
        page,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } catch (e) {
      Logger.error('Error going to page', error: e);
    }
  }

  /// 下一頁
  Future<void> nextPage() async {
    if (_currentPage.value < _totalPages.value) {
      await goToPage(_currentPage.value + 1);
    }
  }

  /// 上一頁
  Future<void> previousPage() async {
    if (_currentPage.value > 1) {
      await goToPage(_currentPage.value - 1);
    }
  }

  /// 第一頁
  Future<void> firstPage() async {
    await goToPage(1);
  }

  /// 最後一頁
  Future<void> lastPage() async {
    await goToPage(_totalPages.value);
  }

  /// 縮放功能（由PdfView組件內部處理）
  Future<void> zoomIn() async {
    // 縮放功能由PdfView組件內部處理
    Logger.debug('Zoom in requested');
  }

  /// 縮小
  Future<void> zoomOut() async {
    // 縮放功能由PdfView組件內部處理
    Logger.debug('Zoom out requested');
  }

  /// 重置縮放
  Future<void> resetZoom() async {
    // 縮放功能由PdfView組件內部處理
    Logger.debug('Reset zoom requested');
  }

  /// 添加書籤
  void addBookmark(int page) {
    if (_currentPdfFile.value == null) return;

    final updatedPdfFile = _currentPdfFile.value!.addBookmark(page);
    _currentPdfFile.value = updatedPdfFile;
    _savePdfProgress(updatedPdfFile);
  }

  /// 移除書籤
  void removeBookmark(int page) {
    if (_currentPdfFile.value == null) return;

    final updatedPdfFile = _currentPdfFile.value!.removeBookmark(page);
    _currentPdfFile.value = updatedPdfFile;
    _savePdfProgress(updatedPdfFile);
  }

  /// 切換書籤狀態
  void toggleBookmark(int page) {
    if (_currentPdfFile.value == null) return;

    final updatedPdfFile = _currentPdfFile.value!.toggleBookmark(page);
    _currentPdfFile.value = updatedPdfFile;
    _savePdfProgress(updatedPdfFile);
  }

  /// 檢查是否有書籤
  bool hasBookmarkAt(int page) {
    return _currentPdfFile.value?.hasBookmarkAt(page) ?? false;
  }

  /// 獲取所有書籤
  List<int> get bookmarks {
    return _currentPdfFile.value?.bookmarks ?? [];
  }

  /// 更新閱讀設置
  void updateReadingSettings(PdfReadingSettings settings) {
    _readingSettings.value = settings;
    // TODO: 保存設置到本地存儲
  }

  /// 更新頁面適配模式
  void updateFitMode(PageFitMode fitMode) {
    final newSettings = _readingSettings.value.copyWith(fitMode: fitMode);
    updateReadingSettings(newSettings);
  }

  /// 更新閱讀方向
  void updateReadingDirection(ReadingDirection direction) {
    final newSettings = _readingSettings.value.copyWith(direction: direction);
    updateReadingSettings(newSettings);
  }

  /// 更新亮度
  void updateBrightness(double brightness) {
    final newSettings = _readingSettings.value.copyWith(brightness: brightness);
    updateReadingSettings(newSettings);
  }

  /// 關閉PDF
  Future<void> closePdf() async {
    // 保存閱讀進度
    if (_currentPdfFile.value != null) {
      final updatedPdfFile = _currentPdfFile.value!.updateReadProgress(
        _currentPage.value,
      );
      _savePdfProgress(updatedPdfFile);
    }

    await _disposePdfController();
    _currentPdfFile.value = null;
    _currentPage.value = 1;
    _totalPages.value = 0;
    _errorMessage.value = null;
  }

  /// 開始頁面監聽
  void _startPageListener() {
    _pageSubscription?.cancel();

    if (_pdfController != null) {
      // 監聽頁面變化
      _pdfController!.pageListenable.addListener(() {
        final newPage = _pdfController!.page;
        if (newPage != _currentPage.value) {
          _currentPage.value = newPage;
          _onPageChanged(newPage);
        }
      });
    }
  }

  /// 頁面變化處理
  void _onPageChanged(int page) {
    // 自動保存進度
    if (_readingSettings.value.autoSaveProgress &&
        _currentPdfFile.value != null) {
      final updatedPdfFile = _currentPdfFile.value!.updateReadProgress(page);
      _currentPdfFile.value = updatedPdfFile;
      _savePdfProgress(updatedPdfFile);
    }
  }

  /// 保存PDF閱讀進度
  void _savePdfProgress(PdfFile pdfFile) {
    // TODO: 實現本地存儲邏輯
    Logger.debug(
      'Saving PDF progress: ${pdfFile.title} - Page ${pdfFile.currentPage}',
    );
  }

  /// 釋放PDF控制器
  Future<void> _disposePdfController() async {
    _pageSubscription?.cancel();
    _pageSubscription = null;

    if (_pdfController != null) {
      _pdfController!.dispose();
      _pdfController = null;
    }
  }

  /// 清理資源
  void _dispose() {
    _disposePdfController();
  }

  /// 獲取閱讀進度百分比
  double get readProgress {
    if (_totalPages.value == 0) return 0.0;
    return (_currentPage.value / _totalPages.value).clamp(0.0, 1.0);
  }

  /// 獲取閱讀進度文字
  String get readProgressText {
    return '第 ${_currentPage.value} 頁 / 共 ${_totalPages.value} 頁';
  }

  /// 檢查是否可以翻到下一頁
  bool get canGoNext => _currentPage.value < _totalPages.value;

  /// 檢查是否可以翻到上一頁
  bool get canGoPrevious => _currentPage.value > 1;
}
