import 'dart:convert';
import 'dart:io';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import '../models/playlist.dart';
import '../models/audio_file.dart';
import '../utils/logger.dart';

/// 播放列表管理服務
/// 負責播放列表的創建、編輯、刪除和持久化存儲
class PlaylistService extends GetxService {
  // 響應式播放列表
  final _playlists = <Playlist>[].obs;
  final _isLoading = false.obs;

  // 文件路徑
  late String _playlistsFilePath;

  // Getters
  List<Playlist> get playlists => _playlists.toList();
  bool get isLoading => _isLoading.value;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeService();
  }

  /// 初始化服務
  Future<void> _initializeService() async {
    try {
      // 獲取應用文檔目錄
      final appDir = await getApplicationDocumentsDirectory();
      _playlistsFilePath = '${appDir.path}/playlists.json';

      // 載入播放列表
      await loadPlaylists();

      // 創建系統預設播放列表
      await _createSystemPlaylists();

      Logger.info('Playlist service initialized');
    } catch (e) {
      Logger.error('Error initializing playlist service', error: e);
    }
  }

  /// 載入播放列表
  Future<void> loadPlaylists() async {
    try {
      _isLoading.value = true;

      final file = File(_playlistsFilePath);
      if (file.existsSync()) {
        final jsonString = await file.readAsString();
        final jsonList = jsonDecode(jsonString) as List<dynamic>;

        final playlists = jsonList
            .map((json) => Playlist.fromJson(json as Map<String, dynamic>))
            .toList();

        _playlists.value = playlists;
      }
    } catch (e) {
      Logger.error('Error loading playlists', error: e);
    } finally {
      _isLoading.value = false;
    }
  }

  /// 保存播放列表
  Future<void> savePlaylists() async {
    try {
      final file = File(_playlistsFilePath);
      final jsonList = _playlists.map((playlist) => playlist.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      await file.writeAsString(jsonString);
    } catch (e) {
      Logger.error('Error saving playlists', error: e);
    }
  }

  /// 創建播放列表
  Future<Playlist> createPlaylist({
    required String name,
    String? description,
    String? coverImagePath,
    List<AudioFile>? audioFiles,
  }) async {
    final playlist = Playlist.create(
      name: name,
      description: description,
      coverImagePath: coverImagePath,
      audioFiles: audioFiles,
    );

    _playlists.add(playlist);
    await savePlaylists();

    return playlist;
  }

  /// 更新播放列表
  Future<bool> updatePlaylist(Playlist updatedPlaylist) async {
    try {
      final index = _playlists.indexWhere((p) => p.id == updatedPlaylist.id);
      if (index >= 0) {
        _playlists[index] = updatedPlaylist.copyWith(updatedAt: DateTime.now());
        await savePlaylists();
        return true;
      }
      return false;
    } catch (e) {
      Logger.error('Error updating playlist', error: e);
      return false;
    }
  }

  /// 刪除播放列表
  Future<bool> deletePlaylist(String playlistId) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist?.isSystem == true) {
        // 不能刪除系統播放列表
        return false;
      }

      _playlists.removeWhere((p) => p.id == playlistId);
      await savePlaylists();
      return true;
    } catch (e) {
      Logger.error('Error deleting playlist', error: e);
      return false;
    }
  }

  /// 根據ID獲取播放列表
  Playlist? getPlaylistById(String id) {
    try {
      return _playlists.firstWhere((p) => p.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 根據名稱獲取播放列表
  Playlist? getPlaylistByName(String name) {
    try {
      return _playlists.firstWhere((p) => p.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 添加歌曲到播放列表
  Future<bool> addAudioToPlaylist(
    String playlistId,
    AudioFile audioFile,
  ) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.addAudioFile(audioFile);
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error adding audio to playlist', error: e);
      return false;
    }
  }

  /// 添加多首歌曲到播放列表
  Future<bool> addAudiosToPlaylist(
    String playlistId,
    List<AudioFile> audioFiles,
  ) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.addAudioFiles(audioFiles);
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error adding audios to playlist', error: e);
      return false;
    }
  }

  /// 從播放列表移除歌曲
  Future<bool> removeAudioFromPlaylist(
    String playlistId,
    AudioFile audioFile,
  ) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.removeAudioFile(audioFile);
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error removing audio from playlist', error: e);
      return false;
    }
  }

  /// 移動播放列表中的歌曲
  Future<bool> moveAudioInPlaylist(
    String playlistId,
    int oldIndex,
    int newIndex,
  ) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.moveAudioFile(oldIndex, newIndex);
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error moving audio in playlist', error: e);
      return false;
    }
  }

  /// 清空播放列表
  Future<bool> clearPlaylist(String playlistId) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.clear();
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error clearing playlist', error: e);
      return false;
    }
  }

  /// 打亂播放列表
  Future<bool> shufflePlaylist(String playlistId) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.shuffle();
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error shuffling playlist', error: e);
      return false;
    }
  }

  /// 排序播放列表
  Future<bool> sortPlaylist(
    String playlistId,
    PlaylistSortType sortType, {
    bool ascending = true,
  }) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        Playlist updatedPlaylist;
        switch (sortType) {
          case PlaylistSortType.title:
            updatedPlaylist = playlist.sortByTitle(ascending: ascending);
            break;
          case PlaylistSortType.artist:
            updatedPlaylist = playlist.sortByArtist(ascending: ascending);
            break;
          case PlaylistSortType.album:
            updatedPlaylist = playlist.sortByAlbum(ascending: ascending);
            break;
          case PlaylistSortType.duration:
            updatedPlaylist = playlist.sortByDuration(ascending: ascending);
            break;
        }
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error sorting playlist', error: e);
      return false;
    }
  }

  /// 複製播放列表
  Future<Playlist?> duplicatePlaylist(String playlistId, String newName) async {
    try {
      final originalPlaylist = getPlaylistById(playlistId);
      if (originalPlaylist != null) {
        return await createPlaylist(
          name: newName,
          description: originalPlaylist.description,
          audioFiles: originalPlaylist.audioFiles,
        );
      }
      return null;
    } catch (e) {
      Logger.error('Error duplicating playlist', error: e);
      return null;
    }
  }

  /// 搜索播放列表
  List<Playlist> searchPlaylists(String query) {
    if (query.isEmpty) return playlists;

    final lowerQuery = query.toLowerCase();
    return playlists.where((playlist) {
      return playlist.name.toLowerCase().contains(lowerQuery) ||
          (playlist.description?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  /// 獲取包含指定歌曲的播放列表
  List<Playlist> getPlaylistsContaining(AudioFile audioFile) {
    return playlists.where((playlist) => playlist.contains(audioFile)).toList();
  }

  /// 創建系統預設播放列表
  Future<void> _createSystemPlaylists() async {
    // 檢查是否已存在系統播放列表
    final favoritesExists = playlists.any((p) => p.id == 'favorites');
    final recentExists = playlists.any((p) => p.id == 'recent');

    if (!favoritesExists) {
      final favorites = Playlist.system(
        id: 'favorites',
        name: '我的最愛',
        description: '收藏的歌曲',
      );
      _playlists.add(favorites);
    }

    if (!recentExists) {
      final recent = Playlist.system(
        id: 'recent',
        name: '最近播放',
        description: '最近播放的歌曲',
      );
      _playlists.add(recent);
    }

    await savePlaylists();
  }

  /// 獲取我的最愛播放列表
  Playlist? get favoritesPlaylist => getPlaylistById('favorites');

  /// 獲取最近播放列表
  Playlist? get recentPlaylist => getPlaylistById('recent');

  /// 添加到我的最愛
  Future<bool> addToFavorites(AudioFile audioFile) async {
    return await addAudioToPlaylist('favorites', audioFile);
  }

  /// 從我的最愛移除
  Future<bool> removeFromFavorites(AudioFile audioFile) async {
    return await removeAudioFromPlaylist('favorites', audioFile);
  }

  /// 添加到最近播放
  Future<bool> addToRecent(AudioFile audioFile) async {
    final recentPlaylist = this.recentPlaylist;
    if (recentPlaylist != null) {
      // 如果已存在，先移除再添加到開頭
      var updatedPlaylist = recentPlaylist.removeAudioFile(audioFile);
      updatedPlaylist = updatedPlaylist.addAudioFile(audioFile);

      // 限制最近播放列表最多50首歌
      if (updatedPlaylist.audioFiles.length > 50) {
        final limitedAudioFiles = updatedPlaylist.audioFiles.take(50).toList();
        updatedPlaylist = updatedPlaylist.copyWith(
          audioFiles: limitedAudioFiles,
        );
      }

      return await updatePlaylist(updatedPlaylist);
    }
    return false;
  }

  /// 檢查歌曲是否在我的最愛中
  bool isInFavorites(AudioFile audioFile) {
    return favoritesPlaylist?.contains(audioFile) ?? false;
  }
}

/// 播放列表排序類型
enum PlaylistSortType { title, artist, album, duration }
