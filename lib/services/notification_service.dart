import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import '../utils/logger.dart';

class NotificationService extends GetxService {
  late FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;

  Future<NotificationService> init() async {
    _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

    // Android 初始化設置
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS 初始化設置
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onDidReceiveNotificationResponse,
    );

    Logger.info('Notification Service initialized');
    return this;
  }

  void _onDidReceiveNotificationResponse(NotificationResponse response) {
    // 處理通知點擊事件
    Logger.debug('Notification clicked: ${response.payload}');
  }

  // 顯示音樂播放通知
  Future<void> showMusicNotification({
    required String title,
    required String artist,
    required String album,
    bool isPlaying = false,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'music_channel',
          'Music Player',
          channelDescription: 'Music player controls',
          importance: Importance.low,
          priority: Priority.low,
          ongoing: true,
          autoCancel: false,
          showWhen: false,
          category: AndroidNotificationCategory.transport,
          actions: <AndroidNotificationAction>[
            AndroidNotificationAction(
              'previous',
              'Previous',
              icon: DrawableResourceAndroidBitmap('@drawable/ic_skip_previous'),
            ),
            AndroidNotificationAction(
              'play_pause',
              'Play/Pause',
              icon: DrawableResourceAndroidBitmap('@drawable/ic_play_pause'),
            ),
            AndroidNotificationAction(
              'next',
              'Next',
              icon: DrawableResourceAndroidBitmap('@drawable/ic_skip_next'),
            ),
          ],
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(categoryIdentifier: 'music_category');

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      0,
      title,
      '$artist - $album',
      platformChannelSpecifics,
      payload: 'music_notification',
    );
  }

  // 取消音樂通知
  Future<void> cancelMusicNotification() async {
    await _flutterLocalNotificationsPlugin.cancel(0);
  }

  // 顯示一般通知
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'general_channel',
          'General Notifications',
          channelDescription: 'General app notifications',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails();

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  // 取消所有通知
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }
}
