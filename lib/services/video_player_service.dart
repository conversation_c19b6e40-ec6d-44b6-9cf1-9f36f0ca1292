import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart' as video_player;
import '../models/video_file.dart';
import '../models/video_player_state.dart' as app_state;
import '../utils/logger.dart';
import 'video_playlist_service.dart';

/// 視頻播放服務
/// 負責視頻播放的核心功能，包括播放控制、全屏切換、畫中畫等
class VideoPlayerService extends GetxService {
  video_player.VideoPlayerController? _videoController;

  // 響應式狀態
  final _playerState = app_state.VideoPlayerFullState().obs;
  final _currentPlaylist = <VideoFile>[].obs;
  final _currentIndex = (-1).obs;

  // 控制器顯示定時器
  Timer? _controlsTimer;

  // 流訂閱
  StreamSubscription<void>? _positionSubscription;

  // 服務依賴
  VideoPlaylistService? _playlistService;

  // Getters
  app_state.VideoPlayerFullState get playerState => _playerState.value;
  List<VideoFile> get currentPlaylist => _currentPlaylist.toList();
  int get currentIndex => _currentIndex.value;
  VideoFile? get currentVideo =>
      currentIndex >= 0 && currentIndex < currentPlaylist.length
      ? currentPlaylist[currentIndex]
      : null;
  video_player.VideoPlayerController? get videoController => _videoController;

  @override
  Future<void> onInit() async {
    super.onInit();
    _initializeService();
  }

  @override
  void onClose() {
    _dispose();
    super.onClose();
  }

  /// 初始化服務
  void _initializeService() {
    try {
      _playlistService = Get.find<VideoPlaylistService>();
    } catch (e) {
      // 服務可能還未初始化，稍後再試
      Logger.warning('Video playlist service not yet available: $e');
    }
    Logger.info('Video player service initialized');
  }

  /// 播放視頻文件
  Future<bool> playVideo(VideoFile videoFile) async {
    try {
      _playerState.value = _playerState.value.copyWith(
        playerState: app_state.VideoPlayerState.loading,
        currentVideoPath: videoFile.path,
        errorMessage: null,
      );

      // 檢查文件是否存在
      if (!File(videoFile.path).existsSync()) {
        throw Exception('視頻文件不存在: ${videoFile.path}');
      }

      // 釋放之前的控制器
      await _disposeController();

      // 創建新的視頻控制器
      _videoController = video_player.VideoPlayerController.file(
        File(videoFile.path),
      );

      // 初始化控制器
      await _videoController!.initialize();

      // 監聽播放狀態變化
      _videoController!.addListener(_onVideoStateChanged);

      // 設置播放位置（如果有播放記錄）
      if (videoFile.lastPlayPosition > 0) {
        await _videoController!.seekTo(
          Duration(milliseconds: videoFile.lastPlayPosition),
        );
      }

      // 更新狀態
      _playerState.value = _playerState.value.copyWith(
        playerState: app_state.VideoPlayerState.paused,
        totalDuration: _videoController!.value.duration.inMilliseconds,
        currentPosition: _videoController!.value.position.inMilliseconds,
      );

      // 添加到最近播放
      _addToRecentPlaylist(videoFile);

      // 開始播放
      await _videoController!.play();

      // 開始位置監聽
      _startPositionListener();

      // 播放時啟動控制器隱藏定時器
      _startControlsTimer();

      return true;
    } catch (e) {
      Logger.error('Error playing video', error: e);
      _playerState.value = _playerState.value.copyWith(
        playerState: app_state.VideoPlayerState.error,
        errorMessage: '播放失敗: $e',
      );
      return false;
    }
  }

  /// 播放視頻列表
  Future<bool> playPlaylist(
    List<VideoFile> playlist, {
    int startIndex = 0,
  }) async {
    try {
      if (playlist.isEmpty) {
        throw Exception('播放列表為空');
      }

      if (startIndex < 0 || startIndex >= playlist.length) {
        throw Exception('無效的起始索引');
      }

      _currentPlaylist.value = playlist;
      _currentIndex.value = startIndex;

      return await playVideo(playlist[startIndex]);
    } catch (e) {
      Logger.error('Error playing playlist', error: e);
      _playerState.value = _playerState.value.copyWith(
        playerState: app_state.VideoPlayerState.error,
        errorMessage: '播放列表失敗: $e',
      );
      return false;
    }
  }

  /// 暫停播放
  Future<void> pause() async {
    try {
      if (_videoController != null && _videoController!.value.isPlaying) {
        await _videoController!.pause();
        // 暫停時取消控制器隱藏定時器
        _cancelControlsTimer();
      }
    } catch (e) {
      Logger.error('Error pausing video', error: e);
    }
  }

  /// 恢復播放
  Future<void> resume() async {
    try {
      if (_videoController != null && !_videoController!.value.isPlaying) {
        await _videoController!.play();
        // 播放時啟動控制器隱藏定時器
        _startControlsTimer();
      }
    } catch (e) {
      Logger.error('Error resuming video', error: e);
    }
  }

  /// 停止播放
  Future<void> stop() async {
    try {
      if (_videoController != null) {
        await _videoController!.pause();
        await _videoController!.seekTo(Duration.zero);
        _playerState.value = _playerState.value.copyWith(
          playerState: app_state.VideoPlayerState.stopped,
          currentPosition: 0,
        );
      }
    } catch (e) {
      Logger.error('Error stopping video', error: e);
    }
  }

  /// 播放/暫停切換
  Future<void> togglePlayPause() async {
    if (_videoController == null) return;

    if (_videoController!.value.isPlaying) {
      await pause();
    } else {
      await resume();
    }
  }

  /// 跳轉到指定位置
  Future<void> seekTo(Duration position) async {
    try {
      if (_videoController != null) {
        await _videoController!.seekTo(position);
      }
    } catch (e) {
      Logger.error('Error seeking to position', error: e);
    }
  }

  /// 快進5秒
  Future<void> seekForward() async {
    if (_videoController == null) return;

    final currentPosition = _videoController!.value.position;
    final newPosition = currentPosition + const Duration(seconds: 5);
    final maxPosition = _videoController!.value.duration;

    await seekTo(newPosition > maxPosition ? maxPosition : newPosition);
  }

  /// 快退5秒
  Future<void> seekBackward() async {
    if (_videoController == null) return;

    final currentPosition = _videoController!.value.position;
    final newPosition = currentPosition - const Duration(seconds: 5);

    await seekTo(newPosition < Duration.zero ? Duration.zero : newPosition);
  }

  /// 設置音量
  Future<void> setVolume(double volume) async {
    try {
      final clampedVolume = volume.clamp(0.0, 1.0);
      if (_videoController != null) {
        await _videoController!.setVolume(clampedVolume);
      }
      _playerState.value = _playerState.value.copyWith(volume: clampedVolume);
    } catch (e) {
      Logger.error('Error setting volume', error: e);
    }
  }

  /// 設置播放速度
  Future<void> setPlaybackSpeed(double speed) async {
    try {
      final clampedSpeed = speed.clamp(0.25, 2.0);
      if (_videoController != null) {
        await _videoController!.setPlaybackSpeed(clampedSpeed);
      }
      _playerState.value = _playerState.value.copyWith(speed: clampedSpeed);
    } catch (e) {
      Logger.error('Error setting playback speed', error: e);
    }
  }

  /// 切換播放模式
  void togglePlayMode() {
    final currentMode = _playerState.value.playMode;
    final nextMode = currentMode.next;
    _playerState.value = _playerState.value.copyWith(playMode: nextMode);
  }

  /// 切換全屏模式
  Future<void> toggleFullscreen() async {
    final isFullscreen = _playerState.value.displayMode.isFullscreen;

    if (isFullscreen) {
      // 退出全屏
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: SystemUiOverlay.values,
      );
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      _playerState.value = _playerState.value.copyWith(
        displayMode: app_state.VideoDisplayMode.normal,
      );
    } else {
      // 進入全屏
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      _playerState.value = _playerState.value.copyWith(
        displayMode: app_state.VideoDisplayMode.fullscreen,
      );
    }
  }

  /// 切換畫中畫模式
  Future<void> togglePictureInPicture() async {
    // TODO: 實現畫中畫功能
    // 需要使用 pip_view 或類似的包
    Logger.debug('Picture in picture mode not implemented yet');
  }

  /// 顯示/隱藏控制器
  void toggleControls() {
    final showControls = !_playerState.value.showControls;
    _playerState.value = _playerState.value.copyWith(
      showControls: showControls,
    );

    if (showControls) {
      _startControlsTimer();
    } else {
      _cancelControlsTimer();
    }
  }

  /// 播放上一個視頻
  Future<void> playPrevious() async {
    if (currentPlaylist.isEmpty) return;

    int newIndex = currentIndex - 1;
    if (newIndex < 0) {
      newIndex = currentPlaylist.length - 1;
    }

    _currentIndex.value = newIndex;
    await playVideo(currentPlaylist[newIndex]);
  }

  /// 播放下一個視頻
  Future<void> playNext() async {
    if (currentPlaylist.isEmpty) return;

    int newIndex = currentIndex + 1;
    if (newIndex >= currentPlaylist.length) {
      newIndex = 0;
    }

    _currentIndex.value = newIndex;
    await playVideo(currentPlaylist[newIndex]);
  }

  /// 視頻狀態變化監聽
  void _onVideoStateChanged() {
    if (_videoController == null) return;

    final value = _videoController!.value;
    app_state.VideoPlayerState newState;

    if (value.hasError) {
      newState = app_state.VideoPlayerState.error;
      _playerState.value = _playerState.value.copyWith(
        playerState: newState,
        errorMessage: value.errorDescription,
      );
    } else if (value.isBuffering) {
      newState = app_state.VideoPlayerState.buffering;
      _playerState.value = _playerState.value.copyWith(playerState: newState);
    } else if (value.isPlaying) {
      newState = app_state.VideoPlayerState.playing;
      _playerState.value = _playerState.value.copyWith(playerState: newState);
      _startControlsTimer();
    } else {
      newState = app_state.VideoPlayerState.paused;
      _playerState.value = _playerState.value.copyWith(playerState: newState);
      _cancelControlsTimer();
    }
  }

  /// 開始位置監聽
  void _startPositionListener() {
    _positionSubscription?.cancel();
    _positionSubscription = Stream.periodic(const Duration(milliseconds: 500))
        .listen((_) {
          if (_videoController != null &&
              _videoController!.value.isInitialized) {
            _playerState.value = _playerState.value.copyWith(
              currentPosition: _videoController!.value.position.inMilliseconds,
            );
          }
        });
  }

  /// 開始控制器隱藏定時器
  void _startControlsTimer() {
    _cancelControlsTimer();
    _controlsTimer = Timer(const Duration(seconds: 3), () {
      if (_playerState.value.playerState.isPlaying) {
        _playerState.value = _playerState.value.copyWith(showControls: false);
      }
    });
  }

  /// 取消控制器定時器
  void _cancelControlsTimer() {
    _controlsTimer?.cancel();
    _controlsTimer = null;
  }

  /// 釋放視頻控制器
  Future<void> _disposeController() async {
    if (_videoController != null) {
      await _videoController!.dispose();
      _videoController = null;
    }
  }

  /// 添加到最近播放列表
  void _addToRecentPlaylist(VideoFile videoFile) {
    try {
      if (_playlistService != null) {
        _playlistService!.addToRecent(videoFile);
      }
    } catch (e) {
      Logger.error('Error adding to recent playlist', error: e);
    }
  }

  /// 清理資源
  void _dispose() {
    _positionSubscription?.cancel();
    _cancelControlsTimer();
    _disposeController();
  }
}
