import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import '../models/music_folder.dart';
import '../models/audio_file.dart';
import '../utils/logger.dart';
import '../utils/file_utils.dart';
import '../utils/error_utils.dart';
import 'storage_service.dart';
import 'audio_player_service.dart';
import 'package:get/get.dart';

/// 音樂文件夾管理服務
/// 負責文件夾的創建、刪除、重命名等操作
class MusicFolderService {
  static const String _foldersFileName = 'music_folders.json';
  final StorageService _storageService;
  final Uuid _uuid = const Uuid();

  MusicFolderService(this._storageService);

  /// 獲取文件夾配置文件路徑
  String get _foldersFilePath {
    return path.join(_storageService.appDocumentsDir.path, _foldersFileName);
  }

  /// 載入所有文件夾
  Future<List<MusicFolder>> loadFolders() async {
    try {
      final file = File(_foldersFilePath);

      if (!file.existsSync()) {
        // 創建默認文件夾
        final defaultFolders = await _createDefaultFolders();
        await _saveFolders(defaultFolders);
        return defaultFolders;
      }

      final jsonString = await file.readAsString();
      final jsonList = json.decode(jsonString) as List;

      return jsonList
          .map((json) => MusicFolder.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      Logger.error('Failed to load music folders: $e');
      return await _createDefaultFolders();
    }
  }

  /// 保存文件夾列表
  Future<void> _saveFolders(List<MusicFolder> folders) async {
    try {
      final file = File(_foldersFilePath);

      final jsonList = folders.map((folder) => folder.toJson()).toList();
      final jsonString = json.encode(jsonList);

      await file.writeAsString(jsonString);
    } catch (e) {
      Logger.error('Failed to save music folders: $e');
      throw Exception('無法保存文件夾配置');
    }
  }

  /// 創建默認文件夾
  Future<List<MusicFolder>> _createDefaultFolders() async {
    final musicDir = _storageService.musicDir;
    final folders = <MusicFolder>[];

    // 根文件夾
    folders.add(
      MusicFolder.createSystemFolder(
        id: 'root',
        name: '所有音樂',
        path: musicDir.path,
        description: '所有音樂文件',
        color: 0xFF9C27B0,
        icon: 'library_music',
      ),
    );

    // 最近添加
    folders.add(
      MusicFolder.createSystemFolder(
        id: 'recent',
        name: '最近添加',
        path: musicDir.path,
        description: '最近添加的音樂',
        color: 0xFF4CAF50,
        icon: 'schedule',
      ),
    );

    // 我的最愛
    folders.add(
      MusicFolder.createSystemFolder(
        id: 'favorites',
        name: '我的最愛',
        path: musicDir.path,
        description: '收藏的音樂',
        color: 0xFFFF5722,
        icon: 'favorite',
      ),
    );

    // 流行音樂
    folders.add(
      MusicFolder.createSystemFolder(
        id: 'pop',
        name: '流行音樂',
        path: musicDir.path,
        description: '流行音樂分類',
        color: 0xFF2196F3,
        icon: 'trending_up',
      ),
    );

    return folders;
  }

  /// 創建新文件夾
  Future<MusicFolder> createFolder({
    required String name,
    String? parentId,
    String? description,
    int? color,
    String? icon,
  }) async {
    try {
      // 生成唯一ID
      final id = _uuid.v4();

      // 確定父文件夾路徑
      String parentPath;
      if (parentId != null) {
        final folders = await loadFolders();
        final parentFolder = folders.firstWhere(
          (f) => f.id == parentId,
          orElse: () => throw Exception('父文件夾不存在'),
        );
        parentPath = parentFolder.path;
      } else {
        parentPath = _storageService.musicDir.path;
      }

      // 創建文件夾路徑
      final folderPath = path.join(parentPath, name);
      final directory = Directory(folderPath);

      // 檢查文件夾是否已存在
      if (directory.existsSync()) {
        throw Exception('文件夾已存在');
      }

      // 創建物理文件夾
      await directory.create(recursive: true);

      // 創建文件夾對象
      final folder = MusicFolder.createUserFolder(
        id: id,
        name: name,
        path: folderPath,
        parentId: parentId,
        description: description,
        color: color,
        icon: icon,
      );

      // 保存到配置文件
      final folders = await loadFolders();
      folders.add(folder);
      await _saveFolders(folders);

      Logger.info('Created music folder: $name at $folderPath');
      return folder;
    } catch (e) {
      Logger.error('Failed to create music folder: $e');
      throw Exception('創建文件夾失敗: $e');
    }
  }

  /// 重命名文件夾
  Future<MusicFolder> renameFolder(String folderId, String newName) async {
    try {
      final folders = await loadFolders();
      final folderIndex = folders.indexWhere((f) => f.id == folderId);

      if (folderIndex == -1) {
        throw Exception('文件夾不存在');
      }

      final folder = folders[folderIndex];

      if (folder.isSystem) {
        throw Exception('無法重命名系統文件夾');
      }

      // 計算新路徑
      final parentPath = path.dirname(folder.path);
      final newPath = path.join(parentPath, newName);

      // 檢查新路徑是否已存在
      if (Directory(newPath).existsSync()) {
        throw Exception('文件夾名稱已存在');
      }

      // 重命名物理文件夾
      await folder.directory.rename(newPath);

      // 更新文件夾對象
      final updatedFolder = folder.copyWith(
        name: newName,
        path: newPath,
        updatedAt: DateTime.now(),
      );

      folders[folderIndex] = updatedFolder;
      await _saveFolders(folders);

      Logger.info('Renamed music folder: ${folder.name} -> $newName');
      return updatedFolder;
    } catch (e) {
      Logger.error('Failed to rename music folder: $e');
      throw Exception('重命名文件夾失敗: $e');
    }
  }

  /// 刪除文件夾
  Future<bool> deleteFolder(String folderId, {bool deleteFiles = false}) async {
    try {
      final folders = await loadFolders();
      final folder = folders.firstWhere(
        (f) => f.id == folderId,
        orElse: () => throw Exception('文件夾不存在'),
      );

      if (folder.isSystem) {
        throw Exception('無法刪除系統文件夾');
      }

      // 檢查是否有子文件夾
      final hasChildren = folders.any((f) => f.parentId == folderId);
      if (hasChildren) {
        throw Exception('文件夾包含子文件夾，無法刪除');
      }

      // 檢查文件夾是否為空
      final directory = folder.directory;
      if (directory.existsSync()) {
        final contents = directory.listSync();
        if (contents.isNotEmpty && !deleteFiles) {
          throw Exception('文件夾不為空，無法刪除');
        }

        // 刪除物理文件夾
        await directory.delete(recursive: deleteFiles);
      }

      // 從配置中移除
      folders.removeWhere((f) => f.id == folderId);
      await _saveFolders(folders);

      Logger.info('Deleted music folder: ${folder.name}');
      return true;
    } catch (e) {
      Logger.error('Failed to delete music folder: $e');
      throw Exception('刪除文件夾失敗: $e');
    }
  }

  /// 移動文件夾
  Future<MusicFolder> moveFolder(String folderId, String? newParentId) async {
    try {
      final folders = await loadFolders();
      final folderIndex = folders.indexWhere((f) => f.id == folderId);

      if (folderIndex == -1) {
        throw Exception('文件夾不存在');
      }

      final folder = folders[folderIndex];

      if (folder.isSystem) {
        throw Exception('無法移動系統文件夾');
      }

      // 確定新父文件夾路徑
      String newParentPath;
      if (newParentId != null) {
        final parentFolder = folders.firstWhere(
          (f) => f.id == newParentId,
          orElse: () => throw Exception('目標文件夾不存在'),
        );
        newParentPath = parentFolder.path;
      } else {
        newParentPath = _storageService.musicDir.path;
      }

      // 計算新路徑
      final newPath = path.join(newParentPath, folder.name);

      // 檢查新路徑是否已存在
      if (Directory(newPath).existsSync()) {
        throw Exception('目標位置已存在同名文件夾');
      }

      // 移動物理文件夾
      await folder.directory.rename(newPath);

      // 更新文件夾對象
      final updatedFolder = folder.copyWith(
        path: newPath,
        parentId: newParentId,
        updatedAt: DateTime.now(),
      );

      folders[folderIndex] = updatedFolder;
      await _saveFolders(folders);

      Logger.info('Moved music folder: ${folder.name} to $newPath');
      return updatedFolder;
    } catch (e) {
      Logger.error('Failed to move music folder: $e');
      throw Exception('移動文件夾失敗: $e');
    }
  }

  /// 獲取文件夾中的音樂文件
  Future<List<AudioFile>> getFolderMusic(String folderId) async {
    try {
      final folders = await loadFolders();
      final folder = folders.firstWhere(
        (f) => f.id == folderId,
        orElse: () => throw Exception('文件夾不存在'),
      );

      if (!folder.directory.existsSync()) {
        return [];
      }

      final audioFiles = <AudioFile>[];
      final contents = folder.directory.listSync();

      for (final entity in contents) {
        if (entity is File && _isMusicFile(entity.path)) {
          audioFiles.add(AudioFile.fromFile(entity));
        }
      }

      // 按文件名排序
      audioFiles.sort((a, b) => a.fileName.compareTo(b.fileName));

      return audioFiles;
    } catch (e) {
      Logger.error('Failed to get folder music: $e');
      return [];
    }
  }

  /// 檢查是否為音樂文件
  bool _isMusicFile(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    const musicExtensions = [
      '.mp3',
      '.wav',
      '.flac',
      '.aac',
      '.ogg',
      '.m4a',
      '.wma',
    ];
    return musicExtensions.contains(extension);
  }

  /// 移動音樂文件到文件夾
  Future<bool> moveMusicToFolder(
    AudioFile audioFile,
    String targetFolderId,
  ) async {
    return await ErrorUtils.safeExecute<bool>(
          () async {
            final folders = await loadFolders();
            final targetFolder = folders.firstWhere(
              (f) => f.id == targetFolderId,
              orElse: () => throw Exception('目標文件夾不存在'),
            );

            final targetPath = path.join(targetFolder.path, audioFile.fileName);

            // 檢查是否正在播放該文件，如果是則先停止
            try {
              final audioPlayerService = Get.find<AudioPlayerService>();
              if (audioPlayerService.currentAudio?.path == audioFile.path) {
                await audioPlayerService.stop();
                Logger.info(
                  'Stopped playing ${audioFile.fileName} before moving',
                );
              }
            } catch (e) {
              Logger.warning('Could not check/stop audio player: $e');
            }

            // 使用FileUtils進行安全移動
            final success = await FileUtils.moveFile(
              audioFile.path,
              targetPath,
            );
            if (success) {
              Logger.info(
                'Moved music: ${audioFile.fileName} to ${targetFolder.name}',
              );
              ErrorUtils.showSuccess('移動成功', '音樂文件已移動到: ${targetFolder.name}');
            }
            return success;
          },
          context: '移動音樂文件',
          defaultValue: false,
        ) ??
        false;
  }
}
