import 'dart:io';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';

import '../utils/logger.dart';

class StorageService extends GetxService {
  late Directory _appDocumentsDir;
  late Directory _musicDir;
  late Directory _videoDir;
  late Directory _pdfDir;

  Directory get appDocumentsDir => _appDocumentsDir;
  Directory get musicDir => _musicDir;
  Directory get videoDir => _videoDir;
  Directory get pdfDir => _pdfDir;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeDirectories();
  }

  Future<StorageService> init() async {
    await _initializeDirectories();
    return this;
  }

  Future<void> _initializeDirectories() async {
    try {
      // 記錄平台信息
      Logger.info(
        'Platform: ${Platform.operatingSystem} ${Platform.operatingSystemVersion}',
      );
      Logger.info('Is iOS: ${Platform.isIOS}');

      // 獲取應用文檔目錄
      _appDocumentsDir = await getApplicationDocumentsDirectory();
      Logger.info('App documents directory: ${_appDocumentsDir.path}');

      // iOS特定檢查
      if (Platform.isIOS) {
        Logger.info('Running on iOS - performing additional checks');
        await _performIOSSpecificChecks();

        // iOS特定初始化完成
        Logger.info('iOS specific initialization completed');
      }

      // 檢查應用文檔目錄權限
      await _checkDirectoryPermissions(_appDocumentsDir);

      // 創建媒體文件夾
      _musicDir = Directory('${_appDocumentsDir.path}/music');
      _videoDir = Directory('${_appDocumentsDir.path}/video');
      _pdfDir = Directory('${_appDocumentsDir.path}/pdf');

      // 確保文件夾存在
      await _createDirectoryIfNotExists(_musicDir);
      await _createDirectoryIfNotExists(_videoDir);
      await _createDirectoryIfNotExists(_pdfDir);

      Logger.info('Storage Service initialized successfully');
      Logger.debug('Music directory: ${_musicDir.path}');
      Logger.debug('Video directory: ${_videoDir.path}');
      Logger.debug('PDF directory: ${_pdfDir.path}');

      // 驗證所有目錄都可以寫入
      await _verifyDirectoryWriteAccess();
    } catch (e, stackTrace) {
      Logger.error(
        'Failed to initialize storage directories',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  Future<void> _createDirectoryIfNotExists(Directory directory) async {
    try {
      if (!await directory.exists()) {
        Logger.info('Creating directory: ${directory.path}');
        await directory.create(recursive: true);
        Logger.info('Directory created successfully: ${directory.path}');
      } else {
        Logger.debug('Directory already exists: ${directory.path}');
      }

      // 驗證目錄創建成功
      if (!await directory.exists()) {
        throw Exception('Failed to create directory: ${directory.path}');
      }
    } catch (e) {
      Logger.error('Error creating directory ${directory.path}', error: e);
      rethrow;
    }
  }

  /// 檢查目錄權限
  Future<void> _checkDirectoryPermissions(Directory directory) async {
    try {
      // 檢查目錄是否存在
      final exists = await directory.exists();
      Logger.debug('Directory exists: $exists - ${directory.path}');

      if (!exists) {
        Logger.warning('Directory does not exist: ${directory.path}');
        return;
      }

      // 嘗試列出目錄內容來檢查讀取權限
      try {
        final contents = await directory.list().toList();
        Logger.debug('Directory readable, contains ${contents.length} items');
      } catch (e) {
        Logger.error('Directory not readable: ${directory.path}', error: e);
        throw Exception('No read permission for directory: ${directory.path}');
      }

      // 嘗試創建測試文件來檢查寫入權限
      try {
        final testFile = File('${directory.path}/.test_write_permission');
        await testFile.writeAsString('test');
        await testFile.delete();
        Logger.debug('Directory writable: ${directory.path}');
      } catch (e) {
        Logger.error('Directory not writable: ${directory.path}', error: e);
        throw Exception('No write permission for directory: ${directory.path}');
      }
    } catch (e) {
      Logger.error('Permission check failed for ${directory.path}', error: e);
      rethrow;
    }
  }

  /// 驗證所有目錄的寫入權限
  Future<void> _verifyDirectoryWriteAccess() async {
    final directories = [_musicDir, _videoDir, _pdfDir];

    for (final dir in directories) {
      try {
        await _checkDirectoryPermissions(dir);
        Logger.info('✅ Directory verified: ${dir.path}');
      } catch (e) {
        Logger.error('❌ Directory verification failed: ${dir.path}', error: e);
        throw Exception('Directory verification failed for ${dir.path}: $e');
      }
    }

    Logger.info('All directories verified successfully');
  }

  /// iOS特定檢查
  Future<void> _performIOSSpecificChecks() async {
    try {
      // 檢查應用沙盒路徑
      final documentsPath = _appDocumentsDir.path;
      Logger.info('iOS Documents path: $documentsPath');

      // 檢查路徑是否包含應用ID
      if (documentsPath.contains('/Documents')) {
        Logger.info('✅ Standard iOS Documents directory detected');
      } else {
        Logger.warning('⚠️ Unusual iOS Documents directory path');
      }

      // 檢查可用空間
      try {
        final stat = await _appDocumentsDir.stat();
        Logger.info('Documents directory type: ${stat.type}');
        Logger.info('Documents directory modified: ${stat.modified}');
      } catch (e) {
        Logger.warning('Could not get iOS directory stats: $e');
      }

      // 嘗試創建測試文件來驗證寫入權限
      try {
        final testFile = File('${_appDocumentsDir.path}/.ios_write_test');
        await testFile.writeAsString('iOS write test - ${DateTime.now()}');
        final content = await testFile.readAsString();
        await testFile.delete();

        Logger.info('✅ iOS write test successful');
        Logger.debug('Test content: $content');
      } catch (e) {
        Logger.error('❌ iOS write test failed', error: e);
        throw Exception('iOS write permission test failed: $e');
      }

      // 檢查目錄列表權限
      try {
        final contents = await _appDocumentsDir.list().toList();
        Logger.info(
          'iOS Documents directory contains ${contents.length} items',
        );
      } catch (e) {
        Logger.error('❌ iOS directory listing failed', error: e);
        throw Exception('iOS directory listing failed: $e');
      }
    } catch (e, stackTrace) {
      Logger.error(
        'iOS specific checks failed',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // 獲取音樂文件列表
  Future<List<File>> getMusicFiles() async {
    return await _getFilesWithExtensions(_musicDir, [
      '.mp3',
      '.m4a',
      '.aac',
      '.wav',
      '.flac',
      '.ogg',
    ]);
  }

  // 獲取視頻文件列表
  Future<List<File>> getVideoFiles() async {
    return await _getFilesWithExtensions(_videoDir, [
      '.mp4',
      '.avi',
      '.mov',
      '.mkv',
      '.wmv',
      '.flv',
      '.webm',
    ]);
  }

  // 獲取PDF文件列表
  Future<List<File>> getPdfFiles() async {
    return await _getFilesWithExtensions(_pdfDir, ['.pdf']);
  }

  Future<List<File>> _getFilesWithExtensions(
    Directory directory,
    List<String> extensions,
  ) async {
    if (!await directory.exists()) {
      return [];
    }

    final files = <File>[];
    await for (final entity in directory.list()) {
      if (entity is File) {
        final extension = entity.path.toLowerCase().substring(
          entity.path.lastIndexOf('.'),
        );
        if (extensions.contains(extension)) {
          files.add(entity);
        }
      }
    }

    return files;
  }

  // 刪除文件
  Future<bool> deleteFile(File file) async {
    try {
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      Logger.error('Error deleting file', error: e);
      return false;
    }
  }

  // 移動文件到指定目錄
  Future<File?> moveFileToDirectory(
    File sourceFile,
    Directory targetDirectory,
  ) async {
    try {
      final fileName = sourceFile.path.split('/').last;
      final targetPath = '${targetDirectory.path}/$fileName';

      // 如果目標文件已存在，添加時間戳
      var finalTargetPath = targetPath;
      var counter = 1;
      while (await File(finalTargetPath).exists()) {
        final nameWithoutExtension = fileName.substring(
          0,
          fileName.lastIndexOf('.'),
        );
        final extension = fileName.substring(fileName.lastIndexOf('.'));
        finalTargetPath =
            '${targetDirectory.path}/${nameWithoutExtension}_$counter$extension';
        counter++;
      }

      return await sourceFile.copy(finalTargetPath);
    } catch (e) {
      Logger.error('Error moving file', error: e);
      return null;
    }
  }
}
