import 'dart:io';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:shelf/shelf.dart' as shelf;
import 'package:shelf/shelf_io.dart' as shelf_io;
import 'package:shelf_router/shelf_router.dart';
import 'package:shelf_multipart/multipart.dart';
import 'package:flutter/services.dart';
import '../utils/logger.dart';
import 'storage_service.dart';

class HttpServerService extends GetxService {
  HttpServer? _server;
  final _isRunning = false.obs;
  final _serverAddress = ''.obs;
  final _router = Router();

  bool get isRunning => _isRunning.value;
  String get serverAddress => _serverAddress.value;

  @override
  void onInit() {
    super.onInit();
    _setupRoutes();
    _detectLocalAddress();
  }

  void _detectLocalAddress() {
    try {
      NetworkInterface.list().then((interfaces) {
        for (var interface in interfaces) {
          for (var addr in interface.addresses) {
            if (addr.type == InternetAddressType.IPv4 &&
                !addr.isLoopback &&
                interface.name == 'en2') {
              _serverAddress.value = addr.address;
              Logger.info('Detected local IP: ${addr.address}');
              break;
            }
          }
          if (_serverAddress.value.isNotEmpty) break;
        }
      });
    } catch (e) {
      Logger.error('Failed to detect local address: $e');
      _serverAddress.value = '127.0.0.1';
    }
  }

  void _setupRoutes() {
    // 主頁路由
    _router.get('/', (shelf.Request request) async {
      return await _serveHtmlPage(request, 'index.html');
    });

    // 文件上傳頁面路由
    _router.get('/upload.html', (shelf.Request request) async {
      return await _serveHtmlPage(request, 'upload.html');
    });

    // PDF轉換頁面路由
    _router.get('/pdf-converter.html', (shelf.Request request) async {
      return await _serveHtmlPage(request, 'pdf-converter.html');
    });

    // API路由
    _router.post('/api/upload/<type>', _handleFileUpload);
    _router.get('/api/files/<type>', _getFileList);
    _router.delete('/api/files/<type>/<filename>', _deleteFile);
  }

  Future<bool> startServer({int port = 8080}) async {
    try {
      if (_isRunning.value) {
        Logger.warning('Server is already running');
        return true;
      }

      // 創建支持大文件上傳的服務器
      _server = await shelf_io.serve(
        _router.call,
        InternetAddress.anyIPv4,
        port,
        // 增加請求體大小限制 (10GB)
        poweredByHeader: null,
      );

      // 設置服務器超時
      _server!.idleTimeout = const Duration(hours: 2);

      _isRunning.value = true;

      Logger.info('HTTP server started on port $port');
      Logger.info('Upload page: http://${_serverAddress.value}:$port');
      Logger.info('Server configured for large file uploads (up to 10GB)');

      return true;
    } catch (e) {
      Logger.error('Failed to start HTTP server: $e');
      return false;
    }
  }

  Future<void> stopServer() async {
    try {
      await _server?.close();
      _server = null;
      _isRunning.value = false;
      Logger.info('HTTP server stopped');
    } catch (e) {
      Logger.error('Failed to stop HTTP server: $e');
    }
  }

  Future<shelf.Response> _serveHtmlPage(
    shelf.Request request,
    String fileName,
  ) async {
    final htmlContent = await _getHtmlContent(fileName);
    return shelf.Response.ok(
      htmlContent,
      headers: {
        'content-type': 'text/html; charset=utf-8',
        'cache-control': 'no-cache',
      },
    );
  }

  Future<String> _getHtmlContent(String fileName) async {
    try {
      // 從 assets 讀取 HTML 文件
      final htmlContent = await rootBundle.loadString('assets/html/$fileName');
      Logger.info('Successfully loaded $fileName from assets');
      return htmlContent;
    } catch (e) {
      Logger.error('Failed to load $fileName from assets: $e');
      return _getFallbackHtml();
    }
  }

  String _getFallbackHtml() {
    return '''
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ManLeBox - 文件上傳系統</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans CJK TC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
        }
        .title {
            font-size: 2em;
            color: #667eea;
            margin-bottom: 20px;
        }
        .message {
            color: #666;
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .upload-form {
            margin-top: 20px;
        }
        .file-input {
            margin: 20px 0;
            padding: 10px;
            border: 2px dashed #667eea;
            border-radius: 8px;
            background: #f8fafc;
        }
        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">📱 ManLeBox</div>
        <div class="message">
            簡化版文件上傳系統<br>
            (完整版本需要 assets/html/index.html 文件)
        </div>
        <form class="upload-form" action="/api/upload/music" method="post" enctype="multipart/form-data">
            <div class="file-input">
                <input type="file" name="file" multiple accept="*/*">
            </div>
            <button type="submit" class="upload-btn">上傳文件</button>
        </form>
    </div>
</body>
</html>''';
  }

  Future<shelf.Response> _handleFileUpload(
    shelf.Request request,
    String type,
  ) async {
    try {
      Logger.info('Starting file upload for type: $type');

      final storageService = Get.find<StorageService>();
      Directory targetDir;

      switch (type.toLowerCase()) {
        case 'music':
          targetDir = storageService.musicDir;
          break;
        case 'video':
          targetDir = storageService.videoDir;
          break;
        case 'pdf':
          targetDir = storageService.pdfDir;
          break;
        default:
          return shelf.Response.badRequest(
            body: jsonEncode({'success': false, 'error': 'Invalid file type'}),
            headers: {'content-type': 'application/json; charset=utf-8'},
          );
      }

      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      // 使用 shelf_multipart 正確處理 multipart 請求
      final uploadedFiles = <String>[];
      int totalBytes = 0;

      if (request.isMultipart) {
        await for (final part in request.parts) {
          final contentDisposition = part.headers['content-disposition'];
          if (contentDisposition != null &&
              contentDisposition.contains('name="file"')) {
            // 提取文件名
            final filenameRegex = RegExp(r'filename\*?="?([^";\r\n]*)"?');
            final match = filenameRegex.firstMatch(contentDisposition);
            String? filename = match?.group(1);

            if (filename == null || filename.isEmpty) continue;

            // 處理中文文件名編碼
            if (filename.contains('%')) {
              try {
                filename = Uri.decodeComponent(filename);
              } catch (e) {
                Logger.warning('Failed to decode filename: $filename');
              }
            }

            final filePath = '${targetDir.path}/$filename';
            final file = File(filePath);

            Logger.info('Uploading file: $filename to $filePath');

            // 流式寫入文件
            final sink = file.openWrite();
            int fileBytes = 0;

            try {
              await for (final chunk in part) {
                sink.add(chunk);
                fileBytes += chunk.length;
                totalBytes += chunk.length;

                // 每1MB記錄一次進度
                if (fileBytes % (1024 * 1024) == 0) {
                  Logger.info(
                    'Upload progress: $filename - ${(fileBytes / (1024 * 1024)).toStringAsFixed(1)} MB',
                  );
                }
              }
            } finally {
              await sink.close();
            }

            uploadedFiles.add(filename!);
            Logger.info(
              'Successfully uploaded: $filename (${(fileBytes / (1024 * 1024)).toStringAsFixed(2)} MB)',
            );
          }
        }
      } else {
        return shelf.Response.badRequest(
          body: jsonEncode({
            'success': false,
            'error': 'Not a multipart request',
          }),
          headers: {'content-type': 'application/json; charset=utf-8'},
        );
      }

      if (uploadedFiles.isEmpty) {
        return shelf.Response.badRequest(
          body: jsonEncode({'success': false, 'error': 'No files uploaded'}),
          headers: {'content-type': 'application/json; charset=utf-8'},
        );
      }

      return shelf.Response.ok(
        jsonEncode({
          'success': true,
          'message': 'Files uploaded successfully',
          'files': uploadedFiles,
          'totalSize': totalBytes,
          'count': uploadedFiles.length,
        }),
        headers: {'content-type': 'application/json; charset=utf-8'},
      );
    } catch (e, stackTrace) {
      Logger.error('Upload error: $e');
      Logger.error('Stack trace: $stackTrace');
      return shelf.Response.internalServerError(
        body: jsonEncode({
          'success': false,
          'error': 'Upload failed: ${e.toString()}',
        }),
        headers: {'content-type': 'application/json; charset=utf-8'},
      );
    }
  }

  Future<shelf.Response> _getFileList(
    shelf.Request request,
    String type,
  ) async {
    try {
      final storageService = Get.find<StorageService>();
      Directory targetDir;

      switch (type.toLowerCase()) {
        case 'music':
          targetDir = storageService.musicDir;
          break;
        case 'video':
          targetDir = storageService.videoDir;
          break;
        case 'pdf':
          targetDir = storageService.pdfDir;
          break;
        default:
          return shelf.Response.badRequest(
            body: jsonEncode({'success': false, 'error': 'Invalid file type'}),
            headers: {'content-type': 'application/json; charset=utf-8'},
          );
      }

      if (!await targetDir.exists()) {
        return shelf.Response.ok(
          jsonEncode({'success': true, 'files': []}),
          headers: {'content-type': 'application/json; charset=utf-8'},
        );
      }

      final files = <Map<String, dynamic>>[];
      await for (final entity in targetDir.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          files.add({
            'name': entity.uri.pathSegments.last,
            'size': stat.size,
            'modified': stat.modified.toIso8601String(),
          });
        }
      }

      return shelf.Response.ok(
        jsonEncode({'success': true, 'files': files}),
        headers: {'content-type': 'application/json; charset=utf-8'},
      );
    } catch (e) {
      Logger.error('Get file list error: $e');
      return shelf.Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'content-type': 'application/json; charset=utf-8'},
      );
    }
  }

  Future<shelf.Response> _deleteFile(
    shelf.Request request,
    String type,
    String filename,
  ) async {
    try {
      final storageService = Get.find<StorageService>();
      Directory targetDir;

      switch (type.toLowerCase()) {
        case 'music':
          targetDir = storageService.musicDir;
          break;
        case 'video':
          targetDir = storageService.videoDir;
          break;
        case 'pdf':
          targetDir = storageService.pdfDir;
          break;
        default:
          return shelf.Response.badRequest(
            body: jsonEncode({'success': false, 'error': 'Invalid file type'}),
            headers: {'content-type': 'application/json; charset=utf-8'},
          );
      }

      final decodedFilename = Uri.decodeComponent(filename);
      final file = File('${targetDir.path}/$decodedFilename');

      if (await file.exists()) {
        await file.delete();
        Logger.info('Deleted file: $decodedFilename');
        return shelf.Response.ok(
          jsonEncode({'success': true, 'message': 'File deleted successfully'}),
          headers: {'content-type': 'application/json; charset=utf-8'},
        );
      } else {
        return shelf.Response.notFound(
          jsonEncode({'success': false, 'error': 'File not found'}),
          headers: {'content-type': 'application/json; charset=utf-8'},
        );
      }
    } catch (e) {
      Logger.error('Delete file error: $e');
      return shelf.Response.internalServerError(
        body: jsonEncode({'success': false, 'error': e.toString()}),
        headers: {'content-type': 'application/json; charset=utf-8'},
      );
    }
  }

  // 添加缺失的getter方法以兼容測試
  String? get serverUrl {
    if (!_isRunning.value || _server == null) return null;
    return 'http://${_serverAddress.value}:${_server!.port}';
  }

  int get serverPort => _server?.port ?? 0;

  Rx<String> get serverAddressRx => _serverAddress;

  void setManualIpAddress(String? ipAddress) {
    if (ipAddress == null || ipAddress.isEmpty) {
      _detectLocalAddress();
    } else {
      _serverAddress.value = ipAddress;
      Logger.info('Manual IP address set: $ipAddress');
    }
  }
}
