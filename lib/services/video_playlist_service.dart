import 'dart:convert';
import 'dart:io';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import '../models/video_playlist.dart';
import '../models/video_file.dart';
import '../utils/logger.dart';

/// 視頻播放列表管理服務
/// 負責視頻播放列表的創建、編輯、刪除和持久化存儲
class VideoPlaylistService extends GetxService {
  // 響應式播放列表
  final _playlists = <VideoPlaylist>[].obs;
  final _isLoading = false.obs;

  // 文件路徑
  late String _playlistsFilePath;

  // Getters
  List<VideoPlaylist> get playlists => _playlists.toList();
  bool get isLoading => _isLoading.value;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeService();
  }

  /// 初始化服務
  Future<void> _initializeService() async {
    try {
      // 獲取應用文檔目錄
      final appDir = await getApplicationDocumentsDirectory();
      _playlistsFilePath = '${appDir.path}/video_playlists.json';

      // 載入播放列表
      await loadPlaylists();

      // 創建系統預設播放列表
      await _createSystemPlaylists();

      Logger.info('Video playlist service initialized');
    } catch (e) {
      Logger.error('Error initializing video playlist service', error: e);
    }
  }

  /// 載入播放列表
  Future<void> loadPlaylists() async {
    try {
      _isLoading.value = true;

      final file = File(_playlistsFilePath);
      if (file.existsSync()) {
        final jsonString = await file.readAsString();
        final jsonList = jsonDecode(jsonString) as List<dynamic>;

        final playlists = jsonList
            .map((json) => VideoPlaylist.fromJson(json as Map<String, dynamic>))
            .toList();

        _playlists.value = playlists;
      }
    } catch (e) {
      Logger.error('Error loading video playlists', error: e);
    } finally {
      _isLoading.value = false;
    }
  }

  /// 保存播放列表
  Future<void> savePlaylists() async {
    try {
      final file = File(_playlistsFilePath);
      final jsonList = _playlists.map((playlist) => playlist.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      await file.writeAsString(jsonString);
    } catch (e) {
      Logger.error('Error saving video playlists', error: e);
    }
  }

  /// 創建播放列表
  Future<VideoPlaylist> createPlaylist({
    required String name,
    String? description,
    String? coverImagePath,
    List<VideoFile>? videoFiles,
    VideoPlaylistMode? playMode,
  }) async {
    final playlist = VideoPlaylist.create(
      name: name,
      description: description,
      coverImagePath: coverImagePath,
      videoFiles: videoFiles,
      playMode: playMode,
    );

    _playlists.add(playlist);
    await savePlaylists();

    return playlist;
  }

  /// 更新播放列表
  Future<bool> updatePlaylist(VideoPlaylist updatedPlaylist) async {
    try {
      final index = _playlists.indexWhere((p) => p.id == updatedPlaylist.id);
      if (index >= 0) {
        _playlists[index] = updatedPlaylist.copyWith(updatedAt: DateTime.now());
        await savePlaylists();
        return true;
      }
      return false;
    } catch (e) {
      Logger.error('Error updating video playlist', error: e);
      return false;
    }
  }

  /// 刪除播放列表
  Future<bool> deletePlaylist(String playlistId) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist?.isSystem == true) {
        // 不能刪除系統播放列表
        return false;
      }

      _playlists.removeWhere((p) => p.id == playlistId);
      await savePlaylists();
      return true;
    } catch (e) {
      Logger.error('Error deleting video playlist', error: e);
      return false;
    }
  }

  /// 根據ID獲取播放列表
  VideoPlaylist? getPlaylistById(String id) {
    try {
      return _playlists.firstWhere((p) => p.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 根據名稱獲取播放列表
  VideoPlaylist? getPlaylistByName(String name) {
    try {
      return _playlists.firstWhere((p) => p.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 添加視頻到播放列表
  Future<bool> addVideoToPlaylist(
    String playlistId,
    VideoFile videoFile,
  ) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.addVideoFile(videoFile);
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error adding video to playlist', error: e);
      return false;
    }
  }

  /// 添加多個視頻到播放列表
  Future<bool> addVideosToPlaylist(
    String playlistId,
    List<VideoFile> videoFiles,
  ) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.addVideoFiles(videoFiles);
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error adding videos to playlist', error: e);
      return false;
    }
  }

  /// 從播放列表移除視頻
  Future<bool> removeVideoFromPlaylist(
    String playlistId,
    VideoFile videoFile,
  ) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.removeVideoFile(videoFile);
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error removing video from playlist', error: e);
      return false;
    }
  }

  /// 移動播放列表中的視頻
  Future<bool> moveVideoInPlaylist(
    String playlistId,
    int oldIndex,
    int newIndex,
  ) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.moveVideoFile(oldIndex, newIndex);
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error moving video in playlist', error: e);
      return false;
    }
  }

  /// 清空播放列表
  Future<bool> clearPlaylist(String playlistId) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.clear();
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error clearing playlist', error: e);
      return false;
    }
  }

  /// 打亂播放列表
  Future<bool> shufflePlaylist(String playlistId) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        final updatedPlaylist = playlist.shuffle();
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error shuffling playlist', error: e);
      return false;
    }
  }

  /// 排序播放列表
  Future<bool> sortPlaylist(
    String playlistId,
    VideoPlaylistSortType sortType, {
    bool ascending = true,
  }) async {
    try {
      final playlist = getPlaylistById(playlistId);
      if (playlist != null) {
        VideoPlaylist updatedPlaylist;
        switch (sortType) {
          case VideoPlaylistSortType.title:
            updatedPlaylist = playlist.sortByTitle(ascending: ascending);
            break;
          case VideoPlaylistSortType.fileSize:
            updatedPlaylist = playlist.sortByFileSize(ascending: ascending);
            break;
          case VideoPlaylistSortType.duration:
            updatedPlaylist = playlist.sortByDuration(ascending: ascending);
            break;
          case VideoPlaylistSortType.createdAt:
            updatedPlaylist = playlist.sortByCreatedAt(ascending: ascending);
            break;
        }
        return await updatePlaylist(updatedPlaylist);
      }
      return false;
    } catch (e) {
      Logger.error('Error sorting playlist', error: e);
      return false;
    }
  }

  /// 複製播放列表
  Future<VideoPlaylist?> duplicatePlaylist(
    String playlistId,
    String newName,
  ) async {
    try {
      final originalPlaylist = getPlaylistById(playlistId);
      if (originalPlaylist != null) {
        return await createPlaylist(
          name: newName,
          description: originalPlaylist.description,
          videoFiles: originalPlaylist.videoFiles,
          playMode: originalPlaylist.playMode,
        );
      }
      return null;
    } catch (e) {
      Logger.error('Error duplicating playlist', error: e);
      return null;
    }
  }

  /// 搜索播放列表
  List<VideoPlaylist> searchPlaylists(String query) {
    if (query.isEmpty) return playlists;

    final lowerQuery = query.toLowerCase();
    return playlists.where((playlist) {
      return playlist.name.toLowerCase().contains(lowerQuery) ||
          (playlist.description?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  /// 獲取包含指定視頻的播放列表
  List<VideoPlaylist> getPlaylistsContaining(VideoFile videoFile) {
    return playlists.where((playlist) => playlist.contains(videoFile)).toList();
  }

  /// 創建系統預設播放列表
  Future<void> _createSystemPlaylists() async {
    // 檢查是否已存在系統播放列表
    final favoritesExists = playlists.any((p) => p.id == 'video_favorites');
    final recentExists = playlists.any((p) => p.id == 'video_recent');

    if (!favoritesExists) {
      final favorites = VideoPlaylist.system(
        id: 'video_favorites',
        name: '我的最愛',
        description: '收藏的視頻',
      );
      _playlists.add(favorites);
    }

    if (!recentExists) {
      final recent = VideoPlaylist.system(
        id: 'video_recent',
        name: '最近播放',
        description: '最近播放的視頻',
      );
      _playlists.add(recent);
    }

    await savePlaylists();
  }

  /// 獲取我的最愛播放列表
  VideoPlaylist? get favoritesPlaylist => getPlaylistById('video_favorites');

  /// 獲取最近播放列表
  VideoPlaylist? get recentPlaylist => getPlaylistById('video_recent');

  /// 添加到我的最愛
  Future<bool> addToFavorites(VideoFile videoFile) async {
    return await addVideoToPlaylist('video_favorites', videoFile);
  }

  /// 從我的最愛移除
  Future<bool> removeFromFavorites(VideoFile videoFile) async {
    return await removeVideoFromPlaylist('video_favorites', videoFile);
  }

  /// 添加到最近播放
  Future<bool> addToRecent(VideoFile videoFile) async {
    final recentPlaylist = this.recentPlaylist;
    if (recentPlaylist != null) {
      // 如果已存在，先移除再添加到開頭
      var updatedPlaylist = recentPlaylist.removeVideoFile(videoFile);
      updatedPlaylist = updatedPlaylist.addVideoFile(videoFile);

      // 限制最近播放列表最多30個視頻
      if (updatedPlaylist.videoFiles.length > 30) {
        final limitedVideoFiles = updatedPlaylist.videoFiles.take(30).toList();
        updatedPlaylist = updatedPlaylist.copyWith(
          videoFiles: limitedVideoFiles,
        );
      }

      return await updatePlaylist(updatedPlaylist);
    }
    return false;
  }

  /// 檢查視頻是否在我的最愛中
  bool isInFavorites(VideoFile videoFile) {
    return favoritesPlaylist?.contains(videoFile) ?? false;
  }
}

/// 視頻播放列表排序類型
enum VideoPlaylistSortType { title, fileSize, duration, createdAt }
