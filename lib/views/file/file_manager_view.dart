import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/file_manager_controller.dart';
import '../../models/file_item.dart';
import '../../utils/app_theme.dart';
import '../../widgets/common/loading_widget.dart';

/// 文件管理主頁面
/// 顯示文件列表和管理功能
class FileManagerView extends GetView<FileManagerController> {
  const FileManagerView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('文件管理'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refreshFiles(),
            tooltip: '刷新',
          ),
        ],
      ),
      body: _buildFileList(context),
      floatingActionButton: FloatingActionButton(
        onPressed: () => controller.scanFiles(),
        tooltip: '掃描文件',
        child: const Icon(Icons.refresh),
      ),
    );
  }

  /// 構建文件列表
  Widget _buildFileList(BuildContext context) {
    return Obx(() {
      if (controller.isLoading) {
        return const LoadingWidget(message: '載入文件中...');
      }

      if (controller.isScanning) {
        return const LoadingWidget(message: '掃描文件中...');
      }

      final files = controller.getFilteredFiles();

      if (files.isEmpty) {
        return _buildEmptyState(context);
      }

      return _buildListView(context, files);
    });
  }

  /// 構建空狀態
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open,
            size: 120,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 24),
          Text(
            '沒有找到文件',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '點擊刷新按鈕掃描文件系統',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 構建列表視圖
  Widget _buildListView(BuildContext context, List<FileItem> files) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: files.length,
      itemBuilder: (context, index) {
        final file = files[index];
        return _buildFileItem(context, file);
      },
    );
  }

  /// 構建文件項目
  Widget _buildFileItem(BuildContext context, FileItem file) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        leading: _buildFileIcon(file),
        title: Text(
          file.fileName,
          style: Theme.of(context).textTheme.titleMedium,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${file.formattedFileSize} • ${file.type.displayName}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              '修改於 ${file.formattedModifiedTime}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontSize: 11,
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (file.isFavorite)
              const Icon(Icons.favorite, color: Colors.red, size: 16),
            IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () => _showFileActionDialog(file),
              tooltip: '更多操作',
            ),
          ],
        ),
        onTap: () => controller.openFile(file),
      ),
    );
  }

  /// 構建文件圖標
  Widget _buildFileIcon(FileItem file) {
    Color iconColor;
    IconData iconData;

    switch (file.type) {
      case FileType.audio:
        iconColor = AppTheme.musicColor;
        iconData = Icons.audio_file;
        break;
      case FileType.video:
        iconColor = AppTheme.videoColor;
        iconData = Icons.video_file;
        break;
      case FileType.pdf:
        iconColor = AppTheme.pdfColor;
        iconData = Icons.picture_as_pdf;
        break;
      case FileType.image:
        iconColor = Colors.green;
        iconData = Icons.image;
        break;
      case FileType.document:
        iconColor = Colors.blue;
        iconData = Icons.description;
        break;
      case FileType.archive:
        iconColor = Colors.orange;
        iconData = Icons.archive;
        break;
      case FileType.unknown:
        iconColor = Colors.grey;
        iconData = Icons.insert_drive_file;
        break;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(iconData, size: 24, color: iconColor),
    );
  }

  /// 顯示文件操作對話框
  void _showFileActionDialog(FileItem file) {
    Get.dialog(
      AlertDialog(
        title: Text(file.fileName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.open_in_new),
              title: const Text('打開'),
              onTap: () {
                Get.back();
                controller.openFile(file);
              },
            ),
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('重命名'),
              onTap: () {
                Get.back();
                controller.showRenameDialog(file);
              },
            ),
            ListTile(
              leading: Icon(
                file.isFavorite ? Icons.favorite : Icons.favorite_border,
                color: file.isFavorite ? Colors.red : null,
              ),
              title: Text(file.isFavorite ? '取消收藏' : '添加收藏'),
              onTap: () {
                Get.back();
                controller.toggleFavorite(file);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('刪除', style: TextStyle(color: Colors.red)),
              onTap: () {
                Get.back();
                controller.showDeleteConfirmDialog(file);
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('屬性'),
              onTap: () {
                Get.back();
                controller.showFilePropertiesDialog(file);
              },
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
        ],
      ),
    );
  }
}
