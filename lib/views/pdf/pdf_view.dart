import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/pdf_controller.dart';
import '../../models/pdf_file.dart';
import '../../utils/app_theme.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/custom_button.dart';

/// PDF閱讀器主視圖
/// 顯示PDF文件列表和管理功能
class PdfView extends GetView<PdfController> {
  const PdfView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Obx(() => _buildBody(context)),
    );
  }

  /// 構建應用欄
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text('PDF閱讀器'),
      centerTitle: true,
      actions: [
        // 選擇模式切換
        Obx(
          () => controller.isSelectionMode
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 全選/取消全選
                    IconButton(
                      icon: Icon(
                        controller.selectedFiles.length ==
                                controller.pdfFiles.length
                            ? Icons.deselect
                            : Icons.select_all,
                      ),
                      onPressed: () {
                        if (controller.selectedFiles.length ==
                            controller.pdfFiles.length) {
                          controller.deselectAll();
                        } else {
                          controller.selectAll();
                        }
                      },
                      tooltip:
                          controller.selectedFiles.length ==
                              controller.pdfFiles.length
                          ? '取消全選'
                          : '全選',
                    ),

                    // 刪除選中
                    if (controller.selectedFiles.isNotEmpty)
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: controller.deleteSelectedFiles,
                        tooltip: '刪除選中',
                      ),

                    // 退出選擇模式
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: controller.exitSelectionMode,
                      tooltip: '退出選擇',
                    ),
                  ],
                )
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 刷新
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: controller.loadPdfFiles,
                      tooltip: '刷新',
                    ),

                    // 進入選擇模式
                    if (controller.pdfFiles.isNotEmpty)
                      IconButton(
                        icon: const Icon(Icons.checklist),
                        onPressed: controller.enterSelectionMode,
                        tooltip: '選擇文件',
                      ),
                  ],
                ),
        ),
      ],
    );
  }

  /// 構建主體內容
  Widget _buildBody(BuildContext context) {
    if (controller.isLoading) {
      return const LoadingWidget(message: '載入PDF文件中...');
    }

    if (controller.pdfFiles.isEmpty) {
      return _buildEmptyState(context);
    }

    return _buildPdfList(context);
  }

  /// 構建空狀態
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.picture_as_pdf,
            size: 80,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppSpacing.lg),
          Text(
            '沒有找到PDF文件',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            '請使用文件服務器上傳PDF文件',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          CustomButton(
            text: '打開文件服務器',
            onPressed: () => Get.toNamed('/server'),
            icon: Icons.cloud_upload,
          ),
        ],
      ),
    );
  }

  /// 構建PDF列表
  Widget _buildPdfList(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.sm),
      itemCount: controller.pdfFiles.length,
      itemBuilder: (context, index) {
        final pdfFile = controller.pdfFiles[index];
        return _buildPdfItem(context, pdfFile, index);
      },
    );
  }

  /// 構建PDF項目
  Widget _buildPdfItem(BuildContext context, PdfFile pdfFile, int index) {
    final isCurrentPdf = controller.isCurrentPdf(pdfFile);
    final isSelected = controller.isFileSelected(pdfFile);

    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.xs,
        vertical: AppSpacing.xs,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),

        // 選擇模式下的複選框或縮略圖
        leading: controller.isSelectionMode
            ? Checkbox(
                value: isSelected,
                onChanged: (_) => controller.toggleFileSelection(pdfFile),
                activeColor: AppTheme.pdfColor,
              )
            : Container(
                width: 80,
                height: 60,
                decoration: BoxDecoration(
                  color: isCurrentPdf
                      ? AppTheme.pdfColor.withValues(alpha: 0.2)
                      : AppTheme.pdfColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppRadius.sm),
                ),
                child: Icon(
                  isCurrentPdf
                      ? Icons.picture_as_pdf
                      : Icons.picture_as_pdf_outlined,
                  color: isCurrentPdf ? AppTheme.pdfColor : Colors.grey,
                  size: 32,
                ),
              ),

        // PDF信息
        title: Text(
          pdfFile.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: isCurrentPdf ? FontWeight.w600 : FontWeight.normal,
            color: isCurrentPdf ? AppTheme.pdfColor : null,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (pdfFile.description != null) ...[
              Text(
                pdfFile.description!,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppSpacing.xs),
            ],
            Row(
              children: [
                Text(
                  pdfFile.readProgressText,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontFamily: 'monospace',
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  pdfFile.formattedFileSize,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                if (pdfFile.author != null) ...[
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    pdfFile.author!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),

            // 閱讀進度條（如果有閱讀記錄）
            if (pdfFile.hasReadHistory) ...[
              const SizedBox(height: AppSpacing.xs),
              Row(
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: pdfFile.readProgress,
                      backgroundColor: Colors.grey.withValues(alpha: 0.3),
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        AppTheme.pdfColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    pdfFile.readProgressPercentage,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.pdfColor,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],

            // 書籤指示
            if (pdfFile.hasBookmarks) ...[
              const SizedBox(height: AppSpacing.xs),
              Row(
                children: [
                  const Icon(
                    Icons.bookmark,
                    size: 16,
                    color: AppTheme.pdfColor,
                  ),
                  const SizedBox(width: AppSpacing.xs),
                  Text(
                    '${pdfFile.bookmarks.length} 個書籤',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.pdfColor,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),

        // 打開按鈕
        trailing: controller.isSelectionMode
            ? null
            : IconButton(
                icon: Icon(
                  isCurrentPdf ? Icons.menu_book : Icons.open_in_new,
                  color: isCurrentPdf ? AppTheme.pdfColor : null,
                ),
                onPressed: () => controller.openPdf(pdfFile),
                tooltip: '打開PDF',
              ),

        // 點擊事件
        onTap: () {
          if (controller.isSelectionMode) {
            controller.toggleFileSelection(pdfFile);
          } else {
            controller.openPdf(pdfFile);
          }
        },

        // 長按進入選擇模式
        onLongPress: () {
          if (!controller.isSelectionMode) {
            controller.enterSelectionMode();
            controller.toggleFileSelection(pdfFile);
          }
        },
      ),
    );
  }
}
