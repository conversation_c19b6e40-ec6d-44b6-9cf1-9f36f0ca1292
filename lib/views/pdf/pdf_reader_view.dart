import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/pdf_controller.dart';
import '../../widgets/pdf/pdf_reader_widget.dart';
import '../../widgets/pdf/pdf_reader_controls.dart';

/// PDF閱讀器視圖
/// 提供完整的PDF閱讀體驗
class PdfReaderView extends GetView<PdfController> {
  const PdfReaderView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Obx(
        () => Stack(
          children: [
            // 全屏PDF閱讀器
            GestureDetector(
              onTap: () {
                // 點擊切換控制器顯示狀態
                controller.toggleControlsVisibility();
              },
              child: const PdfReaderWidget(),
            ),

            // 頂部應用欄（可隱藏）
            if (controller.showControls)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: _buildAppBar(context),
              ),

            // 底部控制器（可隱藏）
            if (controller.showControls)
              const Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: PdfReaderControls(),
              ),
          ],
        ),
      ),
    );
  }

  /// 構建應用欄
  Widget _buildAppBar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black.withValues(alpha: 0.8), Colors.transparent],
        ),
      ),
      child: SafeArea(
        child: AppBar(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          elevation: 0,
          title: Obx(
            () => Text(
              controller.currentPdfFile?.title ?? 'PDF閱讀器',
              style: const TextStyle(color: Colors.white),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          centerTitle: true,
          actions: [
            // 書籤按鈕
            Obx(
              () => IconButton(
                icon: Icon(
                  controller.hasCurrentBookmark
                      ? Icons.bookmark
                      : Icons.bookmark_border,
                  color: controller.hasCurrentBookmark
                      ? Colors.orange
                      : Colors.white,
                ),
                onPressed: controller.toggleBookmark,
                tooltip: '書籤',
              ),
            ),

            // 更多選項
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, color: Colors.white),
              onSelected: (value) {
                switch (value) {
                  case 'info':
                    _showFileInfo();
                    break;
                  case 'settings':
                    _showReaderSettings();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'info',
                  child: Row(
                    children: [
                      Icon(Icons.info),
                      SizedBox(width: 8),
                      Text('文件信息'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'settings',
                  child: Row(
                    children: [
                      Icon(Icons.settings),
                      SizedBox(width: 8),
                      Text('閱讀設置'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 顯示文件信息
  void _showFileInfo() {
    final file = controller.currentPdfFile;
    if (file == null) return;

    Get.dialog(
      AlertDialog(
        title: const Text('文件信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('文件名: ${file.fileName}'),
            const SizedBox(height: 8),
            Text('大小: ${_formatFileSize(file.fileSize)}'),
            const SizedBox(height: 8),
            Text('頁數: ${controller.totalPages}'),
            const SizedBox(height: 8),
            Text('當前頁: ${controller.currentPage}'),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('確定')),
        ],
      ),
    );
  }

  /// 顯示閱讀設置
  void _showReaderSettings() {
    // TODO: 實現閱讀設置對話框
    Get.snackbar('提示', '閱讀設置功能開發中...', snackPosition: SnackPosition.BOTTOM);
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
