import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/server_controller.dart';
import '../../utils/app_theme.dart';
import '../../widgets/common/custom_button.dart';

/// 文件服務器視圖
/// 提供WiFi文件上傳服務的管理界面
class ServerView extends GetView<ServerController> {
  const ServerView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('文件服務器'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshFileStatistics,
            tooltip: '刷新統計',
          ),
        ],
      ),
      body: Obx(
        () => SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 服務器狀態卡片
              _buildServerStatusCard(context),
              const SizedBox(height: AppSpacing.lg),

              // 文件統計卡片
              _buildFileStatisticsCard(context),
              const SizedBox(height: AppSpacing.lg),

              // 使用說明卡片
              _buildInstructionsCard(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建服務器狀態卡片
  Widget _buildServerStatusCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.wifi, color: controller.serverStatusColor, size: 28),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  '服務器狀態',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm,
                    vertical: AppSpacing.xs,
                  ),
                  decoration: BoxDecoration(
                    color: controller.serverStatusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppRadius.sm),
                  ),
                  child: Text(
                    controller.serverStatusText,
                    style: TextStyle(
                      color: controller.serverStatusColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),

            if (controller.isServerRunning.value) ...[
              // 服務器運行中的信息
              Container(
                padding: const EdgeInsets.all(AppSpacing.md),
                decoration: BoxDecoration(
                  color: AppTheme.successColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppRadius.md),
                  border: Border.all(
                    color: AppTheme.successColor.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.link,
                          color: AppTheme.successColor,
                          size: 20,
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        Text(
                          '服務器地址',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                color: AppTheme.successColor,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    Row(
                      children: [
                        Expanded(
                          child: Obx(
                            () => Text(
                              controller.serverUrl ?? '獲取中...',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(
                                    fontFamily: 'monospace',
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.copy),
                          onPressed: controller.copyServerUrl,
                          tooltip: '複製地址',
                        ),
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: controller.showIpAddressDialog,
                          tooltip: '手動設置IP',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppSpacing.md),
            ],

            // 控制按鈕
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: controller.isServerRunning.value ? '停止服務器' : '啟動服務器',
                    type: controller.isServerRunning.value
                        ? ButtonType.error
                        : ButtonType.success,
                    icon: controller.isServerRunning.value
                        ? Icons.stop
                        : Icons.play_arrow,
                    onPressed: controller.toggleServer,
                    fullWidth: true,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppSpacing.md),

            // 診斷按鈕
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: '服務器診斷',
                    type: ButtonType.secondary,
                    icon: Icons.network_check,
                    onPressed: controller.runServerDiagnostics,
                    fullWidth: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建文件統計卡片
  Widget _buildFileStatisticsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '文件統計',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppSpacing.md),

            Row(
              children: [
                Expanded(
                  child: _buildStatisticItem(
                    context,
                    icon: Icons.music_note,
                    label: '音樂',
                    count: controller.musicFileCount,
                    color: AppTheme.musicColor,
                  ),
                ),
                Expanded(
                  child: _buildStatisticItem(
                    context,
                    icon: Icons.video_library,
                    label: '視頻',
                    count: controller.videoFileCount,
                    color: AppTheme.videoColor,
                  ),
                ),
                Expanded(
                  child: _buildStatisticItem(
                    context,
                    icon: Icons.picture_as_pdf,
                    label: 'PDF',
                    count: controller.pdfFileCount,
                    color: AppTheme.pdfColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建統計項目
  Widget _buildStatisticItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required int count,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      margin: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.md),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: AppSpacing.sm),
          Text(
            count.toString(),
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建使用說明卡片
  Widget _buildInstructionsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: AppTheme.infoColor),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  '使用說明',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),

            _buildInstructionStep(
              context,
              step: '1',
              title: '啟動服務器',
              description: '點擊「啟動服務器」按鈕開始文件上傳服務',
            ),
            _buildInstructionStep(
              context,
              step: '2',
              title: '連接同一WiFi',
              description: '確保您的電腦和手機連接到同一個WiFi網絡',
            ),
            _buildInstructionStep(
              context,
              step: '3',
              title: '打開網頁',
              description: '在電腦瀏覽器中輸入顯示的服務器地址',
            ),
            _buildInstructionStep(
              context,
              step: '4',
              title: '上傳文件',
              description: '選擇要上傳的音樂、視頻或PDF文件',
              isLast: true,
            ),
          ],
        ),
      ),
    );
  }

  /// 構建說明步驟
  Widget _buildInstructionStep(
    BuildContext context, {
    required String step,
    required String title,
    required String description,
    bool isLast = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : AppSpacing.md),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                step,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
