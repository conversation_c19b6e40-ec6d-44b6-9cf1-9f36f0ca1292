import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart' as vp;

import '../../controllers/video_player_controller.dart' as vpc;
import '../../models/video_file.dart';
import '../../widgets/video/video_player_controls.dart';
import '../../utils/app_theme.dart';

/// 視頻播放頁面 - 類似YouTube的播放體驗
/// 豎屏：視頻在屏幕中間播放
/// 橫屏：自動全屏播放
class VideoPlayerPage extends StatefulWidget {
  final VideoFile videoFile;

  const VideoPlayerPage({super.key, required this.videoFile});

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  late vpc.VideoPlayerController controller;

  @override
  void initState() {
    super.initState();
    // 初始化視頻播放控制器
    controller = Get.put(vpc.VideoPlayerController());
    // 播放指定的視頻文件
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.playVideo(widget.videoFile);
    });
  }

  @override
  void dispose() {
    // 清理資源
    controller.dispose();
    Get.delete<vpc.VideoPlayerController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        // 根據屏幕方向決定是否全屏
        final isLandscape = orientation == Orientation.landscape;

        // 橫屏時自動進入全屏模式
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (isLandscape && !controller.isFullscreen) {
            controller.enterFullscreen();
          } else if (!isLandscape && controller.isFullscreen) {
            controller.exitFullscreen();
          }
        });

        return Scaffold(
          backgroundColor: isLandscape ? Colors.black : null,
          // 橫屏時隱藏AppBar，豎屏時顯示
          appBar: isLandscape ? null : _buildAppBar(),
          body: _buildBody(context, isLandscape),
        );
      },
    );
  }

  /// 構建應用欄
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        controller.currentVideo?.title ?? '視頻播放',
        style: const TextStyle(fontSize: 18),
      ),
      backgroundColor: AppTheme.videoColor,
      foregroundColor: Colors.white,
      elevation: 0,
    );
  }

  /// 構建主體內容
  Widget _buildBody(BuildContext context, bool isLandscape) {
    return GetBuilder<vpc.VideoPlayerController>(
      builder: (controller) {
        final videoController = controller.videoController;

        if (videoController == null || !videoController.value.isInitialized) {
          return _buildLoadingView();
        }

        if (isLandscape) {
          // 橫屏：全屏播放
          return _buildFullscreenPlayer();
        } else {
          // 豎屏：視頻在中間，下方可以添加其他內容
          return _buildPortraitPlayer();
        }
      },
    );
  }

  /// 構建加載視圖
  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppTheme.videoColor),
          SizedBox(height: 16),
          Text('正在加載視頻...', style: TextStyle(fontSize: 16)),
        ],
      ),
    );
  }

  /// 構建全屏播放器（橫屏模式）
  Widget _buildFullscreenPlayer() {
    return Container(
      color: Colors.black,
      child: SafeArea(
        child: Stack(
          children: [
            // 視頻畫面 - 填滿整個屏幕
            Positioned.fill(child: _buildVideoWidget(isFullscreen: true)),

            // 視頻控制器
            const Positioned.fill(child: VideoPlayerControls()),
          ],
        ),
      ),
    );
  }

  /// 構建豎屏播放器
  Widget _buildPortraitPlayer() {
    return Column(
      children: [
        // 視頻播放區域
        Container(
          width: double.infinity,
          height: 250, // 固定高度，類似YouTube
          color: Colors.black,
          child: Stack(
            children: [
              // 視頻畫面
              Positioned.fill(child: _buildVideoWidget(isFullscreen: false)),

              // 視頻控制器
              const Positioned.fill(child: VideoPlayerControls()),
            ],
          ),
        ),

        // 視頻信息區域
        Expanded(child: _buildVideoInfo()),
      ],
    );
  }

  /// 構建視頻組件
  Widget _buildVideoWidget({required bool isFullscreen}) {
    final videoController = controller.videoController!;

    if (isFullscreen) {
      // 全屏模式：保持比例，完整顯示視頻
      return Center(
        child: AspectRatio(
          aspectRatio: videoController.value.aspectRatio,
          child: vp.VideoPlayer(videoController),
        ),
      );
    } else {
      // 普通模式：保持原始比例，居中顯示
      return Center(
        child: AspectRatio(
          aspectRatio: videoController.value.aspectRatio,
          child: vp.VideoPlayer(videoController),
        ),
      );
    }
  }

  /// 構建視頻信息區域
  Widget _buildVideoInfo() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 視頻標題
          Text(
            controller.currentVideo?.title ?? '',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: 8),

          // 視頻詳情
          Builder(
            builder: (context) {
              final video = controller.currentVideo;
              if (video == null) return const SizedBox.shrink();

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '文件大小: ${_formatFileSize(video.fileSize)}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '文件路徑: ${video.path}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              );
            },
          ),

          const SizedBox(height: 16),

          // 播放控制按鈕
          _buildPlaybackControls(),
        ],
      ),
    );
  }

  /// 構建播放控制按鈕
  Widget _buildPlaybackControls() {
    return Row(
      children: [
        // 播放速度
        OutlinedButton.icon(
          onPressed: () => _showSpeedDialog(),
          icon: const Icon(Icons.speed, size: 18),
          label: Obx(() => Text('${controller.playbackSpeed}x')),
        ),

        const SizedBox(width: 12),

        // 全屏按鈕
        OutlinedButton.icon(
          onPressed: () => controller.toggleFullscreen(),
          icon: const Icon(Icons.fullscreen, size: 18),
          label: const Text('全屏'),
        ),
      ],
    );
  }

  /// 顯示播放速度選擇對話框
  void _showSpeedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('播放速度'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [0.5, 0.75, 1.0, 1.25, 1.5, 2.0].map((speed) {
            return ListTile(
              title: Text('${speed}x'),
              onTap: () {
                controller.setPlaybackSpeed(speed);
                Navigator.pop(context);
              },
              trailing: controller.playbackSpeed == speed
                  ? const Icon(Icons.check, color: AppTheme.videoColor)
                  : null,
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
