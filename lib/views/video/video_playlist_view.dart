import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/video_controller.dart';
import '../../models/video_playlist.dart';
import '../../utils/app_theme.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/video/video_playlist_widget.dart';

/// 視頻播放列表管理頁面
/// 顯示所有播放列表和管理功能
class VideoPlaylistView extends GetView<VideoController> {
  const VideoPlaylistView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('視頻播放列表'),
        backgroundColor: AppTheme.videoColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showCreatePlaylistDialog,
            tooltip: '創建播放列表',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.loadPlaylists,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const LoadingWidget(message: '載入播放列表中...');
        }

        final playlists = controller.playlists;

        if (playlists.isEmpty) {
          return _buildEmptyState(context);
        }

        return _buildPlaylistGrid(context, playlists);
      }),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 120,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: AppSpacing.xl),
          Text(
            '還沒有播放列表',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            '創建您的第一個播放列表來開始管理視頻',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.xl),
          CustomButton(
            text: '創建播放列表',
            onPressed: _showCreatePlaylistDialog,
            icon: Icons.add,
          ),
        ],
      ),
    );
  }

  /// 構建播放列表網格
  Widget _buildPlaylistGrid(
    BuildContext context,
    List<VideoPlaylist> playlists,
  ) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppSpacing.md),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: AppSpacing.md,
        mainAxisSpacing: AppSpacing.md,
      ),
      itemCount: playlists.length,
      itemBuilder: (context, index) {
        final playlist = playlists[index];
        return _buildPlaylistCard(context, playlist);
      },
    );
  }

  /// 構建播放列表卡片
  Widget _buildPlaylistCard(BuildContext context, VideoPlaylist playlist) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => _openPlaylist(playlist),
        borderRadius: BorderRadius.circular(AppRadius.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 封面圖片
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppTheme.videoColor.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(AppRadius.md),
                  ),
                ),
                child: playlist.coverImagePath != null
                    ? ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(AppRadius.md),
                        ),
                        child: Image.asset(
                          playlist.coverImagePath!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            playlist.isSystem
                                ? Icons.star
                                : Icons.video_library,
                            size: 48,
                            color: AppTheme.videoColor,
                          ),
                          const SizedBox(height: AppSpacing.sm),
                          Text(
                            '${playlist.videoCount}',
                            style: Theme.of(context).textTheme.headlineMedium
                                ?.copyWith(
                                  color: AppTheme.videoColor,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          Text(
                            '個視頻',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: AppTheme.videoColor),
                          ),
                        ],
                      ),
              ),
            ),

            // 播放列表信息
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.sm),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            playlist.name,
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.w600),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (!playlist.isSystem)
                          PopupMenuButton<String>(
                            icon: const Icon(Icons.more_vert, size: 20),
                            onSelected: (value) =>
                                _handlePlaylistAction(playlist, value),
                            itemBuilder: (context) => [
                              const PopupMenuItem(
                                value: 'edit',
                                child: Row(
                                  children: [
                                    Icon(Icons.edit, size: 16),
                                    SizedBox(width: 8),
                                    Text('編輯'),
                                  ],
                                ),
                              ),
                              const PopupMenuItem(
                                value: 'duplicate',
                                child: Row(
                                  children: [
                                    Icon(Icons.copy, size: 16),
                                    SizedBox(width: 8),
                                    Text('複製'),
                                  ],
                                ),
                              ),
                              const PopupMenuItem(
                                value: 'delete',
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.delete,
                                      size: 16,
                                      color: Colors.red,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      '刪除',
                                      style: TextStyle(color: Colors.red),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),

                    if (playlist.description != null) ...[
                      const SizedBox(height: AppSpacing.xs),
                      Text(
                        playlist.description!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],

                    const Spacer(),

                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(width: AppSpacing.xs),
                        Text(
                          playlist.formattedTotalDuration,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurfaceVariant,
                              ),
                        ),
                        const Spacer(),
                        if (playlist.playCount > 0) ...[
                          Icon(
                            Icons.play_circle_outline,
                            size: 14,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: AppSpacing.xs),
                          Text(
                            '${playlist.playCount}',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                                ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 顯示創建播放列表對話框
  void _showCreatePlaylistDialog() {
    Get.dialog(
      VideoPlaylistCreateDialog(
        onCreate: (name, description, playMode) {
          controller.createPlaylist(
            name,
            description: description,
            playMode: playMode,
          );
        },
      ),
    );
  }

  /// 打開播放列表
  void _openPlaylist(VideoPlaylist playlist) {
    Get.to(() => VideoPlaylistDetailView(playlist: playlist));
  }

  /// 處理播放列表操作
  void _handlePlaylistAction(VideoPlaylist playlist, String action) {
    switch (action) {
      case 'edit':
        _editPlaylist(playlist);
        break;
      case 'duplicate':
        _duplicatePlaylist(playlist);
        break;
      case 'delete':
        _deletePlaylist(playlist);
        break;
    }
  }

  /// 編輯播放列表
  void _editPlaylist(VideoPlaylist playlist) {
    // TODO: 實現編輯播放列表功能
    Get.snackbar('功能開發中', '編輯播放列表功能正在開發中');
  }

  /// 複製播放列表
  void _duplicatePlaylist(VideoPlaylist playlist) {
    Get.defaultDialog(
      title: '複製播放列表',
      content: Column(
        children: [
          Text('確定要複製播放列表 "${playlist.name}" 嗎？'),
          const SizedBox(height: AppSpacing.md),
          TextField(
            decoration: const InputDecoration(
              labelText: '新播放列表名稱',
              border: OutlineInputBorder(),
            ),
            onSubmitted: (newName) {
              if (newName.trim().isNotEmpty) {
                // TODO: 實現複製播放列表功能
                Get.back();
                Get.snackbar('功能開發中', '複製播放列表功能正在開發中');
              }
            },
          ),
        ],
      ),
      textCancel: '取消',
      textConfirm: '複製',
      onConfirm: () {
        Get.back();
        Get.snackbar('功能開發中', '複製播放列表功能正在開發中');
      },
    );
  }

  /// 刪除播放列表
  void _deletePlaylist(VideoPlaylist playlist) {
    Get.defaultDialog(
      title: '刪除播放列表',
      middleText: '確定要刪除播放列表 "${playlist.name}" 嗎？\n此操作無法撤銷。',
      textCancel: '取消',
      textConfirm: '刪除',
      confirmTextColor: Colors.red,
      onConfirm: () {
        controller.deletePlaylist(playlist.id);
        Get.back();
      },
    );
  }
}

/// 視頻播放列表詳情頁面
class VideoPlaylistDetailView extends StatelessWidget {
  final VideoPlaylist playlist;

  const VideoPlaylistDetailView({super.key, required this.playlist});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<VideoController>();

    return Scaffold(
      appBar: AppBar(
        title: Text(playlist.name),
        backgroundColor: AppTheme.videoColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.play_arrow),
            onPressed: playlist.isNotEmpty
                ? () => controller.playPlaylist(playlist)
                : null,
            tooltip: '播放全部',
          ),
          if (!playlist.isSystem)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showAddVideoDialog(context, controller),
              tooltip: '添加視頻',
            ),
        ],
      ),
      body: VideoPlaylistWidget(
        playlist: playlist,
        onPlayVideo: (videoFile) {
          final index = playlist.videoFiles.indexOf(videoFile);
          controller.playPlaylist(playlist, startIndex: index);
        },
        onRemoveVideo: !playlist.isSystem
            ? (videoFile) =>
                  controller.removeFromPlaylist(playlist.id, videoFile)
            : null,
        onMoveVideo: !playlist.isSystem
            ? (oldIndex, newIndex) => controller.moveVideoInPlaylist(
                playlist.id,
                oldIndex,
                newIndex,
              )
            : null,
        editable: !playlist.isSystem,
      ),
    );
  }

  /// 顯示添加視頻對話框
  void _showAddVideoDialog(BuildContext context, VideoController controller) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(AppRadius.lg),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('添加視頻到播放列表', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: AppSpacing.lg),
            CustomButton(
              text: '從視頻庫選擇',
              onPressed: () {
                Get.back();
                // TODO: 實現從視頻庫選擇功能
                Get.snackbar('功能開發中', '從視頻庫選擇功能正在開發中');
              },
              icon: Icons.video_library,
            ),
            const SizedBox(height: AppSpacing.md),
            CustomButton(
              text: '取消',
              onPressed: () => Get.back(),
              type: ButtonType.secondary,
            ),
          ],
        ),
      ),
    );
  }
}
