import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/video_controller.dart';
import '../../models/video_file.dart';
import '../../utils/app_theme.dart';
import '../../widgets/common/loading_widget.dart';
import 'video_player_page.dart';

/// 視頻列表視圖
/// 顯示視頻文件列表，點擊跳轉到播放頁面
class VideoView extends GetView<VideoController> {
  const VideoView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('視頻列表'),
        backgroundColor: AppTheme.videoColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.loadVideoFiles,
            tooltip: '刷新',
          ),
        ],
      ),
      body: _buildVideoList(context),
    );
  }

  /// 構建視頻列表
  Widget _buildVideoList(BuildContext context) {
    return Obx(() {
      if (controller.isLoading) {
        return const LoadingWidget(message: '正在加載視頻文件...');
      }

      if (controller.videoFiles.isEmpty) {
        return _buildEmptyView();
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: controller.videoFiles.length,
        itemBuilder: (context, index) {
          final video = controller.videoFiles[index];
          return _buildVideoItem(context, video);
        },
      );
    });
  }

  /// 構建視頻項目
  Widget _buildVideoItem(BuildContext context, VideoFile video) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppTheme.videoColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.play_circle_filled,
            color: AppTheme.videoColor,
            size: 32,
          ),
        ),
        title: Text(
          video.title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '大小: ${_formatFileSize(video.file.lengthSync())}',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
            const SizedBox(height: 2),
            Text(
              video.path,
              style: TextStyle(color: Colors.grey[500], fontSize: 10),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        trailing: const Icon(
          Icons.play_arrow,
          color: AppTheme.videoColor,
          size: 28,
        ),
        onTap: () => _playVideo(video),
      ),
    );
  }

  /// 構建空視圖
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.video_library_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            '沒有找到視頻文件',
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            '請確保視頻文件夾中有視頻文件',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: controller.loadVideoFiles,
            icon: const Icon(Icons.refresh),
            label: const Text('重新加載'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.videoColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// 播放視頻
  void _playVideo(VideoFile video) {
    Get.to(
      () => VideoPlayerPage(videoFile: video),
      transition: Transition.cupertino,
      duration: const Duration(milliseconds: 300),
    );
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
