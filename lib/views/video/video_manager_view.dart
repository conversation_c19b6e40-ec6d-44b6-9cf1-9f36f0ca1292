import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/video_controller.dart';
import '../../models/video_file.dart';
import '../../utils/app_theme.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/video/video_drag_drop_widget.dart';
import '../../widgets/video/folder_management_dialog.dart';
import 'video_player_page.dart';

/// 視頻管理視圖
/// 包含文件夾管理、多選操作、文件移動等功能
class VideoManagerView extends GetView<VideoController> {
  const VideoManagerView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Column(
        children: [
          // 文件夾導航欄
          _buildFolderNavigation(context),

          // 統計信息欄
          _buildStatsBar(context),

          // 視頻列表
          Expanded(child: _buildVideoList(context)),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  /// 構建應用欄
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: Obx(
        () => Text(
          controller.isSelectionMode
              ? '已選擇 ${controller.selectedFiles.length} 項'
              : '視頻管理',
        ),
      ),
      backgroundColor: AppTheme.videoColor,
      foregroundColor: Colors.white,
      leading: Obx(
        () => controller.isSelectionMode
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: controller.exitSelectionMode,
              )
            : IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Get.back(),
              ),
      ),
      actions: [
        Obx(
          () => controller.isSelectionMode
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: _buildSelectionActions(context),
                )
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: _buildNormalActions(context),
                ),
        ),
      ],
    );
  }

  /// 構建正常模式的操作按鈕
  List<Widget> _buildNormalActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.select_all),
        onPressed: controller.enterSelectionMode,
        tooltip: '選擇模式',
      ),
      IconButton(
        icon: const Icon(Icons.folder_open),
        onPressed: () => Get.dialog(const FolderManagementDialog()),
        tooltip: '文件夾管理',
      ),
      IconButton(
        icon: const Icon(Icons.create_new_folder),
        onPressed: () => _showCreateFolderDialog(context),
        tooltip: '新建文件夾',
      ),
      IconButton(
        icon: const Icon(Icons.refresh),
        onPressed: () => _refreshCurrentFolder(),
        tooltip: '刷新',
      ),
    ];
  }

  /// 構建選擇模式的操作按鈕
  List<Widget> _buildSelectionActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.select_all),
        onPressed: controller.selectAll,
        tooltip: '全選',
      ),
      IconButton(
        icon: const Icon(Icons.drive_file_move),
        onPressed: controller.selectedFiles.isNotEmpty
            ? () => _showMoveToFolderDialog(context)
            : null,
        tooltip: '移動',
      ),
      IconButton(
        icon: const Icon(Icons.delete),
        onPressed: controller.selectedFiles.isNotEmpty
            ? controller.deleteSelectedFiles
            : null,
        tooltip: '刪除',
      ),
    ];
  }

  /// 構建文件夾導航欄
  Widget _buildFolderNavigation(BuildContext context) {
    return Obx(() {
      if (controller.folders.isEmpty) return const SizedBox.shrink();

      return Container(
        height: 60,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).dividerColor,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(Icons.folder, color: AppTheme.videoColor, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: controller.folders.map((folder) {
                    final isSelected =
                        controller.currentFolder?.id == folder.id;
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FolderDropTarget(
                        folder: folder,
                        child: FilterChip(
                          label: Text(folder.name),
                          selected: isSelected,
                          onSelected: (_) =>
                              controller.switchToFolder(folder.id),
                          backgroundColor: isSelected
                              ? AppTheme.videoColor
                              : null,
                          selectedColor: AppTheme.videoColor,
                          labelStyle: TextStyle(
                            color: isSelected ? Colors.white : null,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 構建統計信息欄
  Widget _buildStatsBar(BuildContext context) {
    return Obx(() {
      final videoCount = controller.videoFiles.length;
      final selectedCount = controller.selectedFiles.length;
      final totalSize = controller.videoFiles.fold<int>(
        0,
        (sum, video) => sum + video.fileSize,
      );

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).dividerColor,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(Icons.video_library, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 8),
            Text(
              '$videoCount 個視頻',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            if (totalSize > 0) ...[
              const SizedBox(width: 16),
              Icon(Icons.storage, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                _formatFileSize(totalSize),
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
            if (controller.isSelectionMode && selectedCount > 0) ...[
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.videoColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '已選擇 $selectedCount 項',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.videoColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  /// 構建視頻列表
  Widget _buildVideoList(BuildContext context) {
    return Obx(() {
      if (controller.isLoading) {
        return const LoadingWidget(message: '載入視頻中...');
      }

      if (controller.videoFiles.isEmpty) {
        return _buildEmptyState(context);
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: controller.videoFiles.length,
        itemBuilder: (context, index) {
          final video = controller.videoFiles[index];
          return _buildVideoItem(context, video, index);
        },
      );
    });
  }

  /// 構建空狀態
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library_outlined,
            size: 120,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 24),
          Text(
            '此文件夾沒有視頻',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '上傳視頻文件或從其他文件夾移動視頻到這裡',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 構建視頻項目
  Widget _buildVideoItem(BuildContext context, VideoFile video, int index) {
    return Obx(() {
      final isSelected = controller.selectedFiles.contains(video);
      final isSelectionMode = controller.isSelectionMode;

      return VideoDragDropWidget(
        videoFile: video,
        child: Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: isSelectionMode
                ? Checkbox(
                    value: isSelected,
                    onChanged: (_) => controller.toggleFileSelection(video),
                    activeColor: AppTheme.videoColor,
                  )
                : Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppTheme.videoColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: video.thumbnailPath != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(video.file, fit: BoxFit.cover),
                          )
                        : const Icon(
                            Icons.play_circle_filled,
                            color: AppTheme.videoColor,
                            size: 32,
                          ),
                  ),
            title: Text(
              video.title,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  '大小: ${_formatFileSize(video.fileSize)}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                const SizedBox(height: 2),
                Text(
                  video.path,
                  style: TextStyle(color: Colors.grey[500], fontSize: 10),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            trailing: isSelectionMode
                ? null
                : PopupMenuButton<String>(
                    onSelected: (value) => _handleVideoAction(video, value),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'play',
                        child: Row(
                          children: [
                            Icon(Icons.play_arrow, size: 16),
                            SizedBox(width: 8),
                            Text('播放'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'move',
                        child: Row(
                          children: [
                            Icon(Icons.drive_file_move, size: 16),
                            SizedBox(width: 8),
                            Text('移動'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('刪除', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
            onTap: () {
              if (isSelectionMode) {
                controller.toggleFileSelection(video);
              } else {
                _playVideo(video);
              }
            },
            onLongPress: () {
              if (!isSelectionMode) {
                controller.enterSelectionMode();
                controller.toggleFileSelection(video);
              }
            },
          ),
        ),
      );
    });
  }

  /// 構建浮動操作按鈕
  Widget? _buildFloatingActionButton(BuildContext context) {
    return Obx(() {
      if (controller.isSelectionMode && controller.selectedFiles.isNotEmpty) {
        return FloatingActionButton.extended(
          onPressed: controller.playSelectedFiles,
          backgroundColor: AppTheme.videoColor,
          icon: const Icon(Icons.play_arrow, color: Colors.white),
          label: Text(
            '播放選中 (${controller.selectedFiles.length})',
            style: const TextStyle(color: Colors.white),
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 播放視頻
  void _playVideo(VideoFile video) {
    Get.to(() => VideoPlayerPage(videoFile: video));
  }

  /// 處理視頻操作
  void _handleVideoAction(VideoFile video, String action) {
    switch (action) {
      case 'play':
        _playVideo(video);
        break;
      case 'move':
        controller.enterSelectionMode();
        controller.toggleFileSelection(video);
        _showMoveToFolderDialog(Get.context!);
        break;
      case 'delete':
        controller.enterSelectionMode();
        controller.toggleFileSelection(video);
        controller.deleteSelectedFiles();
        break;
    }
  }

  /// 刷新當前文件夾
  void _refreshCurrentFolder() {
    if (controller.currentFolder != null) {
      controller.loadFolderVideos(controller.currentFolder!.id);
    } else {
      controller.loadVideoFiles();
    }
  }

  /// 顯示創建文件夾對話框
  void _showCreateFolderDialog(BuildContext context) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('創建新文件夾'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: '文件夾名稱',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: '描述（可選）',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: () {
              final name = nameController.text.trim();
              if (name.isNotEmpty) {
                controller.createFolder(
                  name: name,
                  description: descriptionController.text.trim().isEmpty
                      ? null
                      : descriptionController.text.trim(),
                );
                Get.back();
              }
            },
            child: const Text('創建'),
          ),
        ],
      ),
    );
  }

  /// 顯示移動到文件夾對話框
  void _showMoveToFolderDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: Text('移動 ${controller.selectedFiles.length} 個文件'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: controller.folders.length,
            itemBuilder: (context, index) {
              final folder = controller.folders[index];
              final isCurrentFolder = folder.id == controller.currentFolder?.id;

              return ListTile(
                leading: Icon(
                  folder.isSystem ? Icons.star : Icons.folder,
                  color: isCurrentFolder ? Colors.grey : AppTheme.videoColor,
                ),
                title: Text(
                  folder.name,
                  style: TextStyle(color: isCurrentFolder ? Colors.grey : null),
                ),
                subtitle: folder.description != null
                    ? Text(folder.description!)
                    : null,
                enabled: !isCurrentFolder,
                onTap: isCurrentFolder
                    ? null
                    : () {
                        controller.moveVideosToFolder(
                          controller.selectedFiles,
                          folder.id,
                        );
                        Get.back();
                      },
              );
            },
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
        ],
      ),
    );
  }
}
