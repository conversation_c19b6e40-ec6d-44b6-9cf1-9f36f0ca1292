import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/music_controller.dart';
import '../../models/audio_file.dart';
import '../../models/player_state.dart' as app_state;
import '../../models/player_state.dart';
import '../../utils/app_theme.dart';
import '../../widgets/music/lyric_scroll_widget.dart';

/// 網易雲風格音樂播放器頁面
class MusicPlayerPage extends StatefulWidget {
  final AudioFile audioFile;

  const MusicPlayerPage({super.key, required this.audioFile});

  @override
  State<MusicPlayerPage> createState() => _MusicPlayerPageState();
}

class _MusicPlayerPageState extends State<MusicPlayerPage>
    with TickerProviderStateMixin {
  final MusicController controller = Get.find<MusicController>();
  late AnimationController _rotationController;
  late AnimationController _backgroundController;
  bool _showLyrics = false;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 開始播放音樂
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.playAudio(widget.audioFile);
    });

    // 監聽播放狀態
    ever(controller.playerState.obs, (state) {
      if (state.playerState.isPlaying) {
        _rotationController.repeat();
      } else {
        _rotationController.stop();
      }
    });
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  /// 根據播放模式獲取對應的圖標
  IconData _getPlayModeIcon(app_state.PlayMode playMode) {
    switch (playMode) {
      case app_state.PlayMode.sequence:
        return Icons.playlist_play;
      case app_state.PlayMode.repeatOne:
        return Icons.repeat_one;
      case app_state.PlayMode.repeatAll:
        return Icons.repeat;
      case app_state.PlayMode.shuffle:
        return Icons.shuffle;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.musicColor.withValues(alpha: 0.8),
              AppTheme.musicColor.withValues(alpha: 0.3),
              Colors.black.withValues(alpha: 0.9),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 頂部導航欄
              _buildTopBar(),

              // 主要內容區域
              Expanded(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: _showLyrics ? _buildLyricsView() : _buildPlayerView(),
                ),
              ),

              // 底部控制欄
              _buildBottomControls(),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建頂部導航欄
  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(
              Icons.keyboard_arrow_down,
              color: Colors.white,
              size: 28,
            ),
            onPressed: () => Get.back(),
          ),
          Expanded(
            child: Obx(() {
              final audio = controller.currentAudio ?? widget.audioFile;
              return Column(
                children: [
                  Text(
                    audio.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    audio.artist,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              );
            }),
          ),
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  /// 構建播放器視圖
  Widget _buildPlayerView() {
    return SingleChildScrollView(
      key: const ValueKey('player'),
      child: Column(
        children: [
          const SizedBox(height: 20),

          // 專輯封面
          _buildAlbumCover(),

          const SizedBox(height: 20),

          // 歌曲信息
          _buildSongInfo(),

          const SizedBox(height: 20),

          // 進度條
          _buildProgressBar(),

          const SizedBox(height: 20),

          // 播放控制按鈕
          _buildPlayControls(),

          const SizedBox(height: 20),

          // 功能按鈕
          _buildFunctionButtons(),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// 構建歌詞視圖
  Widget _buildLyricsView() {
    return GestureDetector(
      onTap: () {
        // 點擊歌詞區域退出歌詞視圖
        setState(() {
          _showLyrics = false;
        });
      },
      child: Container(
        key: const ValueKey('lyrics'),
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          children: [
            // 歌曲信息簡化版
            Obx(() {
              final audio = controller.currentAudio ?? widget.audioFile;
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Column(
                  children: [
                    Text(
                      audio.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      audio.artist,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // 添加提示文字
                    Text(
                      '點擊任意位置退出歌詞',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.5),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              );
            }),

            // 歌詞滾動區域
            const Expanded(
              child: LyricScrollWidget(
                padding: EdgeInsets.symmetric(vertical: 20),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建專輯封面
  Widget _buildAlbumCover() {
    return Center(
      child: GestureDetector(
        onTap: () {
          // 點擊專輯封面切換歌詞顯示
          setState(() {
            _showLyrics = !_showLyrics;
          });
        },
        child: Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: AnimatedBuilder(
            animation: _rotationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationController.value * 2 * 3.14159,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        AppTheme.musicColor.withValues(alpha: 0.8),
                        AppTheme.musicColor.withValues(alpha: 0.4),
                        Colors.black.withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.music_note,
                      size: 80,
                      color: Colors.white,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 構建歌曲信息
  Widget _buildSongInfo() {
    return Obx(() {
      final audio = controller.currentAudio ?? widget.audioFile;
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        child: Column(
          children: [
            Text(
              audio.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Text(
              '${audio.artist} • ${audio.album}',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      );
    });
  }

  /// 構建進度條
  Widget _buildProgressBar() {
    return Obx(() {
      final state = controller.playerState;
      final duration = state.totalDuration;
      final position = state.currentPosition;

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Column(
          children: [
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Colors.white,
                inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                thumbColor: Colors.white,
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
              ),
              child: Slider(
                value: duration > 0
                    ? (position / duration).clamp(0.0, 1.0)
                    : 0.0,
                onChanged: (value) {
                  controller.seekTo(value);
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _formatDuration(Duration(milliseconds: position)),
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    _formatDuration(Duration(milliseconds: duration)),
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 構建播放控制按鈕
  Widget _buildPlayControls() {
    return Obx(() {
      final state = controller.playerState;
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 播放模式按鈕
          Obx(
            () => IconButton(
              icon: Icon(
                _getPlayModeIcon(controller.currentPlayMode),
                color: controller.currentPlayMode != app_state.PlayMode.sequence
                    ? AppTheme.musicColor
                    : Colors.white.withValues(alpha: 0.8),
                size: 24,
              ),
              onPressed: controller.togglePlayMode,
              tooltip: controller.currentPlayMode.displayName,
            ),
          ),

          // 上一首
          IconButton(
            icon: const Icon(
              Icons.skip_previous,
              color: Colors.white,
              size: 36,
            ),
            onPressed: controller.playPrevious,
          ),

          // 播放/暫停
          Container(
            width: 64,
            height: 64,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                state.playerState.isPlaying ? Icons.pause : Icons.play_arrow,
                color: AppTheme.musicColor,
                size: 32,
              ),
              onPressed: controller.togglePlayPause,
            ),
          ),

          // 下一首
          IconButton(
            icon: const Icon(Icons.skip_next, color: Colors.white, size: 36),
            onPressed: controller.playNext,
          ),

          // 收藏按鈕
          IconButton(
            icon: Icon(
              controller.isInFavorites(widget.audioFile)
                  ? Icons.favorite
                  : Icons.favorite_border,
              color: controller.isInFavorites(widget.audioFile)
                  ? Colors.red
                  : Colors.white.withValues(alpha: 0.8),
              size: 24,
            ),
            onPressed: () {
              if (controller.isInFavorites(widget.audioFile)) {
                controller.removeFromFavorites(widget.audioFile);
              } else {
                controller.addToFavorites(widget.audioFile);
              }
            },
          ),
        ],
      );
    });
  }

  /// 構建功能按鈕
  Widget _buildFunctionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 音量控制
        IconButton(
          icon: Icon(
            Icons.volume_up,
            color: Colors.white.withValues(alpha: 0.8),
          ),
          onPressed: () {
            // TODO: 顯示音量控制滑塊
          },
        ),

        // 播放列表
        IconButton(
          icon: Icon(
            Icons.queue_music,
            color: Colors.white.withValues(alpha: 0.8),
          ),
          onPressed: () {
            // TODO: 顯示播放列表
          },
        ),

        // 歌詞切換提示（點擊專輯封面）
        IconButton(
          icon: Icon(
            _showLyrics ? Icons.album : Icons.lyrics,
            color: Colors.white.withValues(alpha: 0.8),
          ),
          onPressed: () {
            setState(() {
              _showLyrics = !_showLyrics;
            });
          },
        ),

        // 更多選項
        IconButton(
          icon: Icon(
            Icons.more_horiz,
            color: Colors.white.withValues(alpha: 0.8),
          ),
          onPressed: () {
            // TODO: 顯示更多選項
          },
        ),
      ],
    );
  }

  /// 構建底部控制欄
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            icon: Icon(
              Icons.timer_outlined,
              color: Colors.white.withValues(alpha: 0.8),
            ),
            onPressed: () {},
          ),
          IconButton(
            icon: Icon(
              Icons.location_on_outlined,
              color: Colors.white.withValues(alpha: 0.8),
            ),
            onPressed: () {},
          ),
          IconButton(
            icon: Icon(
              Icons.queue_music,
              color: Colors.white.withValues(alpha: 0.8),
            ),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  /// 格式化時長
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
