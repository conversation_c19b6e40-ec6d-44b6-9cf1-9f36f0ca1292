import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/music_controller.dart';
import '../../models/audio_file.dart';
import '../../utils/app_theme.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/music/player_controls.dart';

/// 音樂播放器主視圖
/// 顯示音樂文件列表和播放控制器
class MusicView extends GetView<MusicController> {
  const MusicView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Obx(
        () => Column(
          children: [
            // 音樂列表
            Expanded(child: _buildMusicList(context)),

            // 播放控制器
            const PlayerControls(),
          ],
        ),
      ),
    );
  }

  /// 構建應用欄
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text('音樂播放器'),
      centerTitle: true,
      actions: [
        // 選擇模式切換
        Obx(
          () => controller.isSelectionMode
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 全選/取消全選
                    IconButton(
                      icon: Icon(
                        controller.selectedFiles.length ==
                                controller.audioFiles.length
                            ? Icons.deselect
                            : Icons.select_all,
                      ),
                      onPressed: () {
                        if (controller.selectedFiles.length ==
                            controller.audioFiles.length) {
                          controller.deselectAll();
                        } else {
                          controller.selectAll();
                        }
                      },
                      tooltip:
                          controller.selectedFiles.length ==
                              controller.audioFiles.length
                          ? '取消全選'
                          : '全選',
                    ),

                    // 播放選中
                    if (controller.selectedFiles.isNotEmpty)
                      IconButton(
                        icon: const Icon(Icons.play_arrow),
                        onPressed: controller.playSelectedFiles,
                        tooltip: '播放選中',
                      ),

                    // 刪除選中
                    if (controller.selectedFiles.isNotEmpty)
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: controller.deleteSelectedFiles,
                        tooltip: '刪除選中',
                      ),

                    // 退出選擇模式
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: controller.exitSelectionMode,
                      tooltip: '退出選擇',
                    ),
                  ],
                )
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 刷新
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: controller.loadAudioFiles,
                      tooltip: '刷新',
                    ),

                    // 進入選擇模式
                    if (controller.audioFiles.isNotEmpty)
                      IconButton(
                        icon: const Icon(Icons.checklist),
                        onPressed: controller.enterSelectionMode,
                        tooltip: '選擇文件',
                      ),
                  ],
                ),
        ),
      ],
    );
  }

  /// 構建音樂列表
  Widget _buildMusicList(BuildContext context) {
    if (controller.isLoading) {
      return const LoadingWidget(message: '載入音樂文件中...');
    }

    if (controller.audioFiles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.music_note,
              size: 80,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              '沒有找到音樂文件',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              '請使用文件服務器上傳音樂文件',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppSpacing.lg),
            CustomButton(
              text: '打開文件服務器',
              onPressed: () => Get.toNamed('/server'),
              icon: Icons.cloud_upload,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.sm),
      itemCount: controller.audioFiles.length,
      itemBuilder: (context, index) {
        final audioFile = controller.audioFiles[index];
        return _buildMusicItem(context, audioFile, index);
      },
    );
  }

  /// 構建音樂項目
  Widget _buildMusicItem(BuildContext context, AudioFile audioFile, int index) {
    final isCurrentAudio = controller.isCurrentAudio(audioFile);
    final isPlaying = controller.isCurrentlyPlaying(audioFile);
    final isSelected = controller.isFileSelected(audioFile);

    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.xs,
        vertical: AppSpacing.xs,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),

        // 選擇模式下的複選框或專輯封面
        leading: controller.isSelectionMode
            ? Checkbox(
                value: isSelected,
                onChanged: (_) => controller.toggleFileSelection(audioFile),
                activeColor: AppTheme.musicColor,
              )
            : Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: isCurrentAudio
                      ? AppTheme.musicColor.withValues(alpha: 0.2)
                      : AppTheme.musicColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppRadius.sm),
                ),
                child: Icon(
                  isPlaying ? Icons.equalizer : Icons.music_note,
                  color: isCurrentAudio ? AppTheme.musicColor : Colors.grey,
                  size: isPlaying ? 28 : 24,
                ),
              ),

        // 歌曲信息
        title: Text(
          audioFile.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: isCurrentAudio ? FontWeight.w600 : FontWeight.normal,
            color: isCurrentAudio ? AppTheme.musicColor : null,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${audioFile.artist} • ${audioFile.album}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: AppSpacing.xs),
            Row(
              children: [
                Text(
                  audioFile.formattedDuration,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontFamily: 'monospace',
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  audioFile.formattedFileSize,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ),

        // 播放按鈕
        trailing: controller.isSelectionMode
            ? null
            : IconButton(
                icon: Icon(
                  isPlaying ? Icons.pause : Icons.play_arrow,
                  color: isCurrentAudio ? AppTheme.musicColor : null,
                ),
                onPressed: () {
                  if (isCurrentAudio && isPlaying) {
                    controller.togglePlayPause();
                  } else {
                    controller.playAudio(audioFile);
                  }
                },
              ),

        // 點擊事件
        onTap: () {
          if (controller.isSelectionMode) {
            controller.toggleFileSelection(audioFile);
          } else {
            controller.playAudio(audioFile);
          }
        },

        // 長按進入選擇模式
        onLongPress: () {
          if (!controller.isSelectionMode) {
            controller.enterSelectionMode();
            controller.toggleFileSelection(audioFile);
          }
        },
      ),
    );
  }
}
