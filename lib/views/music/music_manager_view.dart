import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/music_controller.dart';
import '../../models/audio_file.dart';
import '../../utils/app_theme.dart';
import '../../widgets/common/loading_widget.dart';

import '../../widgets/music/music_drag_drop_widget.dart';
import '../../widgets/music/music_folder_management_dialog.dart';
import 'music_player_page.dart';

/// 音樂管理視圖
/// 包含文件夾管理、多選操作、文件移動等功能
class MusicManagerView extends GetView<MusicController> {
  const MusicManagerView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Column(
        children: [
          // 文件夾導航欄
          _buildFolderNavigation(context),

          // 統計信息欄
          _buildStatsBar(context),

          // 音樂列表
          Expanded(child: _buildMusicList(context)),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  /// 構建應用欄
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: Obx(
        () => Text(
          controller.isSelectionMode
              ? '已選擇 ${controller.selectedFiles.length} 項'
              : '音樂管理',
        ),
      ),
      backgroundColor: AppTheme.musicColor,
      foregroundColor: Colors.white,
      leading: Obx(
        () => controller.isSelectionMode
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: controller.exitSelectionMode,
              )
            : IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Get.back(),
              ),
      ),
      actions: [
        Obx(
          () => controller.isSelectionMode
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: _buildSelectionActions(context),
                )
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: _buildNormalActions(context),
                ),
        ),
      ],
    );
  }

  /// 構建正常模式的操作按鈕
  List<Widget> _buildNormalActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.select_all),
        onPressed: controller.enterSelectionMode,
        tooltip: '選擇模式',
      ),
      IconButton(
        icon: const Icon(Icons.folder_open),
        onPressed: () => Get.dialog(const MusicFolderManagementDialog()),
        tooltip: '文件夾管理',
      ),
      IconButton(
        icon: const Icon(Icons.create_new_folder),
        onPressed: () => _showCreateFolderDialog(context),
        tooltip: '新建文件夾',
      ),
      IconButton(
        icon: const Icon(Icons.refresh),
        onPressed: () => _refreshCurrentFolder(),
        tooltip: '刷新',
      ),
    ];
  }

  /// 構建選擇模式的操作按鈕
  List<Widget> _buildSelectionActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.select_all),
        onPressed: controller.selectAll,
        tooltip: '全選',
      ),
      IconButton(
        icon: const Icon(Icons.drive_file_move),
        onPressed: controller.selectedFiles.isNotEmpty
            ? () => _showMoveToFolderDialog(context)
            : null,
        tooltip: '移動',
      ),
      IconButton(
        icon: const Icon(Icons.delete),
        onPressed: controller.selectedFiles.isNotEmpty
            ? controller.deleteSelectedFiles
            : null,
        tooltip: '刪除',
      ),
    ];
  }

  /// 構建文件夾導航欄
  Widget _buildFolderNavigation(BuildContext context) {
    return Obx(() {
      if (controller.folders.isEmpty) return const SizedBox.shrink();

      return Container(
        height: 60,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).dividerColor,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(Icons.library_music, color: AppTheme.musicColor, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: controller.folders.map((folder) {
                    final isSelected =
                        controller.currentFolder?.id == folder.id;
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: MusicFolderDropTarget(
                        folder: folder,
                        child: FilterChip(
                          label: Text(folder.name),
                          selected: isSelected,
                          onSelected: (_) =>
                              controller.switchToFolder(folder.id),
                          backgroundColor: isSelected
                              ? AppTheme.musicColor
                              : null,
                          selectedColor: AppTheme.musicColor,
                          labelStyle: TextStyle(
                            color: isSelected ? Colors.white : null,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 構建統計信息欄
  Widget _buildStatsBar(BuildContext context) {
    return Obx(() {
      final musicCount = controller.audioFiles.length;
      final selectedCount = controller.selectedFiles.length;
      final totalSize = controller.audioFiles.fold<int>(
        0,
        (sum, audio) => sum + audio.fileSize,
      );

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).dividerColor,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(Icons.music_note, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 8),
            Text(
              '$musicCount 首歌曲',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            if (totalSize > 0) ...[
              const SizedBox(width: 16),
              Icon(Icons.storage, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                _formatFileSize(totalSize),
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
            if (controller.isSelectionMode && selectedCount > 0) ...[
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.musicColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '已選擇 $selectedCount 項',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.musicColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  /// 構建音樂列表
  Widget _buildMusicList(BuildContext context) {
    return Obx(() {
      if (controller.isLoading) {
        return const LoadingWidget(message: '載入音樂中...');
      }

      if (controller.audioFiles.isEmpty) {
        return _buildEmptyState(context);
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: controller.audioFiles.length,
        itemBuilder: (context, index) {
          final audio = controller.audioFiles[index];
          return _buildMusicItem(context, audio, index);
        },
      );
    });
  }

  /// 構建空狀態
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.library_music_outlined,
            size: 120,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 24),
          Text(
            '此文件夾沒有音樂',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '上傳音樂文件或從其他文件夾移動音樂到這裡',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 構建音樂項目
  Widget _buildMusicItem(BuildContext context, AudioFile audio, int index) {
    return Obx(() {
      final isSelected = controller.selectedFiles.contains(audio);
      final isSelectionMode = controller.isSelectionMode;
      final isCurrentAudio = controller.isCurrentAudio(audio);
      final isPlaying = controller.isCurrentlyPlaying(audio);

      return MusicDragDropWidget(
        audioFile: audio,
        child: Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: isSelectionMode
                ? Checkbox(
                    value: isSelected,
                    onChanged: (_) => controller.toggleFileSelection(audio),
                    activeColor: AppTheme.musicColor,
                  )
                : GestureDetector(
                    onTap: () {
                      // 跳轉到播放器頁面
                      Get.to(() => MusicPlayerPage(audioFile: audio));
                    },
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: AppTheme.musicColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        isPlaying
                            ? Icons.music_note
                            : Icons.music_note_outlined,
                        color: isCurrentAudio
                            ? AppTheme.musicColor
                            : Colors.grey[600],
                        size: 32,
                      ),
                    ),
                  ),
            title: Text(
              audio.title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: isCurrentAudio ? AppTheme.musicColor : null,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  '${audio.artist} • ${audio.album}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      controller.formatDuration(audio.duration),
                      style: TextStyle(color: Colors.grey[500], fontSize: 10),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatFileSize(audio.fileSize),
                      style: TextStyle(color: Colors.grey[500], fontSize: 10),
                    ),
                  ],
                ),
              ],
            ),
            trailing: isSelectionMode
                ? null
                : PopupMenuButton<String>(
                    onSelected: (value) => _handleMusicAction(audio, value),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'play',
                        child: Row(
                          children: [
                            Icon(Icons.play_arrow, size: 16),
                            SizedBox(width: 8),
                            Text('播放'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'move',
                        child: Row(
                          children: [
                            Icon(Icons.drive_file_move, size: 16),
                            SizedBox(width: 8),
                            Text('移動'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('刪除', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
            onTap: isSelectionMode
                ? () => controller.toggleFileSelection(audio)
                : null,

            onLongPress: () {
              if (!isSelectionMode) {
                controller.enterSelectionMode();
                controller.toggleFileSelection(audio);
              }
            },
          ),
        ),
      );
    });
  }

  /// 構建浮動操作按鈕
  Widget? _buildFloatingActionButton(BuildContext context) {
    return Obx(() {
      if (controller.isSelectionMode && controller.selectedFiles.isNotEmpty) {
        return FloatingActionButton.extended(
          onPressed: controller.playSelectedFiles,
          backgroundColor: AppTheme.musicColor,
          icon: const Icon(Icons.play_arrow, color: Colors.white),
          label: Text(
            '播放選中 (${controller.selectedFiles.length})',
            style: const TextStyle(color: Colors.white),
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 處理音樂操作
  void _handleMusicAction(AudioFile audio, String action) {
    switch (action) {
      case 'play':
        controller.playAudio(audio);
        break;
      case 'move':
        controller.enterSelectionMode();
        controller.toggleFileSelection(audio);
        _showMoveToFolderDialog(Get.context!);
        break;
      case 'delete':
        controller.enterSelectionMode();
        controller.toggleFileSelection(audio);
        controller.deleteSelectedFiles();
        break;
    }
  }

  /// 刷新當前文件夾
  void _refreshCurrentFolder() {
    if (controller.currentFolder != null) {
      controller.loadFolderMusic(controller.currentFolder!.id);
    } else {
      controller.loadAudioFiles();
    }
  }

  /// 顯示創建文件夾對話框
  void _showCreateFolderDialog(BuildContext context) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('創建新文件夾'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: '文件夾名稱',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: '描述（可選）',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: () {
              final name = nameController.text.trim();
              if (name.isNotEmpty) {
                controller.createFolder(
                  name: name,
                  description: descriptionController.text.trim().isEmpty
                      ? null
                      : descriptionController.text.trim(),
                );
                Get.back();
              }
            },
            child: const Text('創建'),
          ),
        ],
      ),
    );
  }

  /// 顯示移動到文件夾對話框
  void _showMoveToFolderDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: Text('移動 ${controller.selectedFiles.length} 個文件'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: controller.folders.length,
            itemBuilder: (context, index) {
              final folder = controller.folders[index];
              final isCurrentFolder = folder.id == controller.currentFolder?.id;

              return ListTile(
                leading: Icon(
                  folder.isSystem ? Icons.star : Icons.folder,
                  color: isCurrentFolder ? Colors.grey : AppTheme.musicColor,
                ),
                title: Text(
                  folder.name,
                  style: TextStyle(color: isCurrentFolder ? Colors.grey : null),
                ),
                subtitle: folder.description != null
                    ? Text(folder.description!)
                    : null,
                enabled: !isCurrentFolder,
                onTap: isCurrentFolder
                    ? null
                    : () {
                        controller.moveMusicToFolder(
                          controller.selectedFiles,
                          folder.id,
                        );
                        Get.back();
                      },
              );
            },
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
        ],
      ),
    );
  }
}
