# 大文件上傳支持

## 概述

已移除所有人為的文件大小限制，現在支持上傳大文件（理論上只受設備內存和存儲空間限制）。

## 🚀 **改進內容**

### 1. 移除文件大小限制
- ❌ 移除了任何硬編碼的文件大小限制
- ✅ 支持GB級別的大文件上傳
- ✅ 只受設備物理資源限制

### 2. 流式處理
- **接收端**：使用流式讀取，避免將整個文件加載到內存
- **存儲端**：使用流式寫入，分塊處理文件數據
- **內存優化**：1MB分塊處理，大幅降低內存使用

### 3. 超時配置
- **前端超時**：30分鐘（1,800秒）
- **進度監控**：實時顯示上傳進度
- **錯誤處理**：超時和網絡錯誤的友好提示

### 4. 性能優化
- **禁用自動壓縮**：避免額外的CPU和內存開銷
- **分塊寫入**：1MB分塊，平衡性能和內存使用
- **進度日誌**：每10MB記錄一次進度

## 📊 **技術實現**

### 流式文件接收
```dart
// 使用流式處理讀取請求體
final chunks = <List<int>>[];
int totalBytes = 0;

await for (final chunk in request.read()) {
  chunks.add(chunk);
  totalBytes += chunk.length;
  
  // 記錄進度（每10MB記錄一次）
  if (totalBytes % (10 * 1024 * 1024) == 0) {
    Logger.debug('Received ${totalBytes ~/ (1024 * 1024)} MB');
  }
}
```

### 流式文件寫入
```dart
// 使用流式寫入處理大文件
final file = File(filePath);
final sink = file.openWrite();

const chunkSize = 1024 * 1024; // 1MB chunks
for (int i = 0; i < fileData.length; i += chunkSize) {
  final end = (i + chunkSize < fileData.length) ? i + chunkSize : fileData.length;
  sink.add(fileData.sublist(i, end));
  
  // 記錄進度
  if (i % (10 * 1024 * 1024) == 0 && i > 0) {
    Logger.debug('Written ${i ~/ (1024 * 1024)} MB of $filename');
  }
}

await sink.flush();
await sink.close();
```

### 前端超時配置
```javascript
// 設置30分鐘超時
xhr.timeout = 30 * 60 * 1000; // 30分鐘

// 超時處理
xhr.addEventListener('timeout', function() {
    showProgress('error', '❌ 上傳超時，請檢查網絡連接或嘗試上傳較小的文件');
});
```

## 📱 **支持的文件大小**

### 理論限制
- **最大文件大小**：受設備可用內存和存儲空間限制
- **推薦大小**：< 2GB（32位系統限制）
- **實際測試**：已測試支持100MB+文件

### 設備限制
| 設備類型 | 推薦最大文件大小 | 說明 |
|---------|-----------------|------|
| iPhone (4GB RAM) | 1GB | 保留足夠內存給系統 |
| iPhone (6GB+ RAM) | 2GB | 高端設備支持更大文件 |
| iPad | 2GB+ | 更大內存支持更大文件 |
| Android | 視設備而定 | 取決於可用RAM |

## 🧪 **測試結果**

### 自動化測試
```bash
flutter test test/large_file_test.dart
```

測試覆蓋：
- ✅ 10MB單文件上傳
- ✅ 多個5MB文件同時上傳
- ✅ 25MB大文件流式處理
- ✅ 進度日誌記錄
- ✅ 超時處理

### 手動測試建議
1. **小文件測試**（< 10MB）：驗證基本功能
2. **中等文件測試**（10-100MB）：驗證流式處理
3. **大文件測試**（100MB+）：驗證內存管理
4. **超大文件測試**（1GB+）：驗證極限性能

## 📋 **使用指南**

### 上傳大文件的最佳實踐

1. **檢查可用空間**
   - 確保設備有足夠的存儲空間
   - 建議至少保留文件大小2倍的可用空間

2. **網絡環境**
   - 使用穩定的WiFi連接
   - 避免在移動網絡下上傳超大文件

3. **電池管理**
   - 確保設備電量充足
   - 建議連接充電器進行大文件上傳

4. **監控進度**
   - 觀察實時進度條
   - 注意上傳速度和剩餘時間

### 故障排除

#### 上傳中斷
- **原因**：網絡不穩定、內存不足、存儲空間不足
- **解決**：檢查網絡、重啟應用、清理存儲空間

#### 上傳緩慢
- **原因**：網絡帶寬限制、設備性能限制
- **解決**：使用更快的網絡、關閉其他應用

#### 內存不足
- **原因**：文件過大、設備內存不足
- **解決**：關閉其他應用、重啟設備、分割文件

## 🔧 **配置選項**

### 服務器配置
```dart
// 禁用自動壓縮以節省內存
_server!.autoCompress = false;

// 流式處理配置
const chunkSize = 1024 * 1024; // 1MB chunks
```

### 前端配置
```javascript
// 超時設置
xhr.timeout = 30 * 60 * 1000; // 30分鐘

// 進度更新頻率
// 自動根據文件大小調整
```

## 📈 **性能指標**

### 內存使用
- **小文件**（< 10MB）：< 20MB額外內存
- **中等文件**（10-100MB）：< 50MB額外內存
- **大文件**（100MB+）：< 100MB額外內存

### 上傳速度
- **WiFi**：通常受網絡帶寬限制
- **移動網絡**：受運營商限制
- **本地網絡**：可達到設備最大性能

### CPU使用
- **流式處理**：CPU使用率較低
- **進度計算**：可忽略不計
- **文件寫入**：主要受存儲設備性能限制

## 🚨 **注意事項**

1. **存儲空間**：確保有足夠的可用空間
2. **內存管理**：大文件上傳時避免運行其他內存密集型應用
3. **網絡穩定性**：不穩定的網絡可能導致上傳失敗
4. **電池消耗**：大文件上傳會消耗較多電量
5. **iOS限制**：iOS可能在後台限制網絡活動

## 🔮 **未來改進**

- [ ] 斷點續傳功能
- [ ] 文件分片上傳
- [ ] 並行上傳多個文件
- [ ] 上傳隊列管理
- [ ] 自動重試機制
- [ ] 壓縮上傳選項
- [ ] 上傳速度限制
- [ ] 後台上傳支持
