# 服務器故障排除指南

## 概述

本文檔提供了iOS應用中HTTP服務器啟動問題的故障排除指南。

## 常見問題及解決方案

### 1. 服務器啟動失敗

**症狀**: 點擊"啟動服務器"按鈕後顯示啟動失敗

**可能原因**:
- iOS本地網絡權限未授予
- 端口被占用
- 網絡接口不可用
- 應用權限不足

**解決步驟**:

1. **檢查iOS權限設置**:
   - 打開 設置 → 隱私與安全性 → 本地網絡
   - 確保您的應用已啟用本地網絡權限

2. **使用診斷工具**:
   - 在應用中點擊"服務器診斷"按鈕
   - 查看診斷報告中的詳細信息
   - 複製報告以便進一步分析

3. **重啟應用**:
   - 完全關閉應用
   - 重新啟動應用
   - 再次嘗試啟動服務器

4. **檢查網絡連接**:
   - 確保設備已連接到Wi-Fi網絡
   - 嘗試訪問其他網絡服務確認網絡正常

### 2. 服務器啟動成功但無法訪問

**症狀**: 服務器顯示已啟動，但無法通過瀏覽器訪問

**解決步驟**:

1. **檢查IP地址**:
   - 確認顯示的IP地址是否正確
   - 嘗試手動設置IP地址

2. **檢查防火牆設置**:
   - 確保路由器沒有阻止相關端口
   - 檢查設備的防火牆設置

3. **嘗試不同的端口**:
   - 應用會自動嘗試不同的端口
   - 如果8080被占用，會嘗試8081、8082等

### 3. 權限相關問題

**症狀**: 出現權限拒絕錯誤

**解決步驟**:

1. **重新安裝應用**:
   - 刪除應用
   - 重新安裝
   - 在首次啟動時授予所有權限

2. **檢查iOS版本**:
   - iOS 14及以上版本需要本地網絡權限
   - 確保系統版本支持

## 診斷工具使用

### 服務器診斷功能

應用內置了服務器診斷工具，可以幫助識別問題：

1. 點擊"服務器診斷"按鈕
2. 等待診斷完成
3. 查看診斷報告
4. 點擊"複製"按鈕複製報告

### 診斷報告內容

診斷報告包含以下信息：
- 平台信息（iOS版本等）
- 服務器狀態
- 網絡接口信息
- 端口可用性

## 手動IP設置

如果自動檢測IP地址失敗，可以手動設置：

1. 在服務器狀態卡片中點擊編輯按鈕
2. 輸入您設備的Wi-Fi IP地址
3. 通常格式為 192.168.x.x
4. 點擊確定保存設置

## 技術細節

### 支持的端口

應用會按順序嘗試以下端口：
- 8080（默認）
- 8081
- 8082
- 8083
- 8084
- 8085
- 3000
- 3001
- 4000
- 5000

### 網絡接口檢測

應用會自動檢測以下網絡接口：
- iOS: en0（Wi-Fi接口）
- 私有IP地址範圍：
  - 192.168.x.x
  - 10.x.x.x
  - 172.16-31.x.x

## 聯繫支持

如果問題仍然存在，請提供以下信息：
1. 設備型號和iOS版本
2. 服務器診斷報告
3. 錯誤截圖
4. 網絡環境描述（家用Wi-Fi、企業網絡等）

## 更新日誌

### v2.0
- 添加了服務器診斷工具
- 改進了IP地址檢測邏輯
- 增加了手動IP設置功能
- 優化了錯誤處理和用戶反饋
