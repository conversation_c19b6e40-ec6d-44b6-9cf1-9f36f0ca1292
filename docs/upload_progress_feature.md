# 文件上傳進度條功能

## 功能概述

新增了實時上傳進度條功能，為用戶提供詳細的上傳狀態信息，包括：
- 實時進度百分比（精確到小數點後一位）
- 已上傳/總文件大小
- 上傳速度（自動單位轉換）
- 預估剩餘時間
- 動畫進度條效果

## 功能特性

### 📊 **實時進度顯示**
- **進度百分比**：顯示精確的上傳進度（如 45.7%）
- **文件大小**：顯示已上傳和總文件大小（如 2.3 MB / 5.1 MB）
- **上傳速度**：實時計算並顯示上傳速度（如 1.2 MB/s）
- **剩餘時間**：智能預估剩餘上傳時間（如 3 分 25 秒）

### 🎨 **視覺效果**
- **漸變進度條**：使用漸變色彩的現代化進度條
- **動畫效果**：進度條填充時的平滑動畫
- **條紋動畫**：進度條內的移動條紋效果
- **響應式設計**：適配不同屏幕尺寸

### ⚡ **性能優化**
- **XMLHttpRequest**：使用原生 XMLHttpRequest 支持進度監控
- **實時計算**：動態計算上傳速度和剩餘時間
- **平滑更新**：進度條更新使用 CSS 過渡效果

## 技術實現

### 前端實現

#### HTML 結構
```html
<div class="progress-container">
    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>
    <div class="progress-text" id="progressText">
        <div>45.7% (2.3 MB / 5.1 MB)</div>
    </div>
    <div class="upload-speed" id="uploadSpeed">
        上傳速度: 1.2 MB/s • 剩餘時間: 3 分 25 秒
    </div>
</div>
```

#### CSS 樣式
```css
.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0; left: 0; bottom: 0; right: 0;
    background-image: linear-gradient(-45deg,
        rgba(255, 255, 255, .2) 25%, transparent 25%,
        transparent 50%, rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%, transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}
```

#### JavaScript 邏輯
```javascript
// 監聽上傳進度
xhr.upload.addEventListener('progress', function(e) {
    if (e.lengthComputable) {
        const percentComplete = (e.loaded / e.total) * 100;
        const elapsed = (Date.now() - startTime) / 1000;
        const speed = e.loaded / elapsed;
        const remaining = (e.total - e.loaded) / speed;
        
        updateProgressBar(
            percentComplete, 
            formatFileSize(e.loaded), 
            formatFileSize(e.total), 
            formatSpeed(speed), 
            formatTime(remaining)
        );
    }
});
```

### 輔助函數

#### 文件大小格式化
```javascript
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
```

#### 上傳速度格式化
```javascript
function formatSpeed(bytesPerSecond) {
    if (bytesPerSecond === 0) return '0 B/s';
    const k = 1024;
    const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
    const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));
    return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}
```

#### 時間格式化
```javascript
function formatTime(seconds) {
    if (!isFinite(seconds) || seconds < 0) return '計算中...';
    
    if (seconds < 60) {
        return Math.round(seconds) + ' 秒';
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.round(seconds % 60);
        return minutes + ' 分 ' + remainingSeconds + ' 秒';
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return hours + ' 小時 ' + minutes + ' 分';
    }
}
```

## 用戶體驗

### 上傳流程
1. **選擇文件**：用戶選擇要上傳的文件
2. **開始上傳**：點擊上傳按鈕，顯示進度條
3. **實時更新**：進度條實時更新，顯示詳細信息
4. **完成提示**：上傳完成後顯示成功消息

### 信息顯示
- **進度百分比**：45.7%
- **文件大小**：2.3 MB / 5.1 MB
- **上傳速度**：1.2 MB/s
- **剩餘時間**：3 分 25 秒

### 錯誤處理
- **網絡錯誤**：顯示網絡連接錯誤提示
- **服務器錯誤**：顯示服務器響應錯誤
- **上傳中斷**：顯示上傳中斷提示

## 瀏覽器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ iOS Safari 12+
- ✅ Android Chrome 60+

## 測試方法

### 使用內置上傳頁面
1. 啟動服務器
2. 在瀏覽器中訪問服務器地址
3. 選擇大文件進行上傳測試
4. 觀察進度條的實時更新

### 使用測試頁面
1. 打開 `test_upload.html`
2. 輸入服務器地址
3. 選擇文件並上傳
4. 查看詳細的進度信息

## 性能考慮

- **更新頻率**：進度事件觸發頻率適中，不會造成性能問題
- **內存使用**：使用流式上傳，不會將整個文件加載到內存
- **CPU 使用**：格式化函數經過優化，計算開銷很小
- **網絡效率**：使用原生 XMLHttpRequest，上傳效率最佳

## 未來改進

- [ ] 支持上傳暫停/恢復功能
- [ ] 添加多文件並行上傳進度
- [ ] 支持拖拽排序上傳隊列
- [ ] 添加上傳歷史記錄
- [ ] 支持斷點續傳功能
